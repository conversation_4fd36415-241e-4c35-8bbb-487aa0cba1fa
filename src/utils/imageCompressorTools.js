

const tools = {
    compress(options, callback) {




    }
};
var options = {
    file: file,
    quality: 0.6,
    mimeType: 'image/jpeg',
    maxWidth: 2000,
    maxHeight: 2000,
    width: 1000,
    height: 1000,
    minWidth: 500,
    minHeight: 500,
    convertSize: Infinity,
    loose: true,
    redressOrientation: true,

    // 压缩前回调
    beforeCompress: function (result) {
        console.log('压缩之前图片尺寸大小: ', result.size);
        console.log('mime 类型: ', result.type);
    },

    // 压缩成功回调
    success: function (result) {
        console.log('压缩之后图片尺寸大小: ', result.size);
        console.log('mime 类型: ', result.type);
        console.log('实际压缩率： ', ((file.size - result.size) / file.size * 100).toFixed(2) + '%');
    },

    // 发生错误
    error: function (msg) {
        console.error(msg);
    }
};

new ImageCompressor(options);