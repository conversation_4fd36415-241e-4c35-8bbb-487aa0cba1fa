import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'

// 密钥对生成 http://web.chacuo.net/netrsakeypair

// const publicKey = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ=='

const publicKey = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAN2dLr6jX0jKIzusfgXnH291tBJNwhxn2izBrEMmwiBT58av/WKJpi1UqkrwlikGnOj86oGPKot9uy0YfMDvabcCAwEAAQ=='


const privateKey = 'MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAmyTw3dIAXeZJX+DArAPcBlQnT7b/+q8jbNcpJlg9PcOmVcYpkXzf7C4/BX34/060StxMeGThoqE3C/o+CvHLEQIDAQABAkBgVrgbhmQj6vR1TSNXra3bYDZpEcIy8L6Wv6UV4oVk7Dmkm5H+WCWtOU4Q/0Vi6Yequ89bORDz5ctocoJaf3YBAiEAzaMKSk7I1ZrMhsx+Rb4S7nw9gn9gkX1tqlFPbXmGqLkCIQDBJCHWitrdi7bMIvk38kgJHUFfsZqbDAwMqiyIhbtZGQIgGUlyS37yrWmyuuTMplDgTRlUCwcU3e85nBcRmFm4WGkCIBXwGLfZxcpsWDMPtecY6f2/CPHppnn+AFBf8/b92a3hAiAX3PzOZy5lStfQ1fcP77Sc7R+xAXCEwPaai5jZxqZyTw=='
// const privateKey = 'MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE='

// 加密
export function encrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对数据进行加密
}

// 解密
export function decrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(privateKey) // 设置私钥
  return encryptor.decrypt(txt) // 对数据进行解密
}

