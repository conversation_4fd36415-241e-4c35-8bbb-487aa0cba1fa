<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="关键字" prop="keyword">
                <el-input v-model="queryParams.keyword" placeholder="昵称/手机号" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="wechatConfigList">
            <el-table-column label="昵称" align="center" prop="nickname" width="150" />
            <el-table-column label="头像" align="center">
                <template slot-scope="scope">
                    <image-preview :src="scope.row.avatar" />
                </template>
            </el-table-column>
            <!-- <el-table-column label="类型" align="center">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.sys_wechat_user_type" :value="scope.row.utype" />
                </template>
            </el-table-column> -->
            <el-table-column label="手机" align="center" prop="tel" width="120" />
            <!-- <el-table-column label="性别" align="center">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.sys_user_sex" :value="scope.row.sex" />
                </template>
            </el-table-column> -->
            <!-- <el-table-column label="城市" align="center" prop="city" :show-overflow-tooltip="true" />
            <el-table-column label="详细地址" align="center" prop="address" :show-overflow-tooltip="true" /> -->
            <!-- <el-table-column label="预产期" align="center">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.dueDate) }}</span>
                </template>
            </el-table-column> -->
            <!-- <el-table-column label="生日" align="center">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.birthDate) }}</span>
                </template>
            </el-table-column> -->
            <el-table-column label="最近登录时间" align="center" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.lastLoginTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="最近登录IP" align="center" width="180">
                <template slot-scope="scope">
                    <span>{{ scope.row.lastLoginIp }}</span>
                </template>
            </el-table-column>
            <!-- <el-table-column label="状态" align="center" prop="status"></el-table-column> -->
            <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center">
                <template slot-scope="scope">
                    <el-switch v-model="scope.row.status" :active-value="'0'" :inactive-value="'1'"
                        @change="changeWechatUserStatus(scope.row)"></el-switch>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                        v-loading.fullscreen.lock="fullscreenLoading">修改</el-button>
                    <el-button size="mini" type="text" icon="el-icon-remove"
                        @click="handleRemove(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改参数配置对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" v-loading="dialogLoading" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="昵称" prop="nickname">
                    <el-input v-model="form.nickname" placeholder="请输入昵称" />
                </el-form-item>
                <el-form-item label="头像" prop="avatar">
                    <image-upload v-model="form.avatar" :limit="1" :isShowTip="false" />
                </el-form-item>
                <el-form-item label="性别" prop="sex">
                    <el-radio-group v-model="form.sex" style="width: 100%">
                        <el-radio v-for="dict in dict.type.sys_user_sex" :key="dict.value" :label="dict.value">{{
                            dict.label
                            }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="角色" prop="roleId">
                    <el-select v-model="form.roleId" placeholder="请选择角色" clearable filterable style="width: 100%">
                        <el-option v-for="option in roleList" :key="option.id" :label="option.name"
                            :value="option.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="房间" prop="roomIds">
                    <el-select v-model="form.roomIds" placeholder="请选择房间" clearable filterable multiple
                        style="width: 100%">
                        <el-option v-for="option in roomList" :key="option.roomId" :label="option.roomNumber"
                            :value="option.roomId" />
                    </el-select>
                </el-form-item>

                <el-form-item label="入住时间" prop="checkInTime" v-if="isCustomer">
                    <el-date-picker v-model="form.checkInTime" type="date" value-format="yyyy-MM-dd"
                        placeholder="请选择宝妈入住时间" style="width: 100%">
                    </el-date-picker>
                </el-form-item>

                <el-form-item prop="tel">
                    <template slot="label">
                        <span>手机号</span>
                        <el-tooltip class="item" effect="dark" :content="'输入的手机号码将会和小程序的真实用户进行绑定。'" placement="top">
                            <i class="el-icon-question" style="color:#2c68ff" />
                        </el-tooltip>
                        <span>:</span>
                    </template>
                    <el-input v-model="form.tel" placeholder="请输入手机号" :disabled="phoneDisabled" />
                </el-form-item>
                <!-- <el-form-item label="预产期" prop="dueDate">
                    <el-date-picker v-model="form.dueDate" type="date" value-format="yyyy-MM-dd" placeholder="选择预产期"
                        style="width: 100%;">
                    </el-date-picker>
                </el-form-item> -->
                <!-- <el-form-item label="出生日期" prop="birthDate">
                    <el-date-picker v-model="form.birthDate" type="date" value-format="yyyy-MM-dd" placeholder="选择出生日期"
                        style="width: 100%;">
                    </el-date-picker>
                </el-form-item> -->
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getWechatUserPage, changeWechatUserStatus, createWechatUser, updateWechatUser, getWechatUser, getRoles, removeWechatUser } from "@/api/system/wechat";
import { getList as getRoomList } from "@/api/platform/room";
export default {
    name: "WechatUser",
    dicts: ['sys_wechat_user_type', 'sys_user_sex'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 表格数据
            wechatConfigList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                keyword: undefined,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                nickname: [
                    { required: true, message: "昵称不能为空", trigger: "blur" },
                    { min: 2, max: 10, message: "名称长度在2到10个字符", trigger: "blur" }
                ],
                sex: [
                    { required: true, message: "性别不能为空", trigger: "blur" }
                ],
                roleId: [
                    { required: true, message: "角色不能为空", trigger: "blur" }
                ]
            },
            customerOptions: [],
            staffOptions: [],
            selectVal: null,
            roleList: [],
            roomList: [],
            fullscreenLoading: false,
            dialogLoading: false,
            phoneDisabled: false
        };
    },
    created() {
        this.getList();
    },
    computed: {
        isCustomer() {
            if (!this.roleList || this.roleList.length === 0) return false;
            const role = this.roleList.find(item => item.id == this.form.roleId);
            return role && role.code === 'CUSTOMER';
        }
    },
    methods: {
        /** 查询参数列表 */
        getList() {
            this.loading = true;
            getWechatUserPage(this.queryParams).then(response => {
                this.wechatConfigList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                miniProgramId: undefined,
                tenantId: undefined,
                type: undefined,
                appid: undefined,
                secret: undefined,
                token: undefined,
                aesKey: undefined,
                issued: undefined,
                msgDataFormat: undefined,
                name: undefined
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.phoneDisabled = false;
            this.title = "添加用户";
            this.getRoles();
            this.getRoomList();
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            this.fullscreenLoading = true;
            const id = row.userId
            getWechatUser(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.fullscreenLoading = false;
                this.phoneDisabled = true;
                this.title = "修改用户";
            });
            this.getRoles();
            this.getRoomList();
        },
        /** 提交按钮 */
        submitForm: function () {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    this.dialogLoading = true;
                    if (Array.isArray(this.form.avatar)) {
                        this.form.avatar = this.form.avatar[0];
                    }

                    if (this.form.userId != undefined) {
                        updateWechatUser(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        }).finally(()=>{
                            this.dialogLoading = false;
                        });
                    } else {
                        createWechatUser(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        }).finally(()=>{
                            this.dialogLoading = false;
                        });
                    }
                }
            });
        },
        /**
         * 微信用户状态修改
         * @param {用户信息} row
         */
        changeWechatUserStatus(row) {
            let text = row.status == '0' ? "启用" : "禁用";
            this.$modal.confirm('确认要' + text + '"' + row.nickname + '"微信用户吗？').then(function () {
                return changeWechatUserStatus(row.status, row.userId);
            }).then(() => {
                this.$modal.msgSuccess(text + "成功");
            }).catch(function () {
                row.status = row.status == '1' ? false : true;
            });
        },
        getRoles() {
            getRoles().then(res => {
                this.roleList = res.data;
            });
        },
        getRoomList() {
            getRoomList().then(res => {
                this.roomList = res.data;
            });
        },
        handleRemove(row) {
            const userId = row.userId;
            this.$modal.confirm('确认要删除用户吗？').then(function () {
                return removeWechatUser(userId);
            }).then(() => {
                this.$modal.msgSuccess("删除成功");
                this.getList();
            }).catch(function () { });

        }
    }
};
</script>