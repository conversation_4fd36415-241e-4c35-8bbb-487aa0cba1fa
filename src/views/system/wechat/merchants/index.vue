<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="商家" prop="tenantId">
        <el-select v-model="queryParams.tenantId" placeholder="请选择商家" clearable filterable style="width: 240px">
          <el-option v-for="tenant in tenantOptions" :key="tenant.tenantId" :label="tenant.companyName"
            :value="tenant.tenantId" />
        </el-select>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入小程序名称" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:wechat:mini:program:add']">新增商家</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tenantMiniProgramList">
      <el-table-column label="商家名称" align="center" prop="companyName" />
      <el-table-column label="小程序名称" align="center" prop="miniProgramName" :show-overflow-tooltip="true" />
      <el-table-column label="小程序码" align="center" prop="qrCode">
        <template slot-scope="scope">
          <image-preview v-if="scope.row.qrCode != null" :src="scope.row.qrCode" />
          <span v-else>暂未生成</span>
        </template>
      </el-table-column>
      <el-table-column label="URL_LINK" align="center" prop="urlLink">
        <template slot-scope="scope">
          <span v-if="scope.row.urlLink == null">暂未生成</span>
          <el-link v-else @click="copyUrl(scope.row.urlLink)">复制链接<i class="el-icon-document-copy"></i> </el-link>
        </template>
      </el-table-column>
      <el-table-column label="URL_SCHEME" align="center" prop="urlScheme">
        <template slot-scope="scope">
          <span v-if="scope.row.urlScheme == null">暂未生成</span>
          <el-link v-else @click="copyUrl(scope.row.urlScheme)">复制链接<i class="el-icon-document-copy"></i> </el-link>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-dropdown trigger="click">
            <el-button class="el-dropdown-link" size="mini" type="text" icon="el-icon-s-tools">
              操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item icon="el-icon-plus" 
                  @click.native="generateQrCode(scope.row)"
                  v-loading.fullscreen.lock="fullscreenLoading"
                  v-hasPermi="['system:wechat:mini:program:qrCode']"
                >生成小程序码</el-dropdown-item>
              <el-dropdown-item icon="el-icon-circle-plus"
                  @click.native="generateUrlLike(scope.row)"
                  v-loading.fullscreen.lock="fullscreenLoading"
                  v-hasPermi="['system:wechat:mini:program:like']"
              >生成URL_LINK</el-dropdown-item>
              <el-dropdown-item icon="el-icon-circle-plus-outline"
                  @click.native="generateUrlScheme(scope.row)"
                  v-loading.fullscreen.lock="fullscreenLoading"
                  v-hasPermi="['system:wechat:mini:program:scheme']"
              >生成URL_SCHEME</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <!-- <el-button size="mini" type="text" icon="el-icon-s-tools" @click="generateQrCode(scope.row)"
            v-hasPermi="['system:wechat:mini:program:update']">生成小程序码</el-button> -->
          <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:wechat:mini:program:update']">生成</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      :page-sizes="[3, 5]" @pagination="getList" />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="商户" prop="tenantId">
          <el-select v-model="form.tenantId" placeholder="请选择商户" clearable style="width: 100%">
            <el-option v-for="tenant in tenantOptions" :key="tenant.tenantId" :label="tenant.companyName"
              :value="tenant.tenantId" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTenantMiniProgramPage, getWechatConfig, generateQrCode, generateUrlLink, generateUrlScheme, saveTenantMiniProgram
} from "@/api/system/wechat";
import { queryOptions } from "@/api/system/tenant";
import ImagePreview from "@/components/ImagePreview/index";

export default {
  name: "TenantMiniProgram",
  components: { ImagePreview },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      tenantMiniProgramList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 3,
        miniProgramId: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        tenantId: [
          { required: true, message: "商户不能为空", trigger: "blur" }
        ],
      },
      tenantOptions: [],
      messageFormatOptions: [
        { label: "JSON", value: "JSON" },
        { label: "XML", value: "XML" }
      ],
      miniProgramId: undefined,
      fullscreenLoading: false
    };
  },
  created() {
    this.miniProgramId = this.$route.params.miniProgramId;

    this.getList();
    this.queryTenantOptions();
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      if (this.miniProgramId == undefined || this.miniProgramId == null) {
        this.$router.push({ path: '/wechat/MiniProgram' });
      }
      this.queryParams.miniProgramId = this.miniProgramId;
      getTenantMiniProgramPage(this.queryParams).then(response => {
        this.tenantMiniProgramList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    queryTenantOptions() {
      queryOptions().then(response => {
        this.tenantOptions = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        miniProgramId: undefined,
        tenantId: undefined,
        type: undefined,
        appid: undefined,
        secret: undefined,
        token: undefined,
        aesKey: undefined,
        issued: undefined,
        msgDataFormat: undefined,
        name: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加微信小程序";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.miniProgramId
      getWechatConfig(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改微信小程序";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.open = true;
          if (this.miniProgramId == undefined || this.miniProgramId == null) {
            this.$router.push({ path: '/system/wechat' });
          }
          this.form.miniProgramId = this.miniProgramId;
          saveTenantMiniProgram(this.form).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    async generateQrCode(row) {
      this.fullscreenLoading = true;
      let tenantConfigId = row.tenantConfigId;
      generateQrCode(tenantConfigId).then(response => {
        this.fullscreenLoading = false;
        this.$modal.msgSuccess("小程序码生成成功");
        this.getList();
      }).catch();
    },
    async generateUrlLike(row) {
      this.fullscreenLoading = true;
      let tenantConfigId = row.tenantConfigId;
      generateUrlLink(tenantConfigId).then(response => {
        this.fullscreenLoading = false;
        this.$modal.msgSuccess("小程序URL_LINK生成成功");
        this.getList();
      }).catch();
    },
    //生成URL_SCHEME
    async generateUrlScheme(row) {
      this.fullscreenLoading = true;
      let tenantConfigId = row.tenantConfigId;
      generateUrlScheme(tenantConfigId).then(response => {
        this.fullscreenLoading = false;
        this.$modal.msgSuccess("小程序URL_SCHEME生成成功");
        this.getList();
      }).catch();
    },
    async copyUrl(text){
      try{
        await navigator.clipboard.writeText(text);
        this.$modal.msgSuccess("内容已复制");
      }catch(err){
        this.$modal.msgError("内容复制失败");
      }
    }
  }
};
</script>
