<template>
    <div class="app-container">
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="小程序订阅消息" name="second">

                <el-form :model="form" ref="form" label-width="150px">
                    <el-form-item label="消息模版" prop="template"
                        :rules="[{ required: true, message: '消息模版不能为空', trigger: 'blur' }]">
                        <el-radio-group v-model="form.template">
                            <el-radio v-for="dict in dict.type.miniprogram_message_template" :key="dict.value"
                                :label="dict.value">{{ dict.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <template v-if="form.template == 'IOn9fTPeoURr_o8AfB70jHQcbWr8SAn3MI1v_thi-dY'">
                        <el-form-item label="活动名称" prop="notificationData.thing1" :rules="[
            { required: true, message: '请输入活动名称', trigger: 'blur' },
            { max: 20, message: '长度最多20个字符', trigger: 'blur' }
        ]">
                            <el-input type="text" v-model="form.notificationData.thing1" autocomplete="off"></el-input>
                        </el-form-item>
                        <el-form-item label="活动时间" prop="notificationData.time2"
                            :rules="[{ required: true, message: '请选择活动时间', trigger: 'blur' }]">
                            <el-date-picker v-model="form.notificationData.time2" type="datetime"
                                value-format="yyyy-MM-dd HH:mm" placeholder="选择活动时间" style="width: 100%;">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="活动简介" prop="notificationData.thing5" :rules="[{ required: true, message: '请输入活动简介', trigger: 'blur' },
        { max: 20, message: '长度最多20个字符', trigger: 'blur' }]">
                            <el-input type="textarea" v-model="form.notificationData.thing5"
                                autocomplete="off"></el-input>
                        </el-form-item>
                    </template>

                    <el-form-item label="推送配置" prop="excludedList">
                        <el-checkbox-group v-model="form.excludedList">
                            <el-checkbox :label="dict" v-for="dict in excludedConfig || []" :key="dict" />
                        </el-checkbox-group>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="onSubmit">立即发送</el-button>
                    </el-form-item>
                </el-form>

                <el-table :data="tableData" style="width: 100%">
                    <el-table-column prop="content" label="通知内容" width="800">
                    </el-table-column>
                    <el-table-column prop="sendTime" label="发送时间" width="180">
                    </el-table-column>
                    <el-table-column prop="sendStatus" label="发送状态" width="120">
                    </el-table-column>
                    <el-table-column label="接收/发送人数" width="120">
                        <template slot-scope="scope">
                            {{ scope.row.senderCount }}/{{ scope.row.recipientCount }}
                        </template>
                    </el-table-column>
                </el-table>

            </el-tab-pane>

            <el-tab-pane label="公众号订阅通知" name="third">
                <el-form :model="form" ref="form" label-width="150px">
                    <el-form-item label="消息模版" prop="template"
                        :rules="[{ required: true, message: '消息模版不能为空', trigger: 'blur' }]">
                        <el-radio-group v-model="form.template">
                            <el-radio v-for="dict in dict.type.public_message_template" :key="dict.value"
                                :label="dict.value">{{
            dict.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <template v-if="form.template == 'dVKo7E-nNZ4fD6lArtOEyjKYn4m8wlMhfkNzoqAlV3Q'">
                        <el-form-item label="活动名称" prop="notificationData.thing1" :rules="[
            { required: true, message: '请输入活动名称', trigger: 'blur' },
            { max: 20, message: '长度最多20个字符', trigger: 'blur' }
        ]">
                            <el-input type="text" v-model="form.notificationData.thing1" autocomplete="off"></el-input>
                        </el-form-item>
                        <el-form-item label="活动状态" prop="notificationData.phrase2" :rules="[
            { required: true, message: '请输入活动状态', trigger: 'blur' },
            { max: 20, message: '长度最多20个字符', trigger: 'blur' }
        ]">
                            <el-input type="text" v-model="form.notificationData.phrase2" autocomplete="off"></el-input>
                        </el-form-item>





                        <el-form-item label="活动时间" prop="notificationData.time3"
                            :rules="[{ required: true, message: '请选择活动时间', trigger: 'blur' }]">
                            <el-date-picker v-model="form.notificationData.time3" type="datetime"
                                value-format="yyyy-MM-dd HH:mm" placeholder="选择活动时间" style="width: 100%;">
                            </el-date-picker>
                        </el-form-item>

                        <el-form-item label="活动地址" prop="notificationData.thing4" :rules="[
            { required: true, message: '请输入活动地址', trigger: 'blur' },
            { max: 20, message: '长度最多20个字符', trigger: 'blur' }
        ]">
                            <el-input type="text" v-model="form.notificationData.thing4" autocomplete="off"></el-input>
                        </el-form-item>




                        <el-form-item label="备注" prop="notificationData.thing6" :rules="[{ required: true, message: '请输入活动简介', trigger: 'blur' },
        { max: 20, message: '长度最多20个字符', trigger: 'blur' }]">
                            <el-input type="textarea" v-model="form.notificationData.thing6"
                                autocomplete="off"></el-input>
                        </el-form-item>
                    </template>

                    <el-form-item label="推送配置" prop="excludedList">
                        <el-checkbox-group v-model="form.excludedList">
                            <el-checkbox :label="dict" v-for="dict in excludedConfig || []" :key="dict" />
                        </el-checkbox-group>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="onSubmit">立即发送</el-button>
                    </el-form-item>
                </el-form>



            </el-tab-pane>

            <el-tab-pane label="公众号模版消息" name="forth">



            </el-tab-pane>
        </el-tabs>


    </div>
</template>

<script>
import { sendMessage, getNotificationPage, sendMpMessage } from "@/api/system/wechat.js";
export default {
    dicts: ["miniprogram_message_template", "public_message_template"],
    created() {
        this.$nextTick(() => {
            this.form.excludedList = ["排除工作人员", "排除老板"];
        });
        this.getList();
    },
    data() {
        return {
            activeName: 'second',
            ruleForm: {},
            form: {
                notificationData: {},
                excludedList: []
            },
            tableData: [],
            excludedConfig: ["排除工作人员", "排除老板"],
        };
    },
    methods: {
        handleClick(tab, event) {
            console.log(tab, event);
        },
        onSubmit() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    console.log(this.form)
                    if (this.activeName === 'second') {
                        sendMessage(this.form).then(response => {
                            this.$modal.msgSuccess("小程序消息推送队列已生成，稍后将推送");
                            this.getList();
                            this.form = {
                                notificationData: {},
                                excludedList: []
                            }
                        });
                    }
                    if (this.activeName === 'third') {
                        sendMpMessage(this.form).then(response => {
                            this.$modal.msgSuccess("公众号消息推送队列已生成，稍后将推送");
                            this.form = {
                                notificationData: {},
                                excludedList: []
                            }
                        });
                    }

                }
            });
        },
        getList() {
            let params = {
                type: "1"
            }
            getNotificationPage(params).then(response => {
                this.tableData = response.rows;
            });
        }
    }
};
</script>

<style scoped lang="scss">
.app-container {
    background: #ffffff;
}
</style>