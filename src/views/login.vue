<template>
  <div class="login">
    <div class="main">
      <div class="left">
        <p class="text1">小叮当 管理尽在掌握</p>
        <p class="text2">孕妈小叮当后台管理系统</p>
        <p class="text3">
          <span>专业</span>
          <span>易用</span>
          <span>高效</span>
        </p>
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
          <p class="text4">Xiao Ding Dang</p>
          <el-form-item prop="tenantId">
            <el-input v-model="loginForm.tenantId" type="text" auto-complete="off" placeholder="商户编号"
              style="border: none;" @keyup.enter.native="handleLogin">
              <svg-icon slot="prefix" icon-class="tenant" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>
          <el-form-item prop="username">
            <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号"
              @keyup.enter.native="handleLogin">
              <svg-icon slot="prefix" icon-class="user_1" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="密码"
              @keyup.enter.native="handleLogin">
              <svg-icon slot="prefix" icon-class="password_1" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>
          <el-form-item style="width:100%;">
            <el-button :loading="loading" size="medium" type="primary" style="width:100%;margin-top: 50px;"
              @click.native.prevent="handleLogin">
              <span v-if="!loading">登 录</span>
              <span v-else>登 录 中...</span>
            </el-button>
            <div style="float: right;" v-if="register">
              <router-link class="link-type" :to="'/register'">立即注册</router-link>
            </div>
          </el-form-item>
          <div class="hint">
            <p class="fgx"></p>
            <p class="hintText">加入我们 让管理变得简单又轻松</p>
            <p class="fgx"></p>
          </div>
        </el-form>
      </div>
      <div class="right">
      </div>
    </div>
    <!-- <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">若依后台管理系统</h3>
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          type="text"
          auto-complete="off"
          placeholder="账号"
        >
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          placeholder="密码"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img"/>
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width:100%;"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="float: right;" v-if="register">
          <router-link class="link-type" :to="'/register'">立即注册</router-link>
        </div>
      </el-form-item>
    </el-form> -->
    <!--  底部  -->
    <div class="el-login-footer">
      <!-- <span>Copyright © 2018-2023 ruoyi.vip All Rights Reserved.</span> -->
      <a href="https://beian.miit.gov.cn" target="_blank" style="color:black; font-size: 20px; font: bold;">Copyright ©
        2024 小叮当& Made by csjinmo & 湘ICP备2024068072号</a>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'

export default {
  name: "Login",
  data() {
    return {
      codeUrl: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        tenantId: "",
        uuid: ""
      },
      loginRules: {
        tenantId: [
          { required: true, trigger: "blur", message: "请输入商户编号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        username: [{ required: true, trigger: "blur", message: "请输入账号" }]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: false,
      redirect: undefined
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        console.log(route);
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    // this.getCode();
    this.getCookie();
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.data.img;
          this.loginForm.uuid = res.data.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            // Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            // Cookies.remove('rememberMe');
          }
          this.$store.dispatch("Login", this.loginForm).then(() => {
            this.$router.push({ path: this.redirect || "/" }).catch(() => { });
            location.reload();
          }).catch(() => {
            this.loading = false;
            if (this.captchaEnabled) {
              this.getCode();
            }
          });
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  height: 100vh;
  background: #fff;
  background-size: cover;
}

// .login {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   height: 100%;
//   // background-image: url("../assets/images/login-background.jpg");
//   background: #fff;
//   background-size: cover;
// }
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 15px 25px 5px 25px;
  text-align: center;

  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 38px;
}

.username {
  border: none;
}

.main {
  display: flex;
  justify-content: space-between;
}

.left {
  margin-left: 120px;
}

.text1 {
  color: #3F85FF;
  font-size: 18px;
  margin-top: 48px;
  font-weight: bold;
}

.text3 {
  color: #3F85FF;
  font-size: 30px;
  margin-top: 22px;
  display: flex;

  span {
    margin-right: 60px;
  }
}

.text4 {
  color: #3F85FF;
  font-size: 40px;
  font-weight: 400;
}

.text2 {
  color: #3F85FF;
  font-size: 60px;
  margin-top: 127px;
  font-weight: bold;
}

.hint {
  display: flex;
  align-items: center;
  justify-content: center;
}

.fgx {
  width: 32px;
  height: 2px;
  background: #7C7D81;
}

.hintText {
  font-size: 14px;
  color: #7C7D81;
  margin: 0 9px;
}

.right {
  background-image: url("../../image/Group 48098472@2x (1).png");
  width: 834px;
  height: 100vh;
  background-size: cover;
  text-align: right;
}
</style>
