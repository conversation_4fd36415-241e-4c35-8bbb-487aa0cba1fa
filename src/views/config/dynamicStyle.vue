<template>
  <div class="app">
    <!-- <div class="head">
          <div class="titles">话题管理</div>
          <div class="title1">Pages/宝妈社区/话题管理</div>
      </div> -->
    <div class="content">
      <div class="contentHead">
        <!-- <h2 class="title2">话题管理</h2> -->
        <!-- <div class="contentHeads">
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">人员</p>
              <el-select
                v-model="ruleForm.onlineStatus"
                placeholder="请选择人员"
                clearable
                style="width: 159px; margin-left: 10px"
              >
                <el-option
                  v-for="item in onlineStatus"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">标签</p>
              <el-input
                v-model="ruleForm.topicName"
                placeholder="请输入标签"
                clearable
              ></el-input>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">创建时间</p>
              <div style="display: flex; margin-left: 10px">
                <el-date-picker
                  v-model="ruleForm.topicStartTime"
                  type="date"
                  placeholder="创建开始时间"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <el-date-picker
                  v-model="ruleForm.topicEndTime"
                  type="date"
                  placeholder="创建结束时间"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </div>
            </div>
          </div>

          <el-button
            type="primary"
            style="margin-left: 8px; height: 36px"
            @click="inquire"
            >查询</el-button
          >
        </div> -->
        <el-button
          type="primary"
          style="height: 36px; margin: 20px 0 28px 0"
          @click="addMaternitySuite"
          >新增样式
        </el-button>
        <el-table v-loading="loading" :data="listData" class="table">
          <el-table-column
            label="样式名称"
            prop="name"
            :show-overflow-tooltip="true"
            align="center"
          />
          <el-table-column label="图片" prop="topicStartTime" align="center">
            <template slot-scope="scope">
              <img
                :src="scope.row.picture"
                min-width="70"
                height="70"
                style="width: 100px; height: 100px; border-radius: 5px"
              />
            </template>
          </el-table-column>
          <el-table-column label="说明" prop="description" align="center">
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                >替换并使用</el-button
              >
              <el-button size="mini" type="text" @click="compile(scope.row)"
                >编辑</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="block">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog title="删除" :visible.sync="deleteDialogVisible" width="30%">
      <span>是否删除此话题？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
      </span>
    </el-dialog>

    <el-dialog title="新增" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="样式名称" :label-width="formLabelWidth">
          <el-input
            v-model="form.name"
            autocomplete="off"
            placeholder="输入样式名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="图片展示" :label-width="formLabelWidth">
          <div class="upload">
            <image-upload
              :limit="1"
              :isShowTip="false"
              v-model="form.picture"
            />
            <!-- <div v-if="forbidden">
              <img
                :src="item"
                alt=""
                v-for="(item, index) in form.picture"
                :key="index"
                style="
                  width: 146px;
                  height: 146px;
                  margin-right: 10px;
                  border: 1px solid #c0ccda;
                  border-radius: 6px;
                "
              />
            </div> -->
          </div>
        </el-form-item>
        <el-form-item label="模板内容" :label-width="formLabelWidth">
          <el-input
            type="textarea"
            v-model="form.description"
            placeholder="请输入模板内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="getadd()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { page, save, delTable, online } from "@/api/platform/dynamicStyle";
export default {
  name: "app",
  data() {
    return {
      formLabelWidth: "80px",
      form: {
        name: "",
        picture: "",
        description: "",
      },
      dialogFormVisible: false,
      loading: false,
      onlineStatus: [
        //在线状态
        {
          dictLabel: "已上线",
          dictValue: "1",
        },
        {
          dictLabel: "已下线",
          dictValue: "0",
        },
      ],
      topicStatus: [
        //话题状态
        {
          dictLabel: "进行中",
          dictValue: "1",
        },
        {
          dictLabel: "待开始",
          dictValue: "0",
        },
        {
          dictLabel: "已结束",
          dictValue: "2",
        },
      ],
      total: 0,
      topicId: "",
      ruleForm: {
        pageSize: 10,
        pageNum: 1,
        topicStatus: "",
        onlineStatus: "",
        topicStartTime: "",
        topicEndTime: "",
      },
      deleteDialogVisible: false,
      listData: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    inquire() {
      //查询
      this.getList();
    },
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val;
      this.getList();
    },
    handleDelete(row) {
      this.topicId = row.topicId;
      this.deleteDialogVisible = true;
    },
    confirmDelete() {
      //确定删除
      delTable(this.topicId).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.deleteDialogVisible = false;
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    addMaternitySuite() {
      //新增
      this.dialogFormVisible = !this.dialogFormVisible;
    },
    getadd() {
      //新增
      this.loading = true;
      let from = this.form;
      if (from.name == "") {
        this.$message("请填写样式名称");
        return;
      }
      if (from.picture == "") {
        this.$message("请上传图片");
        return;
      }
      if (from.description == "") {
        this.$message("请填写模板内容");
        return;
      }
      this.form.picture = from.picture[0];
      save(from).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.dialogFormVisible = !this.dialogFormVisible;
          this.getList();
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    getList() {
      //列表
      this.loading = true;
      page(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.rows;
          this.total = res.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    compile(row) {
      //编辑
      this.dialogFormVisible = !this.dialogFormVisible;
      this.form.name = row.name;
      this.form.picture = row.picture;
      this.form.description = row.description;
      this.form.id = row.id;
    },
  },
};
</script>

<style scoped lang="scss">
.app {
  background: #f0f1f5;
  padding-bottom: 30px;
}
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .titles {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  margin: 20px 20px;
  .contentHead {
    background: #ffff;
    padding: 5px 14px;
    border-radius: 10px;
    .title2 {
      color: #17191a;
    }
  }
}
.contentHeads {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.roomMessage {
  display: flex;
}
.sel {
  display: flex;
  align-items: center;
  margin-right: 10px;
  .selTitle {
    width: 30%;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
  }
}
#cars {
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  color: #7c7d81;
  font-size: 14px;
  width: 179px;
  height: 32px;
  margin-left: 6px;
}

.block {
  text-align: right;
  margin-top: 20px;
}
</style>
