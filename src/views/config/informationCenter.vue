<template>
  <div class="app">
    <!-- <div class="head">
            <div class="titles">客资中心</div>
            <div class="title1">Pages/客资中心/客户管理</div>
        </div> -->
    <div class="content">
      <div class="contentHead">
        <h2 class="title2">客户管理</h2>
        <div class="contentHeads">
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">客户姓名</p>
              <el-input v-model="ruleForm.name" placeholder="请输入客户姓名" clearable></el-input>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">电话号码</p>
              <el-input v-model="ruleForm.tel" placeholder="请输入电话号码" clearable></el-input>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">客户来源</p>
              <el-select v-model="ruleForm.source" placeholder="全部" clearable style="width: 159px; margin-left: 10px">
                <el-option v-for="item in customerSource" :key="item.dictValue" :label="item.dictLabel"
                  :value="item.dictLabel">
                </el-option>
              </el-select>
            </div>
            <div class="sel">
              <p class="selTitle">状态</p>
              <el-select v-model="ruleForm.status" placeholder="全部" clearable style="width: 159px; margin-left: 10px">
                <el-option v-for="item in status" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue">
                </el-option>
              </el-select>
            </div>
          </div>
          <el-button type="primary" style="margin-left: 8px; height: 36px" @click="inquire">查询</el-button>
        </div>
        <el-button type="primary" style="height: 36px; margin: 20px 0 28px 0"
          @click="addMaternitySuite">新增客户</el-button>
        <el-table v-loading="loading" :data="listData" class="table">
          <el-table-column label="客户姓名" prop="name" :show-overflow-tooltip="true" align="center" />
          <el-table-column label="电话" prop="tel" :show-overflow-tooltip="true" align="center" />
          <el-table-column label="客户来源" prop="source" :show-overflow-tooltip="true" align="center" />
          <el-table-column label="状态" prop="status" :show-overflow-tooltip="true" align="center">
            <template slot-scope="scope">
              <p v-if="scope.row.status == 0">未联系</p>
              <p v-if="scope.row.status == 1">已联系</p>
              <p v-if="scope.row.status == 2">已到店</p>
              <p v-if="scope.row.status == 3">已交易</p>
            </template>
          </el-table-column>
          <el-table-column label="意向" prop="intention" align="center" />
          <el-table-column label="生日" prop="birthday" align="center">
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope" v-if="scope.row.roleId !== 1">
              <el-button size="mini" type="text" @click="examine(scope.row)">查看</el-button>
              <el-button size="mini" type="text" @click="compile(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="block">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :page-sizes="[10, 20, 30, 40]" :page-size="100" layout="total, sizes, prev, pager, next, jumper"
            :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { page, delTable, online } from "@/api/platform/informationCenter";
export default {
  name: "Customer",
  data() {
    return {
      loading: false,
      customerSource: [
        //在线状态
        {
          dictLabel: "小程序",
          dictValue: "1",
        },
        {
          dictLabel: "大众点评",
          dictValue: "2",
        },
        {
          dictLabel: "美团",
          dictValue: "3",
        },
        {
          dictLabel: "抖音",
          dictValue: "4",
        },
        {
          dictLabel: "小红书",
          dictValue: "5",
        },
        {
          dictLabel: "转介绍",
          dictValue: "6",
        },
        {
          dictLabel: "自然进店",
          dictValue: "7",
        },
        {
          dictLabel: "其他",
          dictValue: "8",
        },
      ],
      status: [
        //状态
        {
          dictLabel: "未联系",
          dictValue: "0",
        },
        {
          dictLabel: "已联系",
          dictValue: "1",
        },
        {
          dictLabel: "已到店",
          dictValue: "2",
        },
        {
          dictLabel: "已交易",
          dictValue: "3",
        },
      ],
      total: 0,
      customerId: "",
      ruleForm: {
        pageSize: 10,
        pageNum: 1,
        name: "",
        status: "",
        tel: "",
        source: "",
      },
      deleteDialogVisible: false,
      listData: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    inquire() {
      //查询
      this.getList();
    },
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val;
      this.getList();
    },
    addMaternitySuite() {
      //新增
      // this.$router.replace({ path: "/addinformationCenter" });
      this.$router.push({ path: "/addinformationCenter" });
    },
    getList() {
      //列表
      this.loading = true;
      page(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.rows;
          this.total = res.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    examine(row) {
      //查看
      this.$router.push({
        path: "/addinformationCenter",
        query: {
          customerId: row.customerId,
          forbidden: true,
          examine: 1,
        },
      });
    },
    compile(row) {
      //编辑
      this.$router.push({
        path: "/addinformationCenter",
        query: {
          customerId: row.customerId,
        },
      });
    }
  },
};
</script>

<style scoped lang="scss">
.app {
  background: #f0f1f5;
  padding-bottom: 30px;
}

.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;

  .titles {
    font-size: 22px;
    font-weight: bold;
  }

  .title1 {
    font-size: 14px;
  }
}

.content {
  margin: 20px 20px;

  .contentHead {
    background: #ffff;
    padding: 5px 14px;
    border-radius: 10px;

    .title2 {
      color: #17191a;
    }
  }
}

.contentHeads {
  display: flex;
  align-items: center;
}

.roomMessage {
  display: flex;
}

.sel {
  display: flex;
  align-items: center;
  margin-right: 10px;

  .selTitle {
    width: 30%;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
  }
}

#cars {
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  color: #7c7d81;
  font-size: 14px;
  width: 179px;
  height: 32px;
  margin-left: 6px;
}

.block {
  text-align: right;
  margin-top: 20px;
}
</style>
