<template>
  <div id="app">
    <!-- <div class="head">
            <div class="title">月子套房配置</div>
            <div class="title1">Pages/月子套房设置/新增房型</div>
        </div> -->
    <div class="content">
      <div class="auditContent" v-if="auditInfoList.auditStatus == 2">
        <div class="pass">
          <img
            src="https://aiermuying.oss-cn-shenzhen.aliyun`    cs.com/icon%402x.png"
            alt=""
            style="width: 20px; height: 20px"
          />
          <p>驳回原因</p>
        </div>
        <p class="passCause">{{ auditInfoList.rejectionReason }}</p>
      </div>
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>房间名称</p>
        </div>
        <el-input
          type="text"
          placeholder="请输入房间名称"
          maxlength="10"
          class="input"
          v-model="ruleForm.roomName"
          :disabled="forbidden"
        ></el-input>
      </div>
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>房间描述</p>
        </div>
        <el-input
          type="textarea"
          v-model="ruleForm.description"
          style="width: 368px"
          placeholder="请输入房间描述"
          :disabled="forbidden"
        ></el-input>
      </div>
      <div class="content1">
        <div class="title5">
          <p>房间价格</p>
        </div>
        <el-input
          type="text"
          placeholder="请输入房间价格"
          maxlength="10"
          class="input"
          v-model="ruleForm.price"
          :disabled="forbidden"
        ></el-input>
      </div>
      <div class="content1" v-if="!forbidden">
        <div class="title5">
          <p class="required">*</p>
          <p>房间标签</p>
        </div>
        <el-tag
          :key="tag"
          v-for="tag in ruleForm.tag"
          closable
          :disable-transitions="false"
          @close="handleCloses(tag)"
          class="elTag"
        >
          {{ tag }}
        </el-tag>
      </div>
      <div class="content1" v-if="forbidden">
        <div class="title5">
          <p class="required">*</p>
          <p>房间标签</p>
        </div>
        <el-tag
          :key="tag"
          v-for="tag in ruleForm.tag"
          closable
          :disable-transitions="false"
          @close="handleCloses(tag)"
        >
          {{ tag }}
        </el-tag>
      </div>

      <div class="content1">
        <div class="title5"></div>
        <div class="label">
          <el-tag
            :key="tag"
            v-for="tag in labelList"
            closable
            :disable-transitions="false"
            @close="handleClose(tag)"
            @click="choice(tag)"
            class="elTag"
          >
            {{ tag }}
          </el-tag>
          <el-input
            class="input-new-tag"
            v-if="inputVisible"
            v-model="inputValue"
            ref="saveTagInput"
            size="small"
            @keyup.enter.native="handleInputConfirm"
            @blur="handleInputConfirm"
          >
          </el-input>
          <el-button
            v-else
            class="button-new-tag"
            size="small"
            @click="showInput"
            style="height: 26px; line-height: 26px"
            :disabled="forbidden"
            >+ 添加标签</el-button
          >
        </div>
      </div>

      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>房间照片</p>
        </div>
        <div class="upload">
          <div class="viewLocations">
            <image-upload
              :oneAll="2"
              :limit="8"
              :isShowTip="false"
              v-model="ruleForm.suitePhotos"
              v-if="!forbidden"
            />

            <!-- <img-upload v-model="cover" width='240px' height='135px' border>
              <template #imgUpload>
                <div style="width: 100%;height: 100%;"
                  class="flex flex-direction-column flex-content-center flex-align-center">
                  <em class="el-icon-circle-plus" style=" font-size: 23px;color: #b8b8b8;"></em>
                  <span style="font-size: 14px;font-weight: 400;color: #0f0f0f;">设置作品封面</span>
                </div>
              </template>
            </img-upload> -->

            <div
              class="viewLocation"
              @click="
                onPreview(
                  'http://cdn.xiaodingdang1.com/2024/05/11/74c621a42eeb40e48cbeb7ccdf36631epng'
                )
              "
            >
              <img
                src="http://cdn.xiaodingdang1.com/2024/05/11/74c621a42eeb40e48cbeb7ccdf36631epng"
                alt=""
                style="width: 180px; height: 110px"
              />
              <div class="viewLocation1">查看位置</div>
            </div>
          </div>
          <div v-if="forbidden">
            <img
              :src="item"
              alt=""
              v-for="(item, index) in ruleForm.suitePhotos"
              :key="index"
              style="
                width: 146px;
                height: 146px;
                margin-right: 10px;
                border: 1px solid #c0ccda;
                border-radius: 6px;
              "
            />
          </div>
        </div>
      </div>
      <!-- <div class="title3" v-if="!forbidden">
                <div class="title5"></div>
                <div> 请上传房间环境相关图片，如卧室照片、卫生间图片</div>
            </div> -->
      <div class="hint" v-if="!forbidden">
        <div class="title5"></div>
        <div class="hints">
          <img
            src="http://cdn.xiaodingdang1.com/info-circle-filled.png"
            alt=""
          />
          <p>请上传房间环境相关图片，如卧室照片、卫生间图片</p>
        </div>
      </div>
      <div class="content1">
        <div class="title5">
          <p>图文详情</p>
        </div>
        <div class="upload">
          <image-upload
            :oneAll="3"
            :limit="8"
            :isShowTip="false"
            v-model="ruleForm.textPhotos"
            v-if="!forbidden"
          />
        </div>
      </div>
      <div class="content1">
        <div class="title5">
          <p>房间视频</p>
        </div>
        <div class="upload">
          <file-upload
            :limit="1"
            :isShowTip="false"
            v-model="ruleForm.suiteVideos"
            v-if="!forbidden"
          />
          <div v-if="forbidden">
            <video
              controls="controls"
              :src="item"
              alt=""
              v-for="(item, index) in ruleForm.suiteVideos"
              :key="index"
              style="
                width: 146px;
                height: 146px;
                margin-right: 10px;
                border: 1px solid #c0ccda;
                border-radius: 6px;
              "
            ></video>
          </div>
        </div>
      </div>
      <div class="hint" v-if="!forbidden">
        <div class="title5"></div>
        <div class="hints">
          <img
            src="http://cdn.xiaodingdang1.com/info-circle-filled.png"
            alt=""
          />
          <div>1.建议格式为16：9，时间30-45s</div>
          <div style="margin-left: 10px">2.请上传房间环境的实拍视频</div>
        </div>
      </div>
      <div class="content1">
        <div class="title5">
          <p class="required"></p>
          <p>签约礼</p>
        </div>
        <div class="roomMessage">
          <el-select
            v-model="ruleForm.contractGiftId"
            placeholder="请选择签约礼"
            clearable
            style="width: 240px"
            :disabled="forbidden"
          >
            <el-option
              v-for="item in giftList"
              :key="item.contractGiftId"
              :label="item.description"
              :value="item.contractGiftId"
            />
          </el-select>
        </div>
      </div>
      <!-- <div class="title3" v-if="!forbidden">
                <div class="title5"></div>
                <div>
                    <p>1.建议格式为16：9，时间30-45s</p>
                    <p>2.请上传房间环境的实拍视频</p>
                </div>
            </div> -->
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>房间基本信息</p>
        </div>
        <div class="roomMessage">
          <div class="sel">
            <p class="selTitle">房型</p>
            <el-select
              v-model="ruleForm.roomType"
              placeholder="请选择房型"
              clearable
              style="width: 129px; margin-left: 10px"
              :disabled="forbidden"
            >
              <el-option
                v-for="item in roomTypeList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictLabel"
              />
            </el-select>
          </div>
          <div class="sel">
            <p class="selTitle">朝向</p>
            <el-select
              v-model="ruleForm.orientation"
              placeholder="请选择朝向"
              clearable
              style="width: 129px; margin-left: 10px"
              :disabled="forbidden"
            >
              <el-option
                v-for="item in orientationList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictLabel"
              />
            </el-select>
          </div>
          <div class="sel">
            <p class="selTitle">床型</p>
            <el-select
              v-model="ruleForm.bedType"
              placeholder="请选择床型"
              clearable
              style="width: 129px; margin-left: 10px"
              :disabled="forbidden"
            >
              <el-option
                v-for="item in bedTypeList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictLabel"
              />
            </el-select>
          </div>
        </div>
      </div>
      <div class="content1">
        <div class="title5"></div>
        <div class="postpartum">
          <div class="postpartum">
            <div class="title7">楼层</div>
            <el-input
              placeholder="最低楼层"
              v-model="ruleForm.minFloor"
              class="inputs"
              :disabled="forbidden"
            >
              <template slot="append">楼</template>
            </el-input>
          </div>
          <div class="jia"></div>
          <div class="postpartum">
            <el-input
              placeholder="最高楼层"
              class="inputs"
              v-model="ruleForm.maxFloor"
              :disabled="forbidden"
            >
              <template slot="append">楼</template>
            </el-input>
          </div>
          <div class="postpartum" id="postpartum">
            <div class="title7">面积</div>
            <el-input
              placeholder="最小面积"
              class="inputs"
              v-model="ruleForm.minArea"
              :disabled="forbidden"
            >
              <template slot="append">m²</template>
            </el-input>
          </div>
          <div class="jia"></div>
          <div class="postpartum">
            <el-input
              placeholder="最大面积"
              class="inputs"
              v-model="ruleForm.maxArea"
              :disabled="forbidden"
            >
              <template slot="append">m²</template>
            </el-input>
          </div>
        </div>
      </div>
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>室外景观</p>
        </div>
        <div>
          <el-checkbox-group
            v-model="ruleForm.outdoorFeatures"
            class="checkboxList"
            :disabled="forbidden"
          >
            <el-checkbox
              :label="dict.label"
              v-for="dict in dict.type.platform_suite_outdoor_features"
              :key="dict.value"
              :value="dict.value"
              class="checkboxLists"
            />
          </el-checkbox-group>
        </div>
      </div>
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>便利设施</p>
        </div>
        <div>
          <el-checkbox-group
            v-model="ruleForm.facilityFeatures"
            class="checkboxList"
            :disabled="forbidden"
          >
            <el-checkbox
              :label="dict.label"
              v-for="dict in dict.type.platform_suite_facility_features"
              :key="dict.value"
              :value="dict.value"
              class="checkboxLists"
            />
          </el-checkbox-group>
        </div>
      </div>
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>媒体娱乐</p>
        </div>
        <div>
          <el-checkbox-group
            v-model="ruleForm.mediaFeatures"
            class="checkboxList"
            :disabled="forbidden"
          >
            <el-checkbox
              :label="dict.label"
              v-for="dict in dict.type.platform_suite_media_features"
              :key="dict.value"
              :value="dict.value"
              class="checkboxLists"
            />
          </el-checkbox-group>
        </div>
      </div>
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>卫浴配套</p>
        </div>
        <div>
          <el-checkbox-group
            v-model="ruleForm.bathroomFacilities"
            class="checkboxList"
            :disabled="forbidden"
          >
            <el-checkbox
              :label="dict.label"
              v-for="dict in dict.type.platform_suite_bathroom_facilities"
              :key="dict.value"
              :value="dict.value"
              class="checkboxLists"
            />
          </el-checkbox-group>
        </div>
      </div>
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>房间基本信息</p>
        </div>
        <div class="roomMessage">
          <div class="sel">
            <p class="selTitle">打扫</p>
            <el-select
              v-model="ruleForm.cleaningFrequency"
              placeholder="请选择频次"
              clearable
              style="width: 129px; margin-left: 10px"
              :disabled="forbidden"
            >
              <el-option
                v-for="item in cleaningFrequencyList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              />
            </el-select>
          </div>
          <div class="sel">
            <p class="selTitle">换床单</p>
            <el-select
              v-model="ruleForm.sheetChangeFrequency"
              placeholder="请选择频次"
              clearable
              style="width: 129px; margin-left: 10px"
              :disabled="forbidden"
            >
              <el-option
                v-for="item in sheetChangeFrequencyList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              />
            </el-select>
          </div>
          <div class="sel">
            <p class="selTitle">消毒</p>
            <el-select
              v-model="ruleForm.disinfectionFrequency"
              placeholder="请选择频次"
              clearable
              style="width: 129px; margin-left: 10px"
              :disabled="forbidden"
            >
              <el-option
                v-for="item in disinfectionFrequencyList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              />
            </el-select>
          </div>
        </div>
      </div>
      <!-- <div class="fgx"></div>
      <div>
        <div class="brandTitle">品牌介绍</div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>标题</p>
          </div>
          <el-input
            type="text"
            placeholder="请输入标题"
            maxlength="10"
            class="input"
            v-model="ruleForm.roomName"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>描述</p>
          </div>
          <el-input
            type="textarea"
            v-model="ruleForm.description"
            style="width: 368px"
            placeholder="请输入描述信息"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p>照片</p>
          </div>
          <div class="upload">
            <image-upload
              :oneAll="3"
              :limit="8"
              :isShowTip="false"
              v-model="ruleForm.textPhotos"
              v-if="!forbidden"
            />
          </div>
        </div>
        <div class="fgx"></div>
      </div> -->
      <!-- <div>
        <div class="brandTitle">会所设施</div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>标题</p>
          </div>
          <el-input
            type="text"
            placeholder="请输入标题"
            maxlength="10"
            class="input"
            v-model="ruleForm.roomName"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>描述</p>
          </div>
          <el-input
            type="textarea"
            v-model="ruleForm.description"
            style="width: 368px"
            placeholder="请输入描述信息"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p>照片</p>
          </div>
          <div class="upload">
            <image-upload
              :oneAll="3"
              :limit="8"
              :isShowTip="false"
              v-model="ruleForm.textPhotos"
              v-if="!forbidden"
            />
          </div>
        </div>
        <div class="fgx"></div>
      </div> -->
      <!-- <div>
        <div class="brandTitle">护理团队</div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>标题</p>
          </div>
          <el-input
            type="text"
            placeholder="请输入标题"
            maxlength="10"
            class="input"
            v-model="ruleForm.roomName"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>描述</p>
          </div>
          <el-input
            type="textarea"
            v-model="ruleForm.description"
            style="width: 368px"
            placeholder="请输入描述信息"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p>照片</p>
          </div>
          <div class="upload">
            <image-upload
              :oneAll="3"
              :limit="8"
              :isShowTip="false"
              v-model="ruleForm.textPhotos"
              v-if="!forbidden"
            />
          </div>
        </div>
        <div class="fgx"></div>
      </div> -->
      <!-- <div>
        <div class="brandTitle">产后康复</div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>标题</p>
          </div>
          <el-input
            type="text"
            placeholder="请输入标题"
            maxlength="10"
            class="input"
            v-model="ruleForm.roomName"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>描述</p>
          </div>
          <el-input
            type="textarea"
            v-model="ruleForm.description"
            style="width: 368px"
            placeholder="请输入描述信息"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p>照片</p>
          </div>
          <div class="upload">
            <image-upload
              :oneAll="3"
              :limit="8"
              :isShowTip="false"
              v-model="ruleForm.textPhotos"
              v-if="!forbidden"
            />
          </div>
        </div>
        <div class="fgx"></div>
      </div> -->
      <!-- <div>
        <div class="brandTitle">月子膳食</div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>标题</p>
          </div>
          <el-input
            type="text"
            placeholder="请输入标题"
            maxlength="10"
            class="input"
            v-model="ruleForm.roomName"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>描述</p>
          </div>
          <el-input
            type="textarea"
            v-model="ruleForm.description"
            style="width: 368px"
            placeholder="请输入描述信息"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p>照片</p>
          </div>
          <div class="upload">
            <image-upload
              :oneAll="3"
              :limit="8"
              :isShowTip="false"
              v-model="ruleForm.textPhotos"
              v-if="!forbidden"
            />
          </div>
        </div>
      </div> -->
      <div class="btn" v-if="!forbidden">
        <el-button type="primary" @click="confirm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
      <div class="btn" v-if="examine">
        <el-button @click="cancel">返回</el-button>
      </div>
      <div class="btn" v-if="auditInfoList.auditStatus == 0">
        <el-button type="primary" @click="pass">通过</el-button>
        <el-button @click="reject">驳回</el-button>
      </div>
      <div class="btn">
        <el-button type="success" v-if="auditInfoList.auditStatus == 1"
          >已通过</el-button
        >
        <el-button type="warning" v-if="auditInfoList.auditStatus == 2"
          >已驳回</el-button
        >
      </div>
    </div>
    <!--驳回-->
    <el-dialog title="驳回原因" :visible.sync="dialogVisibleReject" width="30%">
      <el-input
        type="textarea"
        v-model="rejectionReason"
        placeholder="请输入驳回原因"
      ></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleReject = false">取 消</el-button>
        <el-button type="primary" @click="rejectConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-image-viewer
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="[url]"
    />
  </div>
</template>

<script>
import FileUpload from "@/components/FileUpload/index";
import { addConfig } from "@/api/system/user";
import ImageUpload from "@/components/ImageUpload/index";
import ImgUpload from "@/components/ImgUpload/index";
import { page, save, info, update } from "@/api/platform/suite";
import { giftList } from "@/api/platform/public";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
export default {
  name: "app",
  components: { ImageUpload, FileUpload, ElImageViewer, ImgUpload },
  dicts: [
    "platform_suite_outdoor_features",
    "platform_suite_facility_features",
    "platform_suite_media_features",
    "platform_suite_bathroom_facilities",
  ],
  data() {
    return {
      giftList: [],
      showViewer: false, // 显示查看器
      url: "",
      examine: "",
      inputVisible: false,
      inputValue: "",
      labelList: [],
      suiteId: "",
      imgList: [
        {
          img: "http://cdn.xiaodingdang1.com/2024/03/16/3eccfb04bdbc4c689b7a6e44c35ce3b2png",
        },
        {
          img: "http://cdn.xiaodingdang1.com/2024/03/16/3eccfb04bdbc4c689b7a6e44c35ce3b2png",
        },
        {
          img: "http://cdn.xiaodingdang1.com/2024/03/16/3eccfb04bdbc4c689b7a6e44c35ce3b2png",
        },
      ],
      audit: "",
      auditId: "",
      forbidden: false,
      auditInfoList: [],
      dialogVisibleReject: false,
      rejectionReason: "",
      dialogImageUrl: "",
      dialogVisible: false,
      productImgs: [],
      isMultiple: true,
      imgLimit: 6,
      disinfectionFrequencyList: [
        {
          dictLabel: "每天",
          dictValue: "1",
        },
        {
          dictLabel: "隔天",
          dictValue: "2",
        },
        {
          dictLabel: "每周",
          dictValue: "3",
        },
        {
          dictLabel: "其他",
          dictValue: "4",
        },
      ],
      sheetChangeFrequencyList: [
        {
          dictLabel: "每天",
          dictValue: "1",
        },
        {
          dictLabel: "隔天",
          dictValue: "2",
        },
        {
          dictLabel: "每周",
          dictValue: "3",
        },
        {
          dictLabel: "其他",
          dictValue: "4",
        },
      ],
      cleaningFrequencyList: [
        {
          dictLabel: "每天",
          dictValue: "1",
        },
        {
          dictLabel: "隔天",
          dictValue: "2",
        },
        {
          dictLabel: "每周",
          dictValue: "3",
        },
        {
          dictLabel: "其他",
          dictValue: "4",
        },
      ],
      roomTypeList: [
        {
          dictLabel: "标准间",
          dictValue: "1",
        },
        {
          dictLabel: "一房一厅",
          dictValue: "2",
        },
        {
          dictLabel: "一房两厅",
          dictValue: "7",
        },
        {
          dictLabel: "两房一厅",
          dictValue: "3",
        },
        {
          dictLabel: "两房两厅",
          dictValue: "6",
        },
        {
          dictLabel: "三房一厅",
          dictValue: "4",
        },
        {
          dictLabel: "四房一厅",
          dictValue: "5",
        },
      ],
      orientationList: [
        {
          dictLabel: "东",
          dictValue: "1",
        },
        {
          dictLabel: "南",
          dictValue: "2",
        },
        {
          dictLabel: "西",
          dictValue: "3",
        },
        {
          dictLabel: "北",
          dictValue: "4",
        },
        {
          dictLabel: "东南",
          dictValue: "5",
        },
        {
          dictLabel: "西南",
          dictValue: "6",
        },
        {
          dictLabel: "东北",
          dictValue: "7",
        },
        {
          dictLabel: "西北",
          dictValue: "8",
        },
      ],
      bedTypeList: [
        //床型
        {
          dictLabel: "1.2米单人床",
          dictValue: "1",
        },
        {
          dictLabel: "1.35米床",
          dictValue: "2",
        },
        {
          dictLabel: "1.5米大床",
          dictValue: "3",
        },
        {
          dictLabel: "1.8米大床",
          dictValue: "4",
        },
        {
          dictLabel: "2米大床",
          dictValue: "5",
        },
        {
          dictLabel: "2.2米大床",
          dictValue: "6",
        },
      ],
      ruleForm: {
        textPhotos: [],
        roomName: "", //房间名称
        suitePhotos: [], //房间照片
        suiteVideos: [], //房间视频
        roomType: "", //房型
        orientation: "", //朝向
        bedType: "", //床型
        minFloor: "", //最低楼层
        maxFloor: "", //最高楼层
        minArea: "", //最小面积
        maxArea: "", //最大面积
        outdoorFeatures: [], //室外景观
        facilityFeatures: [], //便利设施
        mediaFeatures: [], //媒体娱乐
        bathroomFacilities: [], //卫浴配套
        cleaningFrequency: "", //打扫频次
        sheetChangeFrequency: "", //换床单频次
        disinfectionFrequency: "", //消毒频次
        dialogVisible: false,
        tag: [], //房型标签
        description: "", //套房描述
        price: "", //房间价格
        contractGiftId: "",
      },
      outdoorFeaturesList: [
        {
          dictLabel: "城景",
          dictValue: "1",
        },
        {
          dictLabel: "园景",
          dictValue: "2",
        },
        {
          dictLabel: "山景",
          dictValue: "3",
        },
        {
          dictLabel: "河景",
          dictValue: "4",
        },
        {
          dictLabel: "湖景",
          dictValue: "5",
        },
        {
          dictLabel: "海景",
          dictValue: "6",
        },
        {
          dictLabel: "江景",
          dictValue: "7",
        },
        {
          dictLabel: "有露台",
          dictValue: "8",
        },
      ],
      facilityFeaturesList: [
        {
          dictLabel: "空气净化器",
          dictValue: "1",
        },
        {
          dictLabel: "中央空调",
          dictValue: "2",
        },
        {
          dictLabel: "净水系统",
          dictValue: "3",
        },
        {
          dictLabel: "新风系统",
          dictValue: "4",
        },
        {
          dictLabel: "电热水壶",
          dictValue: "5",
        },
        {
          dictLabel: "免费WiFi",
          dictValue: "6",
        },
        {
          dictLabel: "烟雾报警器",
          dictValue: "7",
        },
        {
          dictLabel: "空调",
          dictValue: "8",
        },
      ],
      suiteMediaFeaturesList: [
        {
          dictLabel: "电视",
          dictValue: "1",
        },
        {
          dictLabel: "投影仪",
          dictValue: "2",
        },
        {
          dictLabel: "音响",
          dictValue: "3",
        },
        {
          dictLabel: "电脑",
          dictValue: "4",
        },
        {
          dictLabel: "游戏机",
          dictValue: "5",
        },
      ],
      bathroomFacilitiesList: [
        {
          dictLabel: "独立卫生间",
          dictValue: "1",
        },
        {
          dictLabel: "智能马桶",
          dictValue: "2",
        },
        {
          dictLabel: "24小时热水",
          dictValue: "3",
        },
        {
          dictLabel: "独立淋浴间",
          dictValue: "4",
        },
        {
          dictLabel: "浴缸",
          dictValue: "5",
        },
        {
          dictLabel: "婴儿浴盆",
          dictValue: "6",
        },
        {
          dictLabel: "吹风机",
          dictValue: "7",
        },
      ],
      cover: "",
    };
  },
  created() {
    this.getList();
    let suiteId = this.$route.query.suiteId;
    let audit = this.$route.query.audit;
    this.audit = audit;
    this.forbidden = this.$route.query.forbidden ? true : false;
    this.auditId = this.$route.query.auditId;
    this.examine = this.$route.query.examine;
    if (audit) {
      this.auditInfo(this.auditId);
    }
    this.suiteId = suiteId;
    if (suiteId) {
      this.info(suiteId);
    }
  },
  methods: {
    getList() {
      //列表
      this.loading = true;
      let data = {
        pageSize: 1000,
        pageNum: 1,
      };
      giftList(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.giftList = res.data;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    onPreview(e) {
      this.showViewer = true;
      this.url = e;
    },
    // 关闭查看器
    closeViewer() {
      this.showViewer = false;
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm() {
      if (this.labelList.length == 8) {
        this.$message("标签最多只能添加8个");
        this.inputVisible = false;
        this.inputValue = "";
        return;
      }
      let inputValue = this.inputValue;
      if (inputValue) {
        this.labelList.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
    handleClose(tag) {
      this.labelList.splice(this.labelList.indexOf(tag), 1);
    },
    handleCloses(tag) {
      this.ruleForm.tag.splice(this.ruleForm.tag.indexOf(tag), 1);
    },
    choice(e) {
      if (this.ruleForm.tag.length == 2) {
        this.$message("房间标签最多只能添加2个");
        return;
      }
      this.ruleForm.tag.forEach((item) => {
        if (item == e) {
          this.$message("标签已添加");
          black;
        }
      });
      this.ruleForm.tag.push(e);
    },
    cancel() {
      this.$router.go(-1);
    },
    rejectConfirm() {
      //驳回确定
      if (this.rejectionReason == "") {
        this.$message("请填写驳回理由");
        return;
      }
      this.dialogVisibleReject = false;
      let data = {
        auditId: this.auditId,
        auditResult: false,
        rejectionReason: this.rejectionReason,
      };
      audit(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.$router.go(-1);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    reject() {
      //驳回
      this.dialogVisibleReject = true;
    },
    pass() {
      //通过
      this.$confirm("是否审核通过?", "审核通过", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // this.$message({
          //   type: 'success',
          //   message: '审核通过!'
          // });
          let data = {
            auditId: this.auditId,
            auditResult: true,
          };
          audit(data).then((res) => {
            if (res.code == 200) {
              this.$message(res.msg);
              this.$router.go(-1);
              return;
            } else {
              this.$message(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消审核",
          });
        });
    },

    auditInfo(auditId) {
      //查询审核信息
      auditInfo(auditId).then((res) => {
        if (res.code == 200) {
          this.auditInfoList = res.data;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      console.log(file.url);
      this.suitePhotos = file.url;
      this.dialogVisible = true;
    },
    handleCheckedCitiesChange() {},
    showValue(e) {
      this.ruleForm[`${e}`] = this.$refs[`${e}`].value;
      console.log(this.ruleForm);
    },
    confirm() {
      //确定
      let data = this.ruleForm;
      if (data.roomName == "") {
        this.$message("请输入房间名称");
        return;
      }

      if (data.description == "") {
        this.$message("请输入房间描述");
        return;
      }
      if (data.tag.length == 0) {
        this.$message("请添加房型标签");
        return;
      }
      if (data.suitePhotos.length == 0) {
        this.$message("请上传房间照片");
        return;
      }
      if (data.roomType == "") {
        this.$message("请选择房型");
        return;
      }
      if (data.orientation == "") {
        this.$message("请选择朝向");
        return;
      }
      if (data.bedType == "") {
        this.$message("请选择床型");
        return;
      }

      if (data.minFloor == "") {
        this.$message("请输入最低楼层");
        return;
      }
      if (data.maxFloor == "") {
        this.$message("请输入最高楼层");
        return;
      }
      if (data.minArea == "") {
        this.$message("请输入最小面积");
        return;
      }
      if (data.maxArea == "") {
        this.$message("请输入最大面积");
        return;
      }
      if (data.outdoorFeatures.length == 0) {
        this.$message("请选择室外景观");
        return;
      }
      if (data.outdoorFeatures.length == 0) {
        this.$message("请选择便利设施");
        return;
      }
      if (data.mediaFeatures.length == 0) {
        this.$message("请选择媒体娱乐");
        return;
      }
      if (data.bathroomFacilities.length == 0) {
        this.$message("请选择卫浴配套");
        return;
      }
      if (data.cleaningFrequency == "") {
        this.$message("请选择打扫频次");
        return;
      }
      if (data.sheetChangeFrequency == "") {
        this.$message("请选择换床单频次");
        return;
      }
      if (data.disinfectionFrequency == "") {
        this.$message("请选择消毒频次");
        return;
      }
      if (!this.ruleForm.suiteVideos) {
        this.ruleForm.suiteVideos = [];
      }
      if (!this.suiteId) {
        save(this.ruleForm).then((res) => {
          console.log(res);
          if (res.code == 200) {
            this.$message(res.msg);
            this.$router.go(-1);
            return;
          } else {
            this.$message(res.msg);
          }
        });
      }
      if (this.suiteId) {
        update(this.ruleForm).then((res) => {
          console.log(res);
          if (res.code == 200) {
            this.$message(res.msg);
            this.$router.go(-1);
            return;
          } else {
            this.$message(res.msg);
          }
        });
      }
    },
    info(suiteId) {
      //查询月子套房信息
      info(suiteId).then((res) => {
        if (res.code == 200) {
          this.ruleForm = res.data;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    handleRemove(file, fileList) {
      //移除图片
      console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      //预览图片时调用
      console.log(file);
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },

    beforeAvatarUpload(file) {
      //文件上传之前调用做一些拦截限制
      console.log(file);
      const isJPG = true;
      // const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 2;

      // if (!isJPG) {
      //   this.$message.error('上传头像图片只能是 JPG 格式!');
      // }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    handleAvatarSuccess(res, file) {
      //图片上传成功
      console.log(res);
      console.log(file);
      this.imageUrl = URL.createObjectURL(file.raw);
    },
    handleExceed(files, fileList) {
      //图片上传超过数量限制
      this.$message.error("上传图片不能超过6张!");
      console.log(file, fileList);
    },
    imgUploadError(err, file, fileList) {
      //图片上传失败调用
      console.log(err);
      this.$message.error("上传图片失败!");
    },
  },
};
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;

  .title {
    font-size: 22px;
    font-weight: bold;
  }

  .title1 {
    font-size: 14px;
  }
}

.content {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
  padding: 24px 50px;

  .title2 {
    font-size: 20px;
    color: #17191a;
    margin-bottom: 24px;
  }

  .table {
    margin-top: 20px;
  }

  .title5 {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
    width: 10%;

    .required {
      color: red;
    }
  }

  .content1 {
    display: flex;
    align-items: center;
    margin: 20px 0 10px 0;

    .input {
      width: 240px;
      height: 32px;
      border-radius: 3px;
    }

    .upload {
    }
  }
}

.title3 {
  display: flex;
  color: #45464a;
  font-size: 14px;
}

.sel {
  display: flex;
  align-items: center;
  margin-right: 10px;

  .selTitle {
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
  }

  #cars {
    border: 1px solid #dcdcdc;
    border-radius: 3px;
    color: #7c7d81;
    font-size: 14px;
    width: 129px;
    height: 32px;
    margin-left: 6px;
  }
}

.roomMessage {
  display: flex;
}

.postpartum {
  display: flex;
  align-items: center;
}

.inputs {
  width: 160px;
  height: 32px;
  border-radius: 3px;
}

.units {
  width: 30px;
  height: 32px;
  background: #dcdcdc;
  border: 1px solid #dcdcdc;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
}

.jia {
  width: 16px;
  height: 3px;
  background: #17191a;
  margin: 0 8px;
}

.title7 {
  font-size: 14px;
  color: #606266;
  box-sizing: border-box;
  font-weight: bold;
  margin-right: 8px;
}

#postpartum {
  margin-left: 8px;
}

.checkboxList {
  width: 690px;

  .checkboxLists {
    width: 100px;
    margin-bottom: 8px;
  }
}

.btn {
  margin: 20px auto;
  text-align: center;
}

.auditContent {
  width: 100%;
  padding: 20px 24px;
  background: #ff6c11;
  border-radius: 6px;
  font-size: 14px;
  line-height: 14px;
}

.pass {
  display: flex;
  align-items: center;
  color: #000000;
}

.passCause {
  color: #000000;
  margin-left: 20px;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.label {
  width: 368px;
  height: 86px;
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  display: flex;
  flex-wrap: wrap;
  padding: 10px 10px;

  .labelName {
    color: #7c7d81;
    font-size: 14px;
    height: 24px;
    border-radius: 3px;
    line-height: 24px;
    background: #ebebeb;
    margin-right: 16px;
    padding: 0 8px;
  }

  .inputLabel {
    width: 156px;
    font-size: 12px;
  }
}

.elTag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.hint {
  display: flex;
  color: #45464a;
  font-size: 14px;
  margin-top: 8px;

  .hints {
    display: flex;
    align-items: center;

    .hints1 {
      color: #ff6c11;
    }
  }
}

.viewLocations {
  display: flex;

  .viewLocation {
    padding: 5px 5px;
    border-radius: 10px;
    border: 1px solid #dcdcdc;
    margin-left: 10px;
    height: 150px;

    .viewLocation1 {
      color: #3f85ff;
      font-size: 14px;
      text-align: center;
      margin-top: 8px;
    }
  }
}
.brandTitle {
  color: #17191a;
  font-size: 16px;
  font-weight: bold;
}
.fgx {
  width: 100%;
  height: 1px;
  background: #ebebeb;
  margin: 10px 0;
}
</style>
