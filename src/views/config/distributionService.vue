<template>
  <div class="app" v-loading="loading">
    <!-- <div class="head">
          <div class="title">配送服务</div>
          <div class="title1">Pages/配送服务/膳食配送</div>
      </div> -->
    <div class="content">
      <div class="title2">膳食配送</div>
      <el-button type="primary" @click="addCheckManagement">新增</el-button>
      <el-table :data="listData" class="table">
        <el-table-column label="照片" align="center">
          <template slot-scope="scope">
            <img
              :src="scope.row.productPhotoUrl[0]"
              min-width="70"
              height="70"
              style="width: 100px; height: 100px; border-radius: 5px"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="名称"
          prop="productName"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          label="商品ID"
          prop="deliveryId"
          :show-overflow-tooltip="true"
          align="center"
        >
        </el-table-column>
        <el-table-column label="内容" prop="productDescription" align="center">
          <template slot-scope="scope">
            <p class="productDescription">{{ scope.row.productDescription }}</p>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope" v-if="scope.row.roleId !== 1">
            <el-button size="mini" type="text" @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button size="mini" type="text" @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog title="删除" :visible.sync="deleteDialogVisible" width="30%">
      <span>是否删除此配送服务？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
      </span>
    </el-dialog>
    <!--新增-->
    <el-dialog title="新增配送" :visible.sync="addShow" width="30%">
      <!-- <el-form :model="from" :rules="rules" ref="from" label-width="100px" class="demo-ruleForm"> -->
      <el-form
        :model="from"
        ref="from"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="商品照片*" style="flex: 1" prop="productPhotoUrl">
          <div style="display: flex; flex-wrap: wrap">
            <image-upload
              :oneAll="2"
              :isShowTip="false"
              v-model="from.productPhotoUrl"
            />
          </div>
        </el-form-item>
        <el-form-item label="商品视频*" style="flex: 1" prop="productPhotoUrl">
          <div style="display: flex; flex-wrap: wrap">
            <file-upload :limit="1" :isShowTip="false" v-model="from.videos" />
          </div>
        </el-form-item>
        <!-- <el-form-item label="商品描述" prop="productDescription">
  <el-input type="textarea" v-model="from.productDescription" placeholder="请输入商品描述"></el-input>
</el-form-item> -->
        <!-- <div class="BackgroundPicture" v-if="!forbidden">
       <div class="uploadTitle"><p style="color: #F84343;">*</p> <p>会所标签</p></div>
       <div class="upload">
        <el-tag
  :key="tag"
  v-for="tag in ruleForm.clubTagNames"
  closable
  :disable-transitions="false"
  @close="handleCloses(tag)">
  {{tag}}
</el-tag>
       </div>
    </div> -->
        <el-form-item label="商品标签*" style="flex: 1" prop="tag">
          <div style="display: flex; flex-wrap: wrap">
            <el-tag
              :key="tag"
              v-for="tag in from.tag"
              closable
              :disable-transitions="false"
              @close="handleCloses(tag)"
              class="tagTitle"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="" prop="name">
          <div class="label">
            <el-tag
              :key="tag"
              v-for="tag in labelList"
              closable
              :disable-transitions="false"
              @close="handleClose(tag)"
              @click="choice(tag)"
              class="tagTitle"
            >
              {{ tag }}
            </el-tag>
            <el-input
              class="input-new-tag"
              v-if="inputVisible"
              v-model="inputValue"
              ref="saveTagInput"
              size="small"
              @keyup.enter.native="handleInputConfirm"
              @blur="handleInputConfirm"
            >
            </el-input>
            <el-button
              v-else
              class="button-new-tag"
              size="small"
              @click="showInput"
              style="height: 26px; line-height: 9px"
              >+ 添加标签</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="商品价格" prop="productPrice">
          <el-input
            v-model="from.productPrice"
            type="number"
            placeholder="请输入商品价格"
          ></el-input>
        </el-form-item>

        <el-form-item label="商品名称" prop="productName">
          <el-input
            v-model="from.productName"
            placeholder="请输入商品名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="图文详情*" style="flex: 1" prop="productDetails">
          <div style="display: flex; flex-wrap: wrap">
            <image-upload
              :oneAll="3"
              :limit="6"
              :isShowTip="false"
              v-model="from.productDetails"
            />
          </div>
          <div class="imgHint">
            <img
              src="http://cdn.xiaodingdang1.com/info-circle-filled.png"
              alt=""
              style="width: 14px; height: 14px"
            />
            <div>最多可配置六张图片</div>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addShow = false">取 消</el-button>
        <el-button type="primary" @click="submitForm('from')">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible" width="80%">
      <img width="100%" :src="dialogImageUrl" alt="" />
      <i
        class="el-icon-arrow-left"
        @click="leftPhoto"
        v-if="currentIndex > 0"
        style="
          background: rgba(0, 0, 0, 0.3);
          width: 35px;
          height: 70px;
          text-align: center;
          line-height: 70px;
          color: #fff;
          font-size: 23px;
          position: absolute;
          top: 48%;
          left: 15px;
        "
      >
      </i>
      <i
        class="el-icon-arrow-right"
        @click="rightPhoto"
        v-if="currentIndex < photoList.length - 1"
        style="
          background: rgba(0, 0, 0, 0.3);
          width: 35px;
          height: 70px;
          text-align: center;
          line-height: 70px;
          color: #fff;
          font-size: 23px;
          position: absolute;
          top: 48%;
          right: 15px;
        "
      >
      </i>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  save,
  delTable,
  info,
  update,
} from "@/api/platform/distributionService";
import ImageUpload from "@/components/ImageUpload/index";

import FileUpload from "@/components/FileUpload/index";
export default {
  name: "app",
  components: { ImageUpload, FileUpload },
  data() {
    return {
      loading: false,
      nowAddress: [], //临时保存地址
      fixedAddress: "https://txyoss.oss-cn-shenzhen.aliyuncs.com",
      currentIndex: "", //当前图片的下标
      fileCount: 0,
      picNum: 0,
      maxNum: 5,
      photoList: [], //照片列表
      dialogImageUrl: "",
      dialogVisible: false,
      disabled: false,
      total: 0,
      listData: [],
      ruleForm: {
        pageSize: 10,
        pageNum: 1,
      },
      from: {
        productDetails: [], //图文详情
        productName: "", //商品昵称
        productDescription: "", //商品描述
        productPhotoUrl: [], //商品照片,
        videos: [],
        tag: [],
        productPrice: "",
      },
      inputValue: "",
      inputVisible: false,
      labelList: [],
      addShow: false,
      deleteDialogVisible: false,
      deliveryId: "",
      // rules: {
      //     productName: [
      //       {required: true,message: '请填写商品昵称', trigger: 'blur' }
      //     ],
      //     productDescription: [
      //       {required: true,message: '请填写商品描述', trigger: 'blur' }
      //     ],
      //     productDetails: [
      //       {required: true,message: '请上传图文详情', trigger: 'blur' }
      //     ],
      //     productPhotoUrl: [
      //       {required: true,message: '请上传商品照片', trigger: 'blur' }
      //     ],
      //     tag: [
      //       {required: true,message: '请添加标签', trigger: 'blur' }
      //     ],
      // }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val;
      this.getList();
    },
    handleUpdate(row) {
      //编辑
      this.deliveryId = row.deliveryId;
      this.addShow = true;
      this.info(row.deliveryId);
      (this.from = {
        productDetails: [], //图文详情
        productName: "", //商品昵称
        productDescription: "", //商品描述
        productPhotoUrl: [], //商品照片,
        videos: [],
        tag: [],
        productPrice: "",
      }),
        (this.labelList = []);
    },
    handleDelete(row) {
      //删除
      this.deliveryId = row.deliveryId;
      this.deleteDialogVisible = true;
    },
    confirmDelete() {
      //确定删除
      delTable(this.deliveryId).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.deleteDialogVisible = false;
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    addCheckManagement() {
      //签到管理
      (this.from = {
        productDetails: [], //图文详情
        productName: "", //商品昵称
        productDescription: "", //商品描述
        productPhotoUrl: [], //商品照片,
        videos: [],
        tag: [],
        productPrice: "",
      }),
        (this.labelList = []);
      this.addShow = true;
    },
    getList() {
      //列表
      this.loading = true;
      page(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.rows;
          this.total = res.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    info(deliveryId) {
      //查询月子套房信息
      info(deliveryId).then((res) => {
        if (res.code == 200) {
          this.from = res.data;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    submitForm(formName) {
      let from = this.from;
      if (from.productPhotoUrl.length == 0) {
        this.$message("请上传商品照片");
        return;
      }
      if (from.tag.length == 0) {
        this.$message("请添加商品标签");
        return;
      }
      if (from.productName == "") {
        this.$message("请填写商品名称");
        return;
      }
      if (from.productDetails.length == 0) {
        this.$message("请上传图文详情");
        return;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.deliveryId) {
            this.$confirm("确定修改膳食配送吗, 是否继续?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                if (this.from.videos == "") {
                  this.from.videos = [];
                }
                update(this.from).then((res) => {
                  if (res.code == 200) {
                    this.$message(res.msg);
                    this.addShow = false;
                    this.getList();
                    return;
                  } else {
                    this.$message(res.msg);
                  }
                });
              })
              .catch(() => {
                this.$message({
                  type: "info",
                  message: "已取消修改",
                });
              });
          }

          if (!this.deliveryId) {
            this.$confirm("确定新增该膳食配送吗, 是否继续?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                save(this.from).then((res) => {
                  if (res.code == 200) {
                    this.$message(res.msg);
                    this.addShow = false;
                    this.getList();
                    this.from = {
                      productDetails: [], //图文详情
                      productName: "", //商品昵称
                      productDescription: "", //商品描述
                      productPhotoUrl: [], //商品照片,
                      videos: [],
                    };
                    return;
                  } else {
                    this.$message(res.msg);
                  }
                });
              })
              .catch(() => {
                this.$message({
                  type: "info",
                  message: "已取消新增",
                });
              });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    add() {
      if (this.labelName == "") {
        this.$message("请输入标签");
        return;
      }
      let data = {
        name: this.labelName,
      };
      this.labelList.push(data);
      this.labelName = "";
    },
    handleClose(tag) {
      this.labelList.splice(this.labelList.indexOf(tag), 1);
    },
    handleCloses(tag) {
      this.from.tag.splice(this.from.tag.indexOf(tag), 1);
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.labelList.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
    choice(e) {
      this.from.tag.forEach((item) => {
        if (item == e) {
          this.$message("标签已添加");
          black;
        }
        if (this.from.tag.length > 1) {
          this.$message("最多添加两个");
          black;
        }
      });
      this.from.tag.push(e);
    },
  },
};
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .title {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
  padding: 24px 20px;
  .title2 {
    font-size: 20px;
    color: #17191a;
    margin-bottom: 24px;
  }
  .table {
    margin-top: 20px;
  }
}
.block {
  text-align: right;
  margin-top: 20px;
}
.imgHint {
  display: flex;
  align-items: center;
}
.productDescription {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  -ms-text-overflow: ellipsis;
  text-overflow: ellipsis;
}
.BackgroundPicture {
  display: flex;
  margin-top: 20px;
  align-items: center;
  width: 100%;
  .uploadTitle {
    width: 7%;
    display: flex;
    color: #17191a;
    font-size: 14px;
    padding: 0 12px 0 0;
    text-align: left;
  }
  .upload {
    width: 90%;
  }
  .el-upload--picture-card {
    width: 50px;
    height: 50px;
  }
}
.label {
  width: 368px;
  height: 86px;
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  display: flex;
  flex-wrap: wrap;
  padding: 10px 10px;
  .labelName {
    color: #7c7d81;
    font-size: 14px;
    height: 24px;
    border-radius: 3px;
    line-height: 24px;
    background: #ebebeb;
    margin-right: 16px;
    padding: 0 8px;
  }
  .inputLabel {
    width: 156px;
    font-size: 12px;
  }
}
.tagTitle {
  margin-right: 5px;
}
</style>
