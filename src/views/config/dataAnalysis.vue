<template>
    <div class="app-container home" id="app">
        <div class="dataDisplay">
               <div class="dataDisplay1" @click="totalAccess">
                <div class="dataDisplay1_1">总访用户量</div>
                <div class="evaluationManagement3">
                <img src="http://cdn.xiaodingdang1.com/2024/04/15/9fa2b8690d80451baeb0a4d29cd53dc0png" alt="" style="width: 88px;height: 88px;">
                <div class="evaluationManagement3_1">
                <div class="evaluationManagement3_2">总访用户量</div>
                <div class="evaluationManagement3_3">{{totalList.userTotal}}人</div>
                </div>
                </div>
                <img src="http://cdn.xiaodingdang1.com/2024/04/17/e412703e9e9442fc96c3a4f6ce145e32png" alt="" class="img1">
                <img src="http://cdn.xiaodingdang1.com/2024/04/17/a540d5aa4add467d979ab3a0d0edaf1cpng" alt="" class="img2">
               </div>
               <div class="dataDisplay1" @click="TotalBrowsingTime">
                <div class="dataDisplay1_1">浏览总时间</div>
                <div class="evaluationManagement3">
          <img src="http://cdn.xiaodingdang1.com/2024/04/15/48faf4f00cd4438ab2aa4721789831efpng" alt="" style="width: 88px;height: 88px;">
          <div class="evaluationManagement3_1">
            <div class="evaluationManagement3_2">浏览总时间</div>
            <div class="evaluationManagement3_3">{{totalList.visitTimeTotalStr}}</div>
          </div>
        </div>
        <img src="http://cdn.xiaodingdang1.com/2024/04/17/e412703e9e9442fc96c3a4f6ce145e32png" alt="" class="img1">
                <img src="http://cdn.xiaodingdang1.com/2024/04/17/a540d5aa4add467d979ab3a0d0edaf1cpng" alt="" class="img2">
               </div>
               <div class="dataDisplay1" @click="totalVisits">
                <div class="dataDisplay1_1">总访问次数</div>
                <div class="evaluationManagement3">
          <img src="http://cdn.xiaodingdang1.com/2024/04/15/81df4c0dfa944216a791981444c626d8png" alt="" style="width: 88px;height: 88px;">
          <div class="evaluationManagement3_1">
            <div class="evaluationManagement3_2">总访问次数</div>
            <div class="evaluationManagement3_3">{{totalList.visitCountTotal}}次</div>
          </div>
        </div>
        <img src="http://cdn.xiaodingdang1.com/2024/04/17/e412703e9e9442fc96c3a4f6ce145e32png" alt="" class="img1">
                <img src="http://cdn.xiaodingdang1.com/2024/04/17/a540d5aa4add467d979ab3a0d0edaf1cpng" alt="" class="img2">
               </div>
        </div>
         <div class="dataStatistics">
          <div class="dataStatistics1">
            <div class="dataStatistics1_1">数据统计</div>
            <div class="dataStatistics2">
              <ul class="dataStatistics2_1">
                <li :class="tabIndex==index?'StatisticsActive':'dataStatistics2_4'" v-for="item,index in listTab" :key="index" @click="tab(index)">{{ item }}</li>
              </ul>
              <el-date-picker
      v-model="startDate"
      type="date"
      value-format="yyyy-MM-dd HH:mm:ss"
      placeholder="选择开始时间" class="dataStatistics2_2">
    </el-date-picker>
    <el-date-picker
    v-model="endDate"
      type="date"
      value-format="yyyy-MM-dd HH:mm:ss"
      placeholder="选择结束时间" class="dataStatistics2_2">
    </el-date-picker>
    <el-button type="primary" @click="inquire">查询</el-button>
              <!-- <p class="dataStatistics2_3">查看更多</p> -->
            </div>
          </div>

          <div class="dataStatistics3">
           <div class="dataStatistics3_1">
              <div class="dataStatistics3_1_1">
                <div class="dataStatistics3_1_2">访客数</div>
                <el-tooltip class="item" effect="dark" content="一天之内到底有多少不同的用户访问了你的小程序。" placement="top">
                <img src="http://cdn.xiaodingdang1.com/2024/04/15/fe3baf5baa76452d92c457eeda2c8686png" alt="" style="width: 16px;height: 16px;">
              </el-tooltip>
              </div>
              <div class="dataStatistics3_2">{{listData.uv}}</div>
              <div class="dataStatistics3_3">
                <p class="dataStatistics3_3_1">较前一日</p>
                <div class="dataStatistics3_3_2">
                  <img src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png" alt="" style="width: 8px;height: 16px;" v-if="listData.pvDayBeforeType==0">
                  <img src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png" alt="" style="width: 8px;height: 16px;" v-if="listData.pvDayBeforeType==1">
                  <p class="dataStatistics3_3_3" v-if="listData.pvDayBeforeType==0">{{listData.pvDayBeforeRate}}人</p>
                  <p id="dataStatistics3_3_3" v-if="listData.pvDayBeforeType==1">{{listData.pvDayBeforeRate}}人</p>
                </div>
              </div>
           </div>
           <div class="dataStatistics3_1">
              <div class="dataStatistics3_1_1">
                <div class="dataStatistics3_1_2">浏览量</div>
                <el-tooltip class="item" effect="dark" content="小程序页面的浏览次数" placement="top">
                <img src="http://cdn.xiaodingdang1.com/2024/04/15/fe3baf5baa76452d92c457eeda2c8686png" alt="" style="width: 16px;height: 16px;">
              </el-tooltip>
              </div>
              <div class="dataStatistics3_2">{{listData.pv}}</div>
              <div class="dataStatistics3_3">
                <p class="dataStatistics3_3_1">较前一日</p>
                <div class="dataStatistics3_3_2">
                  <img src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png" alt="" style="width: 8px;height: 16px;" v-if="listData.pvDayBeforeType==0">
                  <img src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png" alt="" style="width: 8px;height: 16px;" v-if="listData.pvDayBeforeType==1">
                  <p class="dataStatistics3_3_3" v-if="listData.pvDayBeforeType==0">{{listData.avgUvDayBeforeRate}}人</p>
                  <p id="dataStatistics3_3_3" v-if="listData.pvDayBeforeType==1">{{listData.avgUvDayBeforeRate}}人</p>
                </div>
              </div>
           </div>
           <div class="dataStatistics3_1">
              <div class="dataStatistics3_1_1">
                <div class="dataStatistics3_1_2">人均浏览量</div>
                <el-tooltip class="item" effect="dark" content="统计时间内浏览量与访客数的比率" placement="top">
                <img src="http://cdn.xiaodingdang1.com/2024/04/15/fe3baf5baa76452d92c457eeda2c8686png" alt="" style="width: 16px;height: 16px;">
              </el-tooltip>
              </div>
              <div class="dataStatistics3_2">{{listData.avgUv}}</div>
              <div class="dataStatistics3_3">
                <p class="dataStatistics3_3_1">较前一日</p>
                <div class="dataStatistics3_3_2">
                  <img src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png" alt="" style="width: 8px;height: 16px;" v-if="listData.avgUvDayBeforeType==0">
                  <img src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png" alt="" style="width: 8px;height: 16px;" v-if="listData.avgUvDayBeforeType==1">
                  <p class="dataStatistics3_3_3" v-if="listData.avgDurationChangeType==0">{{listData.avgUvDayBeforeRate}}人</p>
                  <p id="dataStatistics3_3_3" v-if="listData.avgUvDayBeforeType==1">{{listData.avgUvDayBeforeRate}}人</p>
                </div>
              </div>
           </div>
           <div class="dataStatistics3_1">
              <div class="dataStatistics3_1_1">
                <div class="dataStatistics3_1_2">跳失率</div>
                <el-tooltip class="item" effect="dark" content="跳失率 = 只浏览了一个页面的访客数 / 总访客数" placement="top">
                <img src="http://cdn.xiaodingdang1.com/2024/04/15/fe3baf5baa76452d92c457eeda2c8686png" alt="" style="width: 16px;height: 16px;">
                </el-tooltip>
              </div>
              <div class="dataStatistics3_2">{{listData.lossRate}}</div>
              <div class="dataStatistics3_3">
                <p class="dataStatistics3_3_1">较前一日</p>
                <div class="dataStatistics3_3_2">
                  <img src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png" alt="" style="width: 8px;height: 16px;" v-if="listData.lossDayBeforeType==0">
                  <img src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png" alt="" style="width: 8px;height: 16px;" v-if="listData.lossDayBeforeType==1">
                  <p class="dataStatistics3_3_3" v-if="listData.lossDayBeforeType==0">{{listData.lossDayBeforeRate}}人</p>
                  <p id="dataStatistics3_3_3" v-if="listData.lossDayBeforeType==1">{{listData.lossDayBeforeRate}}人</p>
                </div>
              </div>
           </div>
           <div class="dataStatistics3_1">
              <div class="dataStatistics3_1_1">
                <div class="dataStatistics3_1_2">平均停留时长</div>
                <el-tooltip class="item" effect="dark" content="访客浏览某一页面时所花费的平均时长" placement="top">
                <img src="http://cdn.xiaodingdang1.com/2024/04/15/fe3baf5baa76452d92c457eeda2c8686png" alt="" style="width: 16px;height: 16px;">
              </el-tooltip>
              </div>
              <div class="dataStatistics3_2">{{listData.avgDurationStr}}</div>
              <div class="dataStatistics3_3">
                <p class="dataStatistics3_3_1">较前一日</p>
                <div class="dataStatistics3_3_2">
                  <img src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png" alt="" style="width: 8px;height: 16px;" v-if="listData.avgDurationChangeType==0">
                  <img src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png" alt="" style="width: 8px;height: 16px;" v-if="listData.avgDurationChangeType==1">
                  <p class="dataStatistics3_3_3" v-if="listData.avgDurationChangeType==0">{{listData.avgDurationSecondsRate}}人</p>
                  <p id="dataStatistics3_3_3" v-if="listData.avgDurationChangeType==1">{{listData.avgDurationSecondsRate}}人</p>
                </div>
              </div>
           </div>
           
        </div>
  </div>
  <div class="information">
    <p class="dataStatistics1_1">今日访客</p>
      <div id="chartLineBox" style="width: 100%;height:349px"> </div>
  </div>
  <div class="table">
     <p class="dataStatistics1_1">访客列表</p>
     <div class="content">
           <el-table :data="analysePage" class="table">
    <el-table-column label="用户姓名" prop="nickname" :show-overflow-tooltip="true" width="150" align="center" />
    <el-table-column label="电话" prop="tel" :show-overflow-tooltip="true" width="150" align="center" >
  </el-table-column>
    <el-table-column label="访问次数" prop="pv" width="200" align="center" />
    <el-table-column label="停留时间" prop="viewTime" width="200" align="center" />
    <el-table-column label="预产期" prop="dueDate" width="200" align="center" />
    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
      <template slot-scope="scope">
        <el-button
          size="mini"
          type="text"
          @click="details(scope.row)"
        >详情</el-button>
      </template>
    </el-table-column>
  </el-table>
  <div class="block">
  <el-pagination
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
    :page-sizes="[10, 20, 30, 40]"
    :page-size="100"
    layout="total, sizes, prev, pager, next, jumper"
    :total="total">
  </el-pagination>
</div>
      </div>
  </div>
    </div>
  </template>
  
  <script>
  import * as echarts from 'echarts'
  import { totalVisit,totalList,analysePage } from "@/api/platform/index";
  export default {
    name: "Index",
    data() {
      return {
        total:0,
        analysePage:[],
        totalList:'',
        startDate:null,//开始时间
      endDate:null,//结束时间
      ruleForm:{
      },
        tabIndex:0,
        listTab:['日','周','月'],
        listData:[
            {
                productName:'1111111'
            }
        ],
        // 版本号
        version: "3.8.7",
        input:'',
        value1:'',
        from:{
          pageSize:10,
          pageNum:1
        }
      };
    },
    created(){
      this.totalVisit()
      this.gettotalList()
      this.analysePages()
    },
    methods: {
      handleSizeChange(val) {
      this.from.pageSize=val
      this.analysePages()
      },
      handleCurrentChange(val) {
        this.from.pageNum=val
        this.analysePages()
      },
      analysePages(){
        analysePage(this.from).then(res => {
                if(res.code==200){
                    this.analysePage=res.rows
                    this.total=res.total
                    this.$message(res.msg);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
      },
      gettotalList(){
        totalList().then(res => {
                if(res.code==200){
                    this.totalList=res.data
                    this.$message(res.msg);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
      },
      getEcharts(){
      this.chartLine = echarts.init(document.getElementById('chartLineBox'));
        // 指定图表的配置项和数据
        var option = {
            tooltip: {              //设置tip提示
                trigger: 'axis'
            },
            color: ['#3058FF', '#3F85FF'],       //设置区分（每条线是什么颜色，和 legend 一一对应）
            xAxis: {
    type: 'category',
    boundaryGap: false,
    data: this.eventTime,
    smooth: true
  },
  yAxis: {
    type: 'value'
  },
            series: [
            {
      data: this.uv,
      type: 'line',
      areaStyle: {},
      itemStyle: {  
                normal: { //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1,[{
                            offset: 0.2, color: '#3F85FF' // 0% 处的颜色
                        }, {
                            offset: 0.3, color: '#3F85FF' // 100% 处的颜色
                        }, {
                            offset: 1, color: '#fff' // 100% 处的颜色
                        }]
                    ), //背景渐变色    
                    lineStyle: {        // 系列级个性化折线样式  
                        width: 2,  
                        type: 'solid',  
                        color: "#3F85FF" //折线的颜色
                    }  
                },  
    },
            // 设置折线弧度，取值：0-1之间
            smooth: 0.5,
  }
          ]
        };
 
        // 使用刚指定的配置项和数据显示图表。
        this.chartLine.setOption(option);
    },
      tab(index){
     this.tabIndex=index
     this.totalVisit()
      this.startDate=null//开始时间
     this.endDate=null//结束时间
      },
      inquire(){
     this.totalVisit()

    },
    totalVisit(){//查询
      if(this.startDate&&this.endDate){
        this.ruleForm.queryType='custom'
      }else{
        this.ruleForm.queryType=this.tabIndex==0?'day':this.tabIndex==1?'week':this.tabIndex==2?'month':''
      }
      this.ruleForm.startDate=this.startDate
      this.ruleForm.endDate=this.endDate
      totalVisit(this.ruleForm).then(res => {
                if(res.code==200){
                    this.listData=res.data
                    this.$message(res.msg);
                    let eventTime=[]
                    let uv=[]
                    res.data.userAccessAnalysisStats.forEach(item => {
                      eventTime.push(item.eventTime)
                      uv.push(item.uv)
                    });
                    this.eventTime=eventTime
                    this.uv=uv
                    console.log(eventTime,uv);
                    this.getEcharts()
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
        },
      goTarget(href) {
        window.open(href, "_blank");
      },
      details(row){//用户详情
        this.$router.push({
                        path: "/dataAnalysisDetails", query: {
                          userId:row.userId
                        },
                    });
      },
      totalAccess(){//总访用户量
        this.$router.push({
                        path: "/totalAccess", query: {},
                    });
      },
      TotalBrowsingTime(){//浏览总时间
        this.$router.push({
                        path: "/totalBrowsingTime", query: {},
                    });
      },
      totalVisits(){//总访问次数
        this.$router.push({
                        path: "/totalVisits", query: {},
                    });
      }
    },
    mounted(){
      },
  };
  </script>
  
  <style scoped lang="scss">
  #app{
    background: #F0F1F5;
      padding-bottom: 40px;
      padding: 20px 20px;
  }
  .dataDisplay{
    display: flex;
    justify-content: space-between;
    // line-height: 0;
    .dataDisplay1{
    background-image: linear-gradient(#3F85FF, #0054E8);
    border-radius: 10px;
    width: 32%;
    padding: 24px 24px;
    position: relative;
    .dataDisplay1_1{
        color: #FFFFFF;
        font-size: 18px;
    }
    .img1{
      position:absolute;
      width: 201px;
      height: 121px;
      top: 0;
      right: 0;
      z-index: 99;
    }
    .img2{
      position:absolute;
      width:  151px;
      height: 91px;
      bottom: 0;
      right: 0;
      z-index: 99;
    }
    .evaluationManagement3{
      display: flex;
      align-items: center;
      .evaluationManagement3_1{
        margin-left: 20px;
        z-index: 1000;
        .evaluationManagement3_2{
          font-size: 14px;
          color: #FFFFFF;
        }
        .evaluationManagement3_3{
          font-size: 30px;
          color: #FFFFFF;
          font-weight: bold;
          padding-top: 4px;
          z-index: 1000;
        }
      }
    }
    }
  }
  .dataStatistics{
    background: #fff;
    padding: 20px 20px;
    width: 100%;
    margin-top: 20px;
    border-radius: 10px;
    .dataStatistics1{
      display: flex;
      justify-content: space-between;
      .dataStatistics1_1{
        color: #17191A;
        font-size: 20px;
      }
    }
    .dataStatistics2{
      display: flex;
      align-items: center;
      .dataStatistics2_1{
        display: flex;
        list-style-type: none;
        .dataStatistics2_4{
          width: 38px;
          height: 25px;
          border: 1px solid #C8C9CD;
          color: #C8C9CD;
          text-align: center;
          line-height: 25px;
          border-radius: 2px;
          margin-right: 12px;
        }
        .StatisticsActive{
          width: 38px;
          height: 25px;
          background: #3F85FF;
          color: #FFFFFF;
          text-align: center;
          line-height: 25px;
          border-radius: 2px;
          margin-right: 12px;
        }
      }
      .dataStatistics2_2{
        margin-right: 12px;
      }
      .dataStatistics2_3{
        color: #3F85FF;
        font-size: 14px;
        margin-left: 10px;
      }
    }
    .dataStatistics3{
      display: flex;
      margin-top: 20px;
      .dataStatistics3_1{
        width: 19%;
        background: #fff;
        padding:12px 16px ;
        border: 1px solid #D9D9D9;
        border-radius: 5px;
        margin-left: 1%;
        .dataStatistics3_1_1{
          display: flex;
          align-items: center;
          line-height: 0;
          .dataStatistics3_1_2{
            color: #17191A;
            font-size: 14px;
            margin-right: 5px;
          }
        }
      }
      .dataStatistics3_2{
        color: #17191A;
        font-size: 30px;
        font-weight: bold;
        margin-top: 16px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        width: 100%;
      }
      .dataStatistics3_3{
        display: flex;
        line-height: 0;
        align-items: center;
        .dataStatistics3_3_1{
          color: #7C7D81;
          font-size:14px ;
        }
        .dataStatistics3_3_2{
          display: flex;
          margin-left: 22px;
          align-items: center;
          .dataStatistics3_3_3{
            color: #F84343;
            font-size: 14px;
            margin-left: 8px;
          }
          #dataStatistics3_3_3{
            color: #0BBD71;
            font-size: 14px;
            margin-left: 8px;
          }
        }
      }
    }
  }
  .information{
    margin-top: 20px;
    background: #fff;
      padding: 20px 20px;
      border-radius: 10px;
      width: 100%;
  }
  .table{
    margin-top: 20px;
    background: #fff;
      padding: 20px 20px;
      border-radius: 10px;
      width: 100%;
  }
  .block{
  text-align: right;
  margin-top: 20px;
}
  
  
  </style>
  
  