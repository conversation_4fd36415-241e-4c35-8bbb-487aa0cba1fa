<template>
  <div class="app">
    <!-- <div class="head">
          <div class="titles">话题管理</div>
          <div class="title1">Pages/宝妈社区/话题管理</div>
      </div> -->
    <div class="content">
      <div class="contentHead">
        <h2 class="title2"></h2>
        <div class="contentHeads">
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">角色</p>
              <el-select
                v-model="ruleForm.nurseId"
                placeholder="请选择人员"
                clearable
                style="width: 159px; margin-left: 10px"
              >
                <el-option
                  v-for="item in topicStatus"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">类型</p>
              <el-select
                v-model="ruleForm.typeId"
                placeholder="请选择类型"
                clearable
                style="width: 159px; margin-left: 10px"
              >
                <el-option
                  v-for="item in topicStatus1"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">标签</p>
              <el-input
                v-model="ruleForm.labelName"
                placeholder="请输入标签"
                clearable
              ></el-input>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">创建时间</p>
              <div style="display: flex; margin-left: 10px">
                <el-date-picker
                  v-model="ruleForm.startDate"
                  type="date"
                  placeholder="创建开始时间"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <el-date-picker
                  v-model="ruleForm.endDate"
                  type="date"
                  placeholder="创建结束时间"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </div>
            </div>
          </div>

          <el-button
            type="primary"
            style="margin-left: 8px; height: 36px"
            @click="inquire"
            >查询</el-button
          >
        </div>
        <el-button
          type="primary"
          style="height: 36px; margin: 20px 0 28px 0"
          @click="addMaternitySuite"
          >新增模板
        </el-button>
        <el-button
          type="primary"
          style="height: 36px; margin: 20px 0 28px 20px"
          @click="addStyle"
          >新增风格
        </el-button>
        <el-table v-loading="loading" :data="listData" class="table">
          <el-table-column label="序号" align="center">
            <template scope="scope">
              <span v-text="scope.$index + 1"></span>
            </template>
          </el-table-column>
          <el-table-column
            label="角色"
            prop="nurseName"
            :show-overflow-tooltip="true"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="类型"
            prop="typeName"
            :show-overflow-tooltip="true"
            align="center"
          >
          </el-table-column>
          <el-table-column label="标签" prop="labelName" align="center" />
          <el-table-column label="模版类型" prop="type" align="center" />
          <el-table-column
            label="文案风格"
            prop="contentStyle"
            align="center"
          />
          <el-table-column label="模板内容" prop="content" align="center">
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" align="center">
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="compile(scope.row)"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="block">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog title="删除" :visible.sync="deleteDialogVisible" width="30%">
      <span>是否删除此动态模板？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
      </span>
    </el-dialog>

    <el-dialog title="新增" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="角色" :label-width="formLabelWidth">
          <el-select
            v-model="nurseId"
            placeholder="请选择角色"
            @change="handleChange"
          >
            <el-option
              :label="item.dictLabel"
              :value="item.dictValue"
              v-for="(item, index) in topicStatus"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型" :label-width="formLabelWidth">
          <el-select
            v-model="typeId"
            placeholder="请选择动态人员"
            @change="handleChange1"
          >
            <el-option
              :label="item.dictLabel"
              :value="item.dictValue"
              v-for="(item, index) in topicStatus1"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标签" :label-width="formLabelWidth">
          <el-select v-model="form.labelName" placeholder="请选择标签">
            <el-option
              :label="item.nodeName"
              :value="item.nodeName"
              v-for="(item, index) in tabList"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模版类型" :label-width="formLabelWidth">
          <el-select
            v-model="form.type"
            placeholder="请选择模版类型"
            @change="handleChange2"
          >
            <el-option
              :label="item.dictLabel"
              :value="item.dictValue"
              v-for="(item, index) in templateList"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文案风格" :label-width="formLabelWidth">
          <el-select v-model="contentStyle" placeholder="请选择风格">
            <el-option
              :label="item.name"
              :value="item.name"
              v-for="(item, index) in listTemplateStyle"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板内容" :label-width="formLabelWidth">
          <el-input
            type="textarea"
            v-model="form.content"
            placeholder="请输入模板内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="getadd">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="新增风格" :visible.sync="dialogFormVisibles">
      <el-form :model="form">
        <el-form-item label="文案风格" :label-width="formLabelWidth">
          <el-input
            type="textarea"
            v-model="styleName"
            placeholder="请输入文案风格"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisibles = false">取 消</el-button>
        <el-button type="primary" @click="styleAdd">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  delTable,
  save,
  listByType,
  templateStyleAdd,
  templateStyle,
} from "@/api/platform/dynamicTemplate";
export default {
  name: "app",
  data() {
    return {
      contentStyle: "",
      tabList: [],
      formLabelWidth: "80px",
      typeId: "",
      nurseId: "",
      form: {
        labelName: "",
        content: "",
        type: "",
      },
      dialogFormVisible: false,
      loading: false,
      onlineStatus: [
        //在线状态
        {
          dictLabel: "已上线",
          dictValue: "1",
        },
        {
          dictLabel: "已下线",
          dictValue: "0",
        },
      ],
      templateList: [
        //模板类型
        {
          dictLabel: "服务笔记",
          dictValue: "服务笔记",
        },
        {
          dictLabel: "服务内容",
          dictValue: "服务内容",
        },
      ],
      topicStatus1: [
        //话题状态
        {
          dictLabel: "产后康复",
          dictValue: "CHKF",
        },
        {
          dictLabel: "月子膳食",
          dictValue: "YZSS",
        },
        {
          dictLabel: "护理服务",
          dictValue: "HLFW",
        },
        {
          dictLabel: "其他服务",
          dictValue: "QTFW",
        },
      ],
      topicStatus: [
        //话题状态
        {
          dictLabel: "护理",
          dictValue: "NURSE",
        },
        {
          dictLabel: "产康",
          dictValue: "POSTPARTUM",
        },
        {
          dictLabel: "厨师",
          dictValue: "CHEF",
        },
        {
          dictLabel: "月嫂",
          dictValue: "MATERNITY_NANNY",
        },
        {
          dictLabel: "销售",
          dictValue: "SALES",
        },
      ],
      total: 0,
      ruleForm: {
        pageSize: 10,
        pageNum: 1,
        typeId: "",
        nurseId: "",
        labelName: "",
        startDate: "",
        endDate: "",
      },
      deleteDialogVisible: false,
      dialogFormVisibles: false,
      listData: [],
      templateId: "",
      styleName: "",
      listTemplateStyle: [],
    };
  },
  watch: {
    // nurseId() {
    //   this.getlistByType();
    // },
    // typeId() {
    //   this.getlistByType();
    // },
  },
  created() {
    this.getList();
  },
  methods: {
    handleChange(value) {
      this.getlistByType();
      this.form.labelName = "";
      // 在这里处理选项变化的逻辑
    },
    handleChange1(value) {
      this.getlistByType();
      this.form.labelName = "";
      // 在这里处理选项变化的逻辑
    },
    inquire() {
      //查询
      this.getList();
    },
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val;
      this.getList();
    },
    handleDelete(row) {
      this.templateId = row.templateId;
      this.deleteDialogVisible = true;
    },
    confirmDelete() {
      //确定删除
      delTable(this.templateId).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.deleteDialogVisible = false;
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    addMaternitySuite() {
      //新增
      this.typeId = "";
      this.nurseId = "";
      this.form.labelName = "";
      this.form.content = "";
      this.templateId = "";
      this.dialogFormVisible = !this.dialogFormVisible;
      this.templateStyle();
    },
    addStyle() {
      this.dialogFormVisibles = !this.dialogFormVisibles;
    },
    getList() {
      //列表
      this.loading = true;
      page(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.rows;
          this.total = res.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    styleAdd() {
      if (this.styleName == "") {
        this.$message("请填写文案风格");
        return;
      }
      let data = {
        name: this.styleName,
      };
      templateStyleAdd(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.dialogFormVisibles = !this.dialogFormVisibles;
          this.styleName = "";
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    templateStyle() {
      //列表
      this.loading = true;
      templateStyle().then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listTemplateStyle = res.data;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    getadd() {
      //新增
      if (this.nurseId == "") {
        this.$message("请选择角色");
        return;
      }
      if (this.typeId == "") {
        this.$message("请选择类型");
        return;
      }
      if (this.form.labelName == "") {
        this.$message("请选择标签");
        return;
      }
      if (this.form.content == "") {
        this.$message("请填写模板内容");
        return;
      }
      this.loading = true;
      let data = {
        nurseId: this.nurseId,
        typeId: this.typeId,
        labelName: this.form.labelName,
        content: this.form.content,
        contentStyle: this.contentStyle,
        type: this.form.type,
      };
      if (this.templateId) {
        data.templateId = this.templateId;
      }
      save(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getList();
          this.dialogFormVisible = !this.dialogFormVisible;
          this.typeId = "";
          this.nurseId = "";
          this.form.labelName = "";
          this.form.content = "";
          this.templateId = "";
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    compile(row) {
      //编辑
      this.typeId = row.typeId;
      this.nurseId = row.nurseId;
      this.form.labelName = row.labelName;
      this.form.content = row.content;
      this.templateId = row.templateId;
      this.contentStyle = row.contentStyle;
      this.dialogFormVisible = !this.dialogFormVisible;
      this.form.type = row.type;
      this.getlistByType();
      this.templateStyle();
    },
    getlistByType() {
      this.loading = true;
      if (this.nurseId && this.typeId) {
        let data = {
          nurseType: this.typeId,
          nurseRole: this.nurseId,
        };
        listByType(data).then((res) => {
          if (res.code == 200) {
            this.tabList = res.data;
            this.loading = false;
            return;
          } else {
            this.$message(res.msg);
          }
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.app {
  background: #f0f1f5;
  padding-bottom: 30px;
}
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .titles {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  margin: 20px 20px;
  .contentHead {
    background: #ffff;
    padding: 5px 14px;
    border-radius: 10px;
    .title2 {
      color: #17191a;
    }
  }
}
.contentHeads {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.roomMessage {
  display: flex;
}
.sel {
  display: flex;
  align-items: center;
  margin-right: 10px;
  .selTitle {
    width: 30%;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
  }
}
#cars {
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  color: #7c7d81;
  font-size: 14px;
  width: 179px;
  height: 32px;
  margin-left: 6px;
}

.block {
  text-align: right;
  margin-top: 20px;
}
</style>
