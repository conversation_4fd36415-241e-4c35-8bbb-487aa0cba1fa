<template>
    <div class="app" v-loading="loading">
        <!-- <div class="head">
            <div class="titles">评价管理</div>
            <div class="title1">Pages/评价管理</div>
        </div> -->
        <div class="content">
        <div class="contentHead">
              <h2 class="title2">评价管理</h2>
                <div class="roomMessage">
                    <div class="sel">
                        <p class="selTitle">标签</p>
                        <el-select
              v-model="ruleForm.tag"
              placeholder="全部"
              clearable
              style="width: 139px;margin-left: 10px;"
            >
              <el-option
                v-for="item in tagList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictLabel"
              />
            </el-select> 
                    </div>
                    <div class="sel">
                        <p class="selTitle">评论星级</p>
                        <el-select
              v-model="ruleForm.overallRating"
              placeholder="全部"
              clearable
              style="width: 139px;margin-left: 10px;"
            >
              <el-option
                v-for="item in overallRatingList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              />
            </el-select>
                    </div>
                    <div class="sel">
                        <p class="selTitle">评论类型</p>
                        <el-select
              v-model="ruleForm.reviewType"
              placeholder="全部"
              clearable
              style="width: 139px;margin-left: 10px;"
            >
              <el-option
                v-for="item in evaluationType"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              />
            </el-select>
                    </div>
                </div>
                <div class="roomMessage">
                    <div class="sel">
                        <p class="selTitle">评论时间</p>
                        <el-date-picker
      v-model="ruleForm.startTime"
      type="datetime"
      placeholder="请选择评论时间"
      value-format="yyyy-MM-dd HH:mm:ss"
      style="margin:0 10px">
    </el-date-picker>
    <el-date-picker
      v-model="ruleForm.endTime"
      type="datetime"
      value-format="yyyy-MM-dd HH:mm:ss"
      placeholder="请选择评论时间">
    </el-date-picker>
    <el-button type="primary" style="margin-left: 8px;" @click="inquire">筛选</el-button>
                </div>
        </div>
    </div>
    <div class="conment">
        <div v-for="item,index in listData" :key="index" style="margin-bottom: 10px;">
         <div class="conment1">
            <img :src="item.avatar" alt="" style="width: 50px;height: 50px;">
            <div class="conment1_1">
                <p class="conmentTitle1">{{ item.nickname }}</p>
                <p class="conmentTitle2">{{ item.tag }}</p>
            </div>
         </div>
         <el-rate v-model="item.overallRating" style="margin-top: 10px;"></el-rate>
         <p class="editor">{{ item.content }}</p>
         <div class="picture">
                <img :rc="item" alt="" style="width: 100px;height: 100px;margin-right: 12px;" v-for="item,index in item.photos" :key="index">
         </div>
         <div class="release">
            <div class="releaseTime">发布于{{ item.createTime }}</div>
            <div>
                <el-button type="success" plain @click="reply">回复</el-button>
                <el-button type="danger" plain @click="remove(item.reviewId)">删除</el-button>
            </div>
         </div>
         <div class="textarea" v-if="addShow">
            <textarea v-model="item.replyContent" name="" id="" cols="30" rows="10" placeholder="请输入回复内容" style="background: #FAFAFD;border: none;width: 100%;height: 100px;padding: 10px 0px 10px 10px; "></textarea>
            <p class="add" @click="add(item.reviewId,item.replyContent)">提交</p>
         </div>
         <div class="releasematter" v-if="contentShow">
             <p class="releasematter1">商家回复:</p>
             <p class="releasematter2">感谢您的支持与信任，我们会再接再厉，为大家提供更优质的服务！</p>
             <div class="releasematter3" v-for="res,index in item.repliesList" :key="index">
                <p class="releasematter4">{{ res.replyContent}} {{ res.createTime }}</p>
                <p class="releasematter5" @click="remove1(res.replyId)">删除</p>
             </div>
         </div>
        </div>
    </div>
    </div>
</div>
</template>

<script>
import { page,replies,delTable,remove } from "@/api/platform/commentManagement";
export default {
    name: "app",
  data() {
    return {
        loading:false,
        addShow:false,
        contentShow:true,
        replyContent:'',//回复内容
        listData:[],
        ruleForm:{
        // pageSize:10,
        // pageNum:1,
        tag:'',//标签
        overallRating:'',//评价星级
        startTime:'',//评论开始时间
        endTime:'',//评论结束时间
        reviewType:''
      },
      evaluationType:[
      {
                    dictLabel: '房间评论',
                    dictValue: 'room'
                },
                {
                    dictLabel: '产康评论',
                    dictValue: 'recovery'
                },
                {
                    dictLabel: '套餐评论',
                    dictValue: 'package'
                },
      ],
      overallRatingList: [//评论星级
                {
                    dictLabel: '一星级',
                    dictValue: '1'
                },
                {
                    dictLabel: '二星级',
                    dictValue: '2'
                },
                {
                    dictLabel: '三星级',
                    dictValue: '3'
                },
                {
                    dictLabel: '四星级',
                    dictValue: '4'
                }, {
                    dictLabel: '五星级',
                    dictValue: '5'
                }
            ],
      tagList: [//标签
                {
                    dictLabel: '环境舒适',
                    dictValue: '1'
                },
                {
                    dictLabel: '服务周到',
                    dictValue: '2'
                },
                {
                    dictLabel: '团队专业',
                    dictValue: '3'
                },
                {
                    dictLabel: '悉心照料',
                    dictValue: '4'
                },
                {
                    dictLabel: '温馨呵护',
                    dictValue: '5'
                },
                {
                    dictLabel: '安心休养',
                    dictValue: '6'
                },
                {
                    dictLabel: '优雅静谧',
                    dictValue: '7'
                },
                {
                    dictLabel: '舒适宜居',
                    dictValue: '8'
                },
            ],
    }
},
created(){
  this.getList()
},
methods:{
    remove(reviewId){//整个评论删除
        this.$confirm('确定删除该评论吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
            delTable(reviewId).then(res=>{
                if(res.code==200){
                    this.$message(res.msg);
                    this.getList()
                return 
                }else{
                    this.$message(res.msg); 
                }
            })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
    },
    remove1(replyId){//单个评论删除
        this.$confirm('确定删除该评论吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
            remove(replyId).then(res=>{
                if(res.code==200){
                    this.$message(res.msg);
                    this.getList()
                return 
                }else{
                    this.$message(res.msg); 
                }
            })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
    },
    reply(){//回复
      this.addShow=!this.addShow
      this.contentShow=!this.contentShow
    },
    add(reviewId,replyContent){//回复提交
     if(replyContent==''){
        this.$message('请填写回复内容');
        return
     }
     let data={
        reviewId:reviewId,
        replyContent:replyContent
     }
     replies(data).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.addShow=!this.addShow
      this.contentShow=!this.contentShow
      this.replyContent=''
      this.getList()
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
    },
    inquire(){//查询
        this.getList()
    },
    getList(){//列表
        this.loading=true
        page(this.ruleForm).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.listData=res.data
                    // this.total=res.total
                    this.loading=false
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
      },
}
}
</script>

<style scoped lang="scss">
.app{
    background:#F0F1F5;
    padding-bottom: 30px;
}
.head{
    width: 100%;
    height: 80px;
    background: #ffff;
    color: #17191A;
    padding: 15px 20px;
    .titles{
        font-size: 22px;
        font-weight: bold;
    }
    .title1{
        font-size: 14px;
    }
}
.content{
    margin: 20px 20px;
    .contentHead{
    background: #ffff;
    padding: 5px 14px;
    border-radius: 10px;
    .title2{
        color: #17191A;
    }
}
}
.roomMessage{
    display: flex;
}
.sel{
    display: flex;
    align-items: center;
    margin-right: 10px;
    .selTitle{
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
}
}
#cars{
    border: 1px solid #DCDCDC;
    border-radius: 3px;
    color: #7C7D81;
    font-size: 14px;
    width: 179px;
    height: 32px;
    margin-left: 6px;
}
.tiem{
    margin-left: 8px;
}
.conment{
   margin-top: 20px;
   background: #ffff;
   border-radius: 10px;
   padding: 20px 20px;
   .conment1{
    display: flex;
    align-items: center;
    .conment1_1{
        margin-left: 12px;
    }
   }
   .conmentTitle1{
    font-size: 16px;
    color: #17191A;
   }
   .conmentTitle2{
    font-size: 14px;
    color: #7C7D81;
   }
   .editor{
    font-size: 14px;
    color: #45464A;
   }
}
.picture{
    display: flex;
}
.release{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.releaseTime{
    font-size: 14px;
    color: #7C7D81;
}
.releasematter{
    background: #FAFAFD;
    border-radius: 5px;
    padding: 20px 20px;
    margin-top: 10px;
    font-size: 14px;
    .releasematter1{
        color: #17191A;
    }
    .releasematter2{
        color: #45464A;
    }
    .releasematter3{
        display: flex;
        justify-content: space-between;
    }
    .releasematter4{
        color: #7C7D81;
    }
    .releasematter5{
        color: #7C7D81;
    }
}

.textarea{
   background: #FAFAFD;
   padding: 10px 0;
   margin-top: 10px;
   border-radius: 5px;
}
.add{
    color: #3F85FF;
    font-size: 14px;
    text-align: right;
    margin-right: 20px;
}
</style>