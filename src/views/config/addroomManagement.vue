<template>
  <div id="app">
    <!-- <div class="head">
             <div class="title">房间管理</div>
             <div class="title1">Pages/房间工单管理/新增房间</div>
         </div> -->
    <div class="content">
      <div class="contentTitle">
        {{ item.suiteName }}——{{ item.roomNumber }}
      </div>
      <div class="fgx"></div>
      <div class="content1">
        <el-form label-width="100px" class="demo-ruleForm">
          <el-form-item label="入住客户" prop="name">
            <el-select
              v-model="ruleForm.customerId"
              placeholder="请选择入住客户"
              clearable
              style="width: 180px; margin-left: 10px"
            >
              <el-option
                v-for="item in customerList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="入住日期">
            <el-date-picker
              v-model="ruleForm.checkinDate"
              type="datetime"
              placeholder="请选择入住日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 220px; margin-left: 10px"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="入住天数" prop="name">
            <!-- <el-select v-model="ruleForm.totalDaysBooked" placeholder="请选择入住天数" clearable style="width: 180px;margin-left: 10px;">
    <el-option
    v-for="item in totalDaysBookedList" :key="item.value"
                                :label="item.label" :value="item.value">
    </el-option>
  </el-select> -->
            <el-input
              v-model="ruleForm.totalDaysBooked"
              placeholder="请输入入住天数"
              style="width: 220px; margin-left: 10px"
            ></el-input>
          </el-form-item>

          <el-form-item label="服务人员" prop="name">
            <el-table
              :data="ruleForm.staffList"
              class="table"
              v-if="ruleForm.staffList.length > 0"
            >
              <el-table-column label="照片" width="120" align="center">
                <template slot-scope="scope">
                  <img
                    :src="scope.row.staffPhotos[0]"
                    style="width: 100px; height: 100px"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="姓名"
                prop="staffName"
                :show-overflow-tooltip="true"
                align="center"
              />
              <el-table-column
                label="职位"
                prop="staffPost"
                :show-overflow-tooltip="true"
                align="center"
              ></el-table-column>
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="houseDelete(scope.$index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
      </div>
      <el-button class="relevance" @click="relevance()"
        >关联服务人员<i class="el-icon-plus"></i
      ></el-button>
      <div class="btn">
        <el-button type="primary" @click="save">保存</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </div>
    <!--关联房型-->
    <el-dialog title="关联服务人员" :visible.sync="dialogUse" width="50%">
      <el-form label-width="100px" class="demo-ruleForm" style="display: flex">
        <el-form-item label="职位" prop="name">
          <el-select
            v-model="from.staffPost"
            placeholder="请选择职位"
            clearable
            style="width: 180px; margin-left: 10px"
          >
            <el-option
              v-for="item in staffPostList"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="from.staffName"
            placeholder="请输入姓名"
          ></el-input>
        </el-form-item>
        <el-button
          type="primary"
          @click="inquire"
          style="height: 36px; margin-left: 10px"
          >查询</el-button
        >
      </el-form>
      <!--关联服务人员-->
      <el-table
        :data="useList"
        class="tables"
        @selection-change="handleSelectionChange"
        :row-key="getHolidayRowKey"
        ref="useList"
      >
        <el-table-column label="头像" prop="staffPhotos" align="center">
          <template slot-scope="scope">
            <img :src="scope.row.staffPhotos[0]" min-width="70" height="70" />
          </template>
        </el-table-column>
        <el-table-column label="姓名" prop="staffName" align="center" />
        <el-table-column label="职位" prop="staffPost" align="center" />
        <el-table-column
          type="selection"
          reserve-selection="true"
          width="55"
          label="勾选"
        >
        </el-table-column>
      </el-table>

      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :total="total"
      >
      </el-pagination>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogUse = false">取 消</el-button>
        <el-button type="primary" @click="houseConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  customer,
  saveRoom,
  staffPage,
  roomInfo,
} from "@/api/platform/roomManagement";
export default {
  name: "app",
  data() {
    return {
      staffPostList: [
        {
          value: 1,
          label: "孕产厨师",
        },
        {
          value: 2,
          label: "孕产医师",
        },
        {
          value: 3,
          label: "孕产护士",
        },
        {
          value: 4,
          label: "孕产月嫂",
        },
        {
          value: 5,
          label: "孕产育婴员",
        },
        {
          value: 6,
          label: "孕产心理咨询师",
        },
        {
          value: 7,
          label: "孕产产后康复师",
        },
        {
          value: 8,
          label: "孕产健康管理师",
        },
        {
          value: 9,
          label: "孕产母婴护理师",
        },
      ],
      from: {
        pageSize: 10,
        pageNum: 1,
        staffName: "", //姓名
        staffPost: "", //职位
      },
      total: 1,
      totalDaysBookedList: [
        {
          value: "1",
          label: "一天",
        },
        {
          value: "2",
          label: "两天",
        },
        {
          value: "3",
          label: "三天",
        },
        {
          value: "4",
          label: "四天",
        },
        {
          value: "5",
          label: "五天",
        },
        {
          value: "6",
          label: "六天",
        },
        {
          value: "7",
          label: "七天",
        },
        {
          value: "8",
          label: "八天",
        },
      ],
      customerList: [],
      dialogUse: false,
      ruleForm: {
        customerId: "", //客户
        checkinDate: "", //入住日期
        totalDaysBooked: "", //总入住天数
        staffList: [],
        roomId: "", //房间id
      },
      houseTypeList: [
        {
          dictValue: "0",
          dictLabel: "未入住",
        },
        {
          dictValue: "1",
          dictLabel: "待入住",
        },
        {
          dictValue: "2",
          dictLabel: "已入住",
        },
      ],
      useList: [],
      multipleSelection: [],
      item: "",
    };
  },
  watch: {
    useList(row, o) {
      console.log(row);
      row.forEach((item) => {
        this.ruleForm.staffList.forEach((res) => {
          if (item.staffId == res.staffId) {
            this.$nextTick(() => {
              this.$refs.useList.toggleRowSelection(item, true);
            });
          }
        });
      });
    },
  },

  created() {
    let item = this.$route.query.item;
    let items = JSON.parse(item);
    this.item = items;
    this.roomInfo(items.roomId);
    this.customer();
  },
  methods: {
    getHolidayRowKey(row) {
      return row.staffId;
    },
    inquire() {
      this.staffPage();
    },
    houseDelete(index) {
      //删除
      this.ruleForm.staffList.splice(index, 1);
    },
    handleSizeChange(val) {
      this.from.pageSize = val;
      this.staffPage();
    },
    handleCurrentChange(val) {
      this.from.pageNum = val;
      this.staffPage();
    },
    roomInfo(roomId) {
      //查询房间信息
      roomInfo(roomId).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.ruleForm = res.data;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    staffPage() {
      //护理人员
      staffPage(this.from).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          let useList = res.rows;
          this.useList = useList;
          this.total = res.total;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    cancel() {
      this.$router.go(-1);
    },
    houseConfirm() {
      //确定
      this.dialogUse = false;
      this.ruleForm.staffList = this.multipleSelection;
    },
    relevance() {
      //关联服务人员
      this.dialogUse = true;
      this.staffPage();
    },
    save() {
      //保存
      let data = this.ruleForm;
      if (data.customerId == "") {
        this.$message("请选择入住客户");
        return;
      }
      if (data.checkinDate == "") {
        this.$message("请选择入住时间");
        return;
      }
      if (data.totalDaysBooked == "") {
        this.$message("请选择总入住天数");
        return;
      }
      if (data.staffList.length == 0) {
        this.$message("请关联服务人员");
        return;
      }
      this.ruleForm.roomId = this.item.roomId;
      saveRoom(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.$router.go(-1);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    customer() {
      customer().then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.customerList = res.data;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .title {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
  padding: 20px 20px 24px 20px;
  .contentTitle {
    color: #17191a;
    font-size: 20px;
    margin-top: 30px;
  }
  .fgx {
    width: 100%;
    height: 2px;
    background: #ebebeb;
    margin: 20px 0;
  }
  .input {
    width: 20%;
  }
  .content1 {
    margin-left: 50px;
  }
  .unit {
    margin-left: 6px;
  }
  .title5 {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
    .required {
      color: red;
    }
  }
}
.relevance {
  margin-top: 10px;
  margin-left: 150px;
}
.postpartum {
  display: flex;
  align-items: center;
}
.inputs {
  width: 80px;
  height: 32px;
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  margin-left: 16px;
}
.units {
  width: 30px;
  height: 32px;
  background: #dcdcdc;
  border: 1px solid #dcdcdc;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
}
.jia {
  font-size: 28px;
  margin: 0 8px;
}
.BackgroundPicture {
  display: flex;
  margin-top: 20px;
  .uploadTitle {
    display: flex;
    color: #17191a;
    font-size: 14px;
    width: 100px;
    justify-content: flex-end;
    padding: 0 12px 0 0;
  }
  .el-upload--picture-card {
    width: 50px;
    height: 50px;
  }
}
.btn {
  margin: 20px auto;
  text-align: center;
}
.dialogTitle {
  display: flex;
  align-items: center;
}
.tables {
  margin-bottom: 10px;
}
.auditContent {
  width: 100%;
  padding: 20px 24px;
  background: #ff6c11;
  border-radius: 6px;
  font-size: 14px;
  line-height: 14px;
}
.pass {
  display: flex;
  align-items: center;
  color: #000000;
}
.passCause {
  color: #000000;
  margin-left: 20px;
}
</style>
