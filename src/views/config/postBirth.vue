<template>
  <div class="app">
    <!-- <div class="head">
            <div class="title">产后康复配置</div>
            <div class="title1">Pages/产后康复配置</div>
        </div> -->
    <div class="content">
      <div class="title2">产后康复</div>
      <el-tabs v-model="ruleForm.isOnShelf" @tab-click="handleClick">
        <el-tab-pane label="已上架" name="1"></el-tab-pane>
        <el-tab-pane label="未上架" name="0"></el-tab-pane>
      </el-tabs>
      <el-button type="primary" @click="addpostBirth">新增</el-button>
      <el-table v-loading="loading" :data="listData" class="table">
        <el-table-column
          label="项目照片"
          prop="displayPhotos"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            <img
              :src="scope.row.displayPhotos[0]"
              min-width="70"
              height="70"
              style="width: 100px; height: 100px; border-radius: 5px"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="项目名称"
          prop="projectName"
          :show-overflow-tooltip="true"
          width="150"
          align="center"
        />
        <el-table-column
          label="项目ID"
          prop="projectId"
          :show-overflow-tooltip="true"
          width="150"
          align="center"
        >
        </el-table-column>
        <el-table-column
          label="审核状态"
          prop="isOnShelf"
          width="200"
          align="center"
        >
          <template slot-scope="scope">
            <p v-if="scope.row.auditStatus == 0" style="color: #ff6c11">
              待审核
            </p>
            <p v-if="scope.row.auditStatus == 1" style="color: #0bbd71">
              已通过
            </p>
            <p v-if="scope.row.auditStatus == 2" style="color: #f84343">
              已驳回
            </p>
          </template>
        </el-table-column>
        <el-table-column
          label="排序"
          prop="sortKey"
          width="150"
          align="center"
        />
        <el-table-column label="是否推荐" align="center" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.onHomepage"
              :active-value="true"
              :inactive-value="false"
              @change="homepage(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope" v-if="scope.row.roleId !== 1">
            <el-button size="mini" type="text" @click="examine(scope.row)"
              >查看</el-button
            >
            <el-button size="mini" type="text" @click="compile(scope.row)"
              >编辑</el-button
            >
            <el-button size="mini" type="text" @click="handleDelete(scope.row)"
              >删除</el-button
            >
            <el-button
              size="mini"
              type="text"
              @click="clickOnline(scope.row)"
              v-if="!scope.row.isOnShelf"
              >上架</el-button
            >
            <el-button
              size="mini"
              type="text"
              @click="showing(scope.row)"
              v-if="scope.row.isOnShelf"
              >下架</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog title="删除" :visible.sync="deleteDialogVisible" width="30%">
      <span>是否删除此产后康复？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { page, delTable, online, homepage } from "@/api/platform/postBirth";
export default {
  name: "app",
  data() {
    return {
      loading: false,
      projectId: "",
      total: 0,
      ruleForm: {
        pageSize: 10,
        pageNum: 1,
        isOnShelf: "1",
      },
      listData: [],
      activeName: "second",
      deleteDialogVisible: false,
      roleList: [
        {
          name: "超值套餐",
          price: "298000",
          status: 1,
          time: "2024年12月12日21:14:25",
          auditStatus: 1,
          show: 1,
        },
        {
          name: "超值套餐",
          price: "298000",
          status: 0,
          time: "2024年12月12日21:14:25",
          auditStatus: 0,
          show: 0,
        },
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    homepage(e) {
      //改变护理人员主页显示状态
      let data = {
        projectId: e.projectId,
        status: e.onHomepage,
      };
      homepage(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    handleSizeChange(val) {
      console.log(val);
      this.ruleForm.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val;
      this.getList();
    },
    handleDelete(row) {
      this.deleteDialogVisible = true;
      this.projectId = row.projectId;
    },
    addpostBirth() {
      //新增房型
      this.$router.push({ path: "/addpostBirth" });
    },

    handleClick(tab, event) {
      this.getList();
    },
    getList() {
      //列表
      this.loading = true;
      page(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.rows;
          this.total = res.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    examine(row) {
      //查看
      this.$router.push({
        path: "/addpostBirth",
        query: {
          projectId: row.projectId,
          forbidden: true,
        },
      });
    },
    compile(row) {
      //编辑
      this.$router.push({
        path: "/addpostBirth",
        query: {
          projectId: row.projectId,
        },
      });
    },
    clickOnline(row) {
      //上架
      let query = {
        id: row.projectId,
        status: true,
      };
      online(query).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    showing(row) {
      //下架
      let query = {
        id: row.projectId,
        status: false,
      };
      online(query).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    confirmDelete() {
      //确定删除
      delTable(this.projectId).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.deleteDialogVisible = false;
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .title {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
  padding: 24px 20px;
  .title2 {
    font-size: 20px;
    color: #17191a;
    margin-bottom: 24px;
  }
  .table {
    margin-top: 20px;
  }
}
.block {
  text-align: right;
  margin-top: 20px;
}
</style>
