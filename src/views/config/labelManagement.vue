<template>
  <div class="app">
    <!-- <div class="head">
          <div class="titles">话题管理</div>
          <div class="title1">Pages/宝妈社区/话题管理</div>
      </div> -->
    <div class="content">
      <div class="contentHead">
        <h2 class="title2">标签管理</h2>
        <div class="contentHeads">
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">标签名称</p>
              <el-input
                v-model="ruleForm.tagName"
                placeholder="请输入标签名称"
                clearable
              ></el-input>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">分类</p>
              <el-select
                v-model="ruleForm.onlineStatus"
                placeholder="请选择分类"
                clearable
                style="width: 159px; margin-left: 10px"
              >
                <el-option
                  v-for="item in onlineStatus"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <el-button
            type="primary"
            style="margin-left: 8px; height: 36px"
            @click="inquire"
            >查询</el-button
          >
        </div>
        <!-- <el-button
          type="primary"
          style="height: 36px; margin: 20px 0 28px 0"
          @click="addMaternitySuite"
          >新增话题</el-button
        > -->
        <el-table v-loading="loading" :data="listData" class="table">
          <el-table-column
            label="标签名称"
            prop="tagName"
            :show-overflow-tooltip="true"
            align="center"
          />
          <el-table-column label="描述" prop="tagDesc" align="center" />
          <el-table-column label="分类" prop="tagIntentLevel" align="center">
          </el-table-column>
          <el-table-column label="话术数量" align="center">
            <!-- <template slot-scope="scope">
              <p>{{ scope.row }}</p>
            </template> -->
          </el-table-column>
          <el-table-column label="解决方案" prop="topicEndTime" align="center">
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="compile(scope.row)"
                >编辑</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!-- <div class="block">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div> -->
        <div style="margin-top: 20px">
          <el-form label-width="80px" style="display: flex">
            <el-form-item label="">
              <el-input style="width: 200px" v-model="from.tagName"></el-input>
            </el-form-item>
            <el-form-item label="">
              <el-input style="width: 200px" v-model="from.tagDesc"></el-input>
            </el-form-item>
            <el-form-item label="">
              <el-select
                placeholder="请选择"
                style="width: 200px"
                v-model="from.tagIntentLevel"
              >
                <el-option label="高意向" value="高意向"></el-option>
                <el-option label="中意向" value="中意向"></el-option>
                <el-option label="低意向" value="低意向"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSubmit">确认添加</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="consultation">
      <h2 class="title2">详细咨询</h2>
      <div class="BackgroundPicture">
        <div class="uploadTitle">
          <p style="color: #f84343">*</p>
          <p>二维码配图</p>
        </div>
        <div class="upload">
          <div class="viewLocations">
            <image-upload
              :limit="1"
              :isShowTip="false"
              v-model="consultationImg"
            />
            <el-button
              type="primary"
              @click="updateConsultation"
              style="margin-left: 20px"
              >上传</el-button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  page,
  save,
  delTable,
  updateConsultation,
  getConsultation,
} from "@/api/platform/labelManagement";
export default {
  name: "app",
  data() {
    return {
      from: {
        tagName: "",
        tagDesc: "",
        tagIntentLevel: "",
      },
      loading: false,
      onlineStatus: [
        //在线状态
        {
          dictLabel: "高意向",
          dictValue: "高意向",
        },
        {
          dictLabel: "中意向",
          dictValue: "中意向",
        },
        {
          dictLabel: "低意向",
          dictValue: "低意向",
        },
      ],
      topicStatus: [
        //话题状态
        {
          dictLabel: "进行中",
          dictValue: "1",
        },
        {
          dictLabel: "待开始",
          dictValue: "0",
        },
        {
          dictLabel: "已结束",
          dictValue: "2",
        },
      ],
      total: 0,
      topicId: "",
      ruleForm: {
        pageSize: 10,
        pageNum: 1,
        tagName: "",
      },
      listData: [],
      consultationImg: "",
    };
  },
  created() {
    this.getList();
    this.getConsultation();
  },
  methods: {
    compile(row) {
      //编辑
      this.$router.push({
        path: "/labelManagementUpdate",
        query: { id: row.id },
      });
    },
    inquire() {
      //查询
      this.getList();
    },
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val;
      this.getList();
    },
    getList() {
      //列表
      this.loading = true;
      page(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.rows;
          this.total = res.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    getConsultation() {
      this.loading = true;
      getConsultation().then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.consultationImg = res.data;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    onSubmit() {
      //确认添加
      if (this.from.tagName == "") {
        this.$message("请填写标签名称");
        return;
      }
      if (this.from.tagDesc == "") {
        this.$message("请填写标签描述");
        return;
      }
      if (this.from.tagIntentLevel == "") {
        this.$message("请选择分类");
        return;
      }
      this.loading = true;
      save(this.from).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.loading = false;
          this.getList();
          this.from = {
            tagName: "",
            tagDesc: "",
            tagIntentLevel: "",
          };
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    updateConsultation() {
      this.loading = true;
      let consultationImg = {
        consultationImg: this.consultationImg[0],
      };
      updateConsultation(consultationImg).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.loading = false;
          this.consultationImg = "";
          this.getConsultation();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    handleDelete(row) {
      //删除
      this.loading = true;
      this.$confirm("确定删除该标签吗？, 是否删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delTable(row.id).then((res) => {
            if (res.code == 200) {
              this.$message(res.msg);
              this.loading = false;
              this.getList();
              return;
            } else {
              this.$message(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style scoped lang="scss">
.viewLocations {
  display: flex;
  align-items: center;
}
.BackgroundPicture {
  display: flex;
}
.upload {
  margin-left: 20px;
}
.uploadTitle {
  display: flex;
  align-items: center;
}
.consultation {
  padding: 20px 24px;
  background: #ffff;
  border-radius: 10px;
  margin: 0px 24px;
}
.app {
  background: #f0f1f5;
  padding-bottom: 30px;
}
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .titles {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  margin: 20px 20px;
  .contentHead {
    background: #ffff;
    padding: 5px 14px;
    border-radius: 10px;
    .title2 {
      color: #17191a;
      font-size: 18px;
      font-weight: bold;
    }
  }
}
.contentHeads {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}
.roomMessage {
  display: flex;
}
.sel {
  display: flex;
  align-items: center;
  margin-right: 10px;
  .selTitle {
    width: 30%;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
  }
}
#cars {
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  color: #7c7d81;
  font-size: 14px;
  width: 179px;
  height: 32px;
  margin-left: 6px;
}

.block {
  text-align: right;
  margin-top: 20px;
}
</style>
