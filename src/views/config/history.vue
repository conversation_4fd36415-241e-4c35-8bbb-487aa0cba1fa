<template>
    <div class="app">
        <!-- <div class="head">
            <div class="title">历史记录</div>
            <div class="title1">Pages/房间管理/历史记录</div>
        </div> -->
        <div class="content">
             <div class="title2">历史记录</div>
             <el-table :data="listData" class="table">
      <el-table-column label="入住人员姓名" prop="customerName" align="center" />
      <el-table-column label="手机号" prop="tel" align="center" />
      <el-table-column label="负责人员" prop="staffName"  align="center" ></el-table-column>
      <el-table-column label="房间号" prop="roomNumber"   align="center" />
      <el-table-column label="房型" prop="suiteName" align="center" ></el-table-column>
      <el-table-column label="入住时间" prop="checkinDate"  align="center" ></el-table-column>
      <el-table-column label="退房时间" prop="checkoutDate"  align="center" ></el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope" >
          <el-button
            size="mini"
            type="text"
            @click="examine(scope.row)"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="block">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="100"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>
  
  </div>
        </div>
    </div>
</template>

<script>
import { checkoutPage } from "@/api/platform/roomManagement";
export default {
    name: "app",
  data() {
    return {
      total:0,
      ruleForm:{
        pageSize:10,
        pageNum:1
      },
      listData:[]
    }
},
created(){
  this.getList()
},
methods:{
    handleSizeChange(val) {
      this.ruleForm.pageSize=val
      this.getList()
      },
      handleCurrentChange(val) {
        this.ruleForm.pageNum=val
        this.getList()
      },
      getList(){//列表
        checkoutPage(this.ruleForm).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.listData=res.rows
                    this.total=res.data.total
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
      },
      examine(row){//查看
        this.$router.push({ path: "/historyDetails", query: {
            checkoutId: row.checkoutId,
    }, });
      },
}
}
</script>

<style scoped lang="scss">
.head{
    width: 100%;
    height: 80px;
    background: #ffff;
    color: #17191A;
    padding: 15px 20px;
    .title{
        font-size: 22px;
        font-weight: bold;
    }
    .title1{
        font-size: 14px;
    }
}
.content{
    background: #ffff;
    margin: 10px 10px;
    border-radius: 5px;
    padding: 24px 20px;
    .title2{
        font-size: 20px;
        color: #17191A;
        margin-bottom: 24px;
    }
    .table{
        margin-top: 20px;
    }
}
.block{
    text-align: right;
    margin-top: 20px;
}
</style>