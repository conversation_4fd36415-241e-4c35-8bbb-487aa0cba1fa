<template>
    <div class="app">
        <!-- <div class="head">
            <div class="titles">话题管理</div>
            <div class="title1">Pages/宝妈社区/话题管理</div>
        </div> -->
        <div class="content">
        <div class="contentHead">
              <h2 class="title2">话题管理</h2>
              <div class="contentHeads">
              <div class="roomMessage">
                <div class="sel">
                        <p class="selTitle">话题名称</p>
                        <el-input v-model="ruleForm.topicName" placeholder="请输入内容" clearable></el-input>
                    </div>
              </div>
              <div class="roomMessage">
                    <div class="sel">
                        <p class="selTitle">选择时间</p>
                 <div style="display: flex;margin-left: 10px;">
                    <el-date-picker
      v-model="ruleForm.topicStartTime"
      type="date"
      placeholder="话题开始时间"
      value-format="yyyy-MM-dd"
      >
    </el-date-picker>
    <el-date-picker
      v-model="ruleForm.topicEndTime"
      type="date"
      placeholder="话题结束时间"
      value-format="yyyy-MM-dd" >
    </el-date-picker>
                 </div>
                </div>
        </div>
                <div class="roomMessage">
                    <div class="sel">
                        <p class="selTitle">上线状态</p>
                        <el-select v-model="ruleForm.onlineStatus" placeholder="请选择在线状态" clearable style="width: 159px;margin-left: 10px;">
    <el-option
    v-for="item in onlineStatus" :key="item.dictValue"
                                :label="item.dictLabel" :value="item.dictValue">
    </el-option>
  </el-select>
                    </div>
                    <div class="sel">
                        <p class="selTitle">话题状态</p>
                        <el-select v-model="ruleForm.topicStatus" placeholder="请选择话题状态" clearable
                            style="width: 159px;margin-left: 10px;">
    <el-option
    v-for="item in topicStatus" :key="item.dictValue"
                                :label="item.dictLabel" :value="item.dictValue">
    </el-option>
  </el-select>
                    </div>
                </div>
                <el-button type="primary" style="margin-left: 8px;height: 36px;" @click="inquire">查询</el-button>
            </div>
            <el-button type="primary" style="height: 36px;margin: 20px 0 28px 0;" @click="addMaternitySuite">新增话题</el-button>
            <el-table v-loading="loading" :data="listData"  class="table">
      <el-table-column label="话题名称" prop="topicName" :show-overflow-tooltip="true"  align="center" />
      <el-table-column label="话题状态" prop="topicStatus" :show-overflow-tooltip="true"  align="center" >
        <template slot-scope="scope">
            <p v-if="scope.row.topicStatus==2" style="color: #7C7D81;">已结束</p>
            <p v-if="scope.row.topicStatus==1" style="color: #0BBD71;">进行中</p>
            <p v-if="scope.row.topicStatus==0" style="color: #FF6C11;">未开始</p>
        </template>
    </el-table-column>
      <el-table-column label="开始时间" prop="topicStartTime"  align="center" />
      <el-table-column label="结束时间" prop="topicEndTime"  align="center" >
      </el-table-column>
      <el-table-column label="上线状态" prop="onlineStatus" align="center" >
        <template slot-scope="scope">
            <p v-if="scope.row.onlineStatus==1" style="color: #0BBD71;">在线</p>
            <p v-if="scope.row.onlineStatus==0">离线</p>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope" v-if="scope.row.roleId !== 1">
          <el-button
            size="mini"
            type="text"
            @click="examine(scope.row)"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            @click="compile(scope.row)"
          >编辑</el-button>
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            @click="clickOnline(scope.row)"
            v-if="!scope.row.onlineStatus"
          >上线</el-button>
          <el-button
            size="mini"
            type="text"
            @click="showing(scope.row)"
            v-if="scope.row.onlineStatus"
          >下线</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="block">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="100"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>
  </div>
    </div>
    </div>
    <el-dialog
  title="删除"
  :visible.sync="deleteDialogVisible"
  width="30%">
  <span>是否删除此话题？</span>
  <span slot="footer" class="dialog-footer">
    <el-button @click="deleteDialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
  </span>
</el-dialog>
</div>
</template>

<script>
import { page,delTable,online } from "@/api/platform/topicManagement";
export default {
    name: "app",
  data() {
    return {
      loading:false,
        onlineStatus: [//在线状态
                {
                    dictLabel: '已上线',
                    dictValue: '1'
                },
                {
                    dictLabel: '已下线',
                    dictValue: '0'
                }
            ],
            topicStatus: [//话题状态
                {
                    dictLabel: '进行中',
                    dictValue: '1'
                },
                {
                    dictLabel: '待开始',
                    dictValue: '0'
                },
                {
                    dictLabel: '已结束',
                    dictValue: '2'
                }
            ],
      total:0
,      topicId:'',
      ruleForm:{
        pageSize:10,
        pageNum:1,
        topicStatus:'',
        onlineStatus:'',
        topicStartTime:'',
        topicEndTime:''
      },
      deleteDialogVisible:false,
      listData:[]
    }
},
created(){
  this.getList()
},
methods:{
    inquire(){//查询
        this.getList()
    },
    handleSizeChange(val) {
      this.ruleForm.pageSize=val
      this.getList()
      },
      handleCurrentChange(val) {
        this.ruleForm.pageNum=val
        this.getList()
      },
      handleDelete(row){
        this.topicId=row.topicId
        this.deleteDialogVisible=true
      },
      confirmDelete(){//确定删除
        delTable(this.topicId).then(res=>{
          if(res.code==200){
                    this.$message(res.msg);
                    this.deleteDialogVisible=false
                   this.getList()
                return 
                }else{
                    this.$message(res.msg); 
                }
        })
      },
      addMaternitySuite(){//新增
        this.$router.push({ path: "/addtopicManagement" });
      },
      getList(){//列表
        this.loading=true
        page(this.ruleForm).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.listData=res.rows
                    this.total=res.total
                    this.loading=false
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
      },
      examine(row){//查看
        this.$router.push({ path: "/addtopicManagement", query: {
            topicId: row.topicId,
          forbidden:true
    }, });
      },
      compile(row){//编辑
        this.$router.push({ path: "/addtopicManagement", query: {
            topicId: row.topicId,
    }, });
      },
      clickOnline(row){//上线
        let query={
            topicId:row.topicId,
          status:true
        }
        online(query).then(res=>{
          if(res.code==200){
                    this.$message(res.msg);
                    this.getList()
                return 
                }else{
                    this.$message(res.msg); 
                }
        })
      },
      showing(row){//下线
        let query={
            topicId:row.topicId,
          status:false
        }
        online(query).then(res=>{
          if(res.code==200){
                    this.$message(res.msg);
                    this.getList()
                return 
                }else{
                    this.$message(res.msg); 
                }
        })
      }
}
}
</script>

<style scoped lang="scss">
.app{
    background:#F0F1F5;
    padding-bottom: 30px;
}
.head{
    width: 100%;
    height: 80px;
    background: #ffff;
    color: #17191A;
    padding: 15px 20px;
    .titles{
        font-size: 22px;
        font-weight: bold;
    }
    .title1{
        font-size: 14px;
    }
}
.content{
    margin: 20px 20px;
    .contentHead{
    background: #ffff;
    padding: 5px 14px;
    border-radius: 10px;
    .title2{
        color: #17191A;
    }
}
}
.contentHeads{
  width: 100%;
    display: flex;
    flex-wrap: wrap;
}
.roomMessage{
    display: flex;
}
.sel{
    display: flex;
    align-items: center;
    margin-right: 10px;
    .selTitle{
      width: 30%;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
}
}
#cars{
    border: 1px solid #DCDCDC;
    border-radius: 3px;
    color: #7C7D81;
    font-size: 14px;
    width: 179px;
    height: 32px;
    margin-left: 6px;
}

.block{
    text-align: right;
    margin-top: 20px;
}
</style>