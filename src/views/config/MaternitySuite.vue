<template>
  <div class="app">
    <!-- <div class="head">
            <div class="title">月子套房配置</div>
            <div class="title1">Pages/月子套房配置</div>
        </div> -->
    <div class="content">
      <div class="title2">月子套房</div>
      <el-button type="primary" @click="addMaternitySuite">新增房型</el-button>
      <el-table v-loading="loading" :data="listData" class="table">
        <el-table-column label="房间照片" align="center">
          <template slot-scope="scope">
            <img :src="scope.row.suitePhotos != null ? scope.row.suitePhotos[0] : ''" min-width="70" height="70"
              style="width: 100px;height: 100px;border-radius: 5px;" />
          </template>
        </el-table-column>
        <el-table-column label="房间ID" prop="suiteId" :show-overflow-tooltip="true" align="center" />
        <el-table-column label="房间名称" prop="roomName" :show-overflow-tooltip="true" align="center">
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" align="center" />
        <el-table-column label="房型" prop="roomType" align="center">
        </el-table-column>
        <el-table-column label="朝向" prop="orientation" align="center">
        </el-table-column>
        <el-table-column label="楼层" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.minFloor }}-{{ scope.row.maxFloor }}楼</p>
          </template>
        </el-table-column>
        <el-table-column label="面积" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.minArea }}-{{ scope.row.maxArea }}m²</p>
          </template>
        </el-table-column>
        <el-table-column label="在线状态" align="center">
          <template slot-scope="scope">
            <p v-if="scope.row.onlineStatus">在线</p>
            <p v-if="!scope.row.onlineStatus">离线</p>
          </template>
        </el-table-column>
        <el-table-column label="展示状态(仅可展示两条)" align="center">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.isShow" :active-value="true" :inactive-value="false"
              @change="homepage(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope" v-if="scope.row.roleId !== 1">
            <el-button size="mini" type="text" @click="examine(scope.row)">查看</el-button>
            <el-button size="mini" type="text" @click="compile(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
            <el-button size="mini" type="text" @click="clickOnline(scope.row)"
              v-if="!scope.row.onlineStatus">上线</el-button>
            <el-button size="mini" type="text" @click="showing(scope.row)" v-if="scope.row.onlineStatus">下线</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :page-sizes="[10, 20, 30, 40]" :page-size="100" layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </div>
    <el-dialog title="删除" :visible.sync="deleteDialogVisible" width="30%">
      <span>是否删除此优惠套餐？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { page, delTable, online, suiteShow } from "@/api/platform/suite";
export default {
  name: "app",
  data() {
    return {
      loading: false,
      total: 0
      , suiteId: '',
      ruleForm: {
        pageSize: 10,
        pageNum: 1
      },
      deleteDialogVisible: false,
      listData: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    homepage(e) {//改变月子套房主页显示状态
      let data = {
        suiteId: e.suiteId,
        status: e.isShow
      }
      suiteShow(data).then(res => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getList()
          return
        } else {
          this.$message(res.msg);
        }
      })
    },
    handleSizeChange(val) {
      this.ruleForm.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val
      this.getList()
    },
    handleDelete(row) {
      this.suiteId = row.suiteId
      this.deleteDialogVisible = true
    },
    confirmDelete() {//确定删除
      delTable(this.suiteId).then(res => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.deleteDialogVisible = false
          this.getList()
          return
        } else {
          this.$message(res.msg);
        }
      })
    },
    addMaternitySuite() {//新增房型
      this.$router.push({ path: "/addMaternitySuite" });
    },
    getList() {//列表
      this.loading = true
      page(this.ruleForm).then(res => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.rows
          this.total = res.total
          this.loading = false
          return
        } else {
          this.$message(res.msg);
        }
      });
    },
    examine(row) {//查看
      this.$router.push({
        path: "/addMaternitySuite", query: {
          suiteId: row.suiteId,
          forbidden: 'true',
          examine: 1
        },
      });
    },
    compile(row) {//编辑
      this.$router.push({
        path: "/addMaternitySuite", query: {
          suiteId: row.suiteId,
        },
      });
    },
    clickOnline(row) {//上线
      let query = {
        suiteId: row.suiteId,
        status: true
      }
      online(query).then(res => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getList()
          return
        } else {
          this.$message(res.msg);
        }
      })
    },
    showing(row) {//下线
      let query = {
        suiteId: row.suiteId,
        status: false
      }
      online(query).then(res => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getList()
          return
        } else {
          this.$message(res.msg);
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191A;
  padding: 15px 20px;

  .title {
    font-size: 22px;
    font-weight: bold;
  }

  .title1 {
    font-size: 14px;
  }
}

.content {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
  padding: 24px 20px;

  .title2 {
    font-size: 20px;
    color: #17191A;
    margin-bottom: 24px;
  }

  .table {
    margin-top: 20px;
  }
}

.block {
  text-align: right;
  margin-top: 20px;
}
</style>