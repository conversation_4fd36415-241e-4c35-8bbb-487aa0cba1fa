<template>
    <div id="app">
     <!-- <div class="head">
             <div class="title">话题管理</div>
             <div class="title1">Pages/宝妈社区/话题管理/新增话题</div>


             
         </div> -->
         <div class="content">
            <div class="content1">
                <div class="title5"><p class="required">*</p><p>话题名称</p></div>
                 <input type="text" placeholder="请输入话题名称" class="input" v-model="ruleForm.topicName" :disabled="forbidden">
            </div>
            <div class="content1">
                <div class="title5"><p class="required">*</p><p>话题简介</p></div>
                <el-input type="textarea" class="textarea" placeholder="请输入话题简介" v-model="ruleForm.topicDescription" :disabled="forbidden"></el-input>
            </div>
            <div class="content1">
                <div class="title5"><p class="required">*</p><p>话题照片</p></div>
                 <div class="upload">
                  <image-upload :limit="8" :isShowTip="false" v-model="ruleForm.topicPhotos" v-if="!forbidden"/>
                  <div v-if="forbidden">
                    <img :src="item" alt="" v-for="item,index in ruleForm.topicPhotos" :key="index" style="width: 146px;height: 146px;margin-right: 10px;    border: 1px solid #c0ccda;
    border-radius: 6px;">
                    </div>
                 </div>
            </div>
            <!-- <div class="content1">
                <div class="title5"><p class="required">*</p><p>话题视频</p></div>
                 <div class="upload">
                  <file-upload :limit="1" :isShowTip="false" v-model="ruleForm.topicPhotos" v-if="!forbidden"/>
                  <div v-if="forbidden">
                    <video  controls="controls" :src="item" alt="" v-for="item,index in ruleForm.videos" :key="index" style=" width: 146px;height: 146px;margin-right: 10px;border: 1px solid #c0ccda;
    border-radius: 6px;"></video>
                    </div>
                 </div>
            </div> -->
            <div class="content1">
                <div class="title5"><p class="required">*</p><p>话题时间</p></div>
               <div>
                <el-date-picker
      v-model="ruleForm.topicStartTime"
      type="date"
      placeholder="话题开始时间"
      value-format="yyyy-MM-dd HH:mm:ss"
      :disabled="forbidden">
    </el-date-picker>
    <el-date-picker
      v-model="ruleForm.topicEndTime"
      type="date"
      placeholder="话题结束时间"
      value-format="yyyy-MM-dd HH:mm:ss"
      :disabled="forbidden">
    </el-date-picker>
               </div>
            </div>
            <div class="content1">
                <div class="title5"><p class="required">*</p><p>上线时间</p></div>
                <div>
                <el-date-picker
      v-model="ruleForm.onlineStartTime"
      type="date"
      placeholder="上线开始时间"
      value-format="yyyy-MM-dd HH:mm:ss"
      :disabled="forbidden">
    </el-date-picker>
    <el-date-picker
      v-model="ruleForm.onlineEndTime"
      type="date"
      placeholder="上线结束时间"
      value-format="yyyy-MM-dd HH:mm:ss"
      :disabled="forbidden"
      >
    </el-date-picker>
               </div>
            </div>
         <div class="btn" v-if="!forbidden">
     <el-button type="primary" @click="confirm">保存</el-button>
 <el-button @click="cancel">取消</el-button>
 </div>
     </div>
            </div>
 </template>
 
 <script>
  import ImageUpload from "@/components/ImageUpload/index"
 import { save,info,update } from "@/api/platform/topicManagement";
 import FileUpload from "@/components/FileUpload/index"
 export default {
    components: {ImageUpload,FileUpload},
    name:'app',
    data(){
     return{
        topicId:'',
        forbidden:false,
    ruleForm:{
        videos:[],
        topicPhotos:[],//话题照片
        topicName:'',//话题名称
        topicStartTime:'',//话题开始时间
        topicEndTime:'',//话题结束时间
        onlineStartTime:'',//上线开始时间
        onlineEndTime:'',//上线结束时间
        topicDescription:''
        },
     }
    },
    created(){
   this.topicId=this.$route.query.topicId
   this.forbidden=this.$route.query.forbidden?true:false
   if(this.topicId){
    this.info(this.topicId)
   }
    },
    methods:{
        cancel(){
            this.$router.go(-1);
        },
      confirm(){
        let data=this.ruleForm
        
                if(data.topicName==''){
                    this.$message('请输入话题名称');
                    return
                }
                if(data.topicDescription==''){
                    this.$message('请输入话题简介');
                    return
                }
                if(data.topicPhotos.length==0){
                    this.$message('请上传话题照片');
                    return
                }
                if(data.topicStartTime==''){
                    this.$message('请选择话题开始时间');
                    return
                }
                if(data.topicEndTime==''){
                    this.$message('请选择话题结束时间');
                    return
                }
                if (!this.ruleForm.videos) {
                    this.ruleForm.videos=[]
            }
                if (!this.topicId) { 
            save(this.ruleForm).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.$router.go(-1);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
        }
            if (this.topicId) { 
            update(this.ruleForm).then(res => {
                if(res.code==200){
                    this.$router.go(-1);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
        }
      },
      info(topicId){//查询
            info(topicId).then(res => {
                if(res.code==200){
                    this.ruleForm=res.data
                    this.$message(res.msg);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
        },
    }
 }
 </script>
 
 <style scoped lang="scss">
 .head{
     width: 100%;
     height: 80px;
     background: #ffff;
     color: #17191A;
     padding: 15px 20px;
     .title{
         font-size: 22px;
         font-weight: bold;
     }
     .title1{
         font-size: 14px;
     }
 }
 .content{
    background: #ffff;
    margin: 10px 10px;
    border-radius: 5px;
    padding: 24px 50px;
    .title2{
        font-size: 20px;
        color: #17191A;
        margin-bottom: 24px;
    }
    .table{
        margin-top: 20px;
    }
    .title5{
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
    width:10%;
    .required{
        color: red;
    }
   }
   .content1{
    display: flex;
    align-items: center;
    margin: 20px 0 10px 0;
    .input{
        width: 240px;
        height: 32px;
        border: 1px solid #DCDCDC;
        border-radius: 3px;
    }
    .upload{
    }
   }
}
 .btn{
     margin: 20px auto;
     text-align: center;
 }
 #cars{
    border: 1px solid #DCDCDC;
    border-radius: 3px;
    color: #7C7D81;
    font-size: 14px;
    width: 129px;
    height: 32px;
    margin-left: 6px;
}
.postpartum{
    display: flex;
    align-items: center;
   
}
.inputs{
    width: 100px;
    height: 32px;
    border: 1px solid #DCDCDC;
    border-radius: 3px;
}
.units{
    width: 46px;
    height: 32px;
    background: #DCDCDC;
    border: 1px solid #DCDCDC;
    text-align: center;
    line-height: 32px;
    font-size: 14px;
}
.textarea{
 width: 368px;
}
 </style>