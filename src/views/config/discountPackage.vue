<template>
  <div class="app" v-loading="loading">
    <!-- <div class="head">
            <div class="title">优惠套餐配置</div>
            <div class="title1">Pages/优惠套餐配置</div>
        </div> -->
    <div class="content">
      <div class="title2">优惠套餐</div>
      <el-button type="primary" @click="addMaternitySuite">创建套餐</el-button>
      <el-table :data="listData" class="table">
        <el-table-column label="活动名称" prop="packageName" align="center" />
        <el-table-column
          label="活动价格"
          prop="packagePrice"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          label="在线状态"
          prop="onlineStatus"
          :show-overflow-tooltip="true"
          align="center"
        >
          <template slot-scope="scope">
            <p v-if="scope.row.onlineStatus == 1">在线</p>
            <p v-if="scope.row.onlineStatus == 0">离线</p>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" align="center" />
        <!-- <el-table-column
          label="审核状态"
          prop="auditStatus"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <p v-if="scope.row.auditStatus == 1">审核通过</p>
            <p v-if="scope.row.auditStatus == 0">审核不通过</p>
          </template>
        </el-table-column> -->
        <el-table-column label="排序" prop="sortKey" align="center" />
        <el-table-column label="是否推荐" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.onHomepage"
              :active-value="true"
              :inactive-value="false"
              @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope" v-if="scope.row.roleId !== 1">
            <el-button size="mini" type="text" @click="examine(scope.row)"
              >查看</el-button
            >
            <el-button size="mini" type="text" @click="compile(scope.row)"
              >编辑</el-button
            >
            <el-button size="mini" type="text" @click="handleDelete(scope.row)"
              >删除</el-button
            >
            <el-button
              size="mini"
              type="text"
              @click="clickOnline(scope.row)"
              v-if="!scope.row.onlineStatus"
              >上线</el-button
            >
            <el-button
              size="mini"
              type="text"
              @click="showing(scope.row)"
              v-if="scope.row.onlineStatus"
              >下线</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog title="删除" :visible.sync="deleteDialogVisible" width="30%">
      <span>是否删除此优惠套餐？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  delTable,
  online,
  homepage,
} from "@/api/platform/discountPackage";
export default {
  name: "app",
  data() {
    return {
      loading: false,
      total: 0,
      packageId: "",
      ruleForm: {
        pageSize: 10,
        pageNum: 1,
      },
      deleteDialogVisible: false,
      listData: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val;
      this.getList();
    },
    handleDelete(row) {
      this.packageId = row.packageId;
      this.deleteDialogVisible = true;
    },
    confirmDelete() {
      //确定删除
      delTable(this.packageId).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.deleteDialogVisible = false;
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    addMaternitySuite() {
      //新增优惠套餐
      this.$router.push({ path: "/addCombo" });
    },
    getList() {
      //列表
      this.loading = true;
      page(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.data.rows;
          this.total = res.data.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    examine(row) {
      //查看
      this.$router.push({
        path: "/addCombo",
        query: {
          packageId: row.packageId,
          forbidden: true,
        },
      });
    },
    compile(row) {
      //编辑
      this.$router.push({
        path: "/addCombo",
        query: {
          packageId: row.packageId,
        },
      });
    },
    clickOnline(row) {
      //上线
      let query = {
        packageId: row.packageId,
        status: true,
      };
      online(query).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    showing(row) {
      //下线
      let query = {
        packageId: row.packageId,
        status: false,
      };
      online(query).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    handleStatusChange(row) {
      //改变优惠套餐主页显示状态
      console.log(row);
      let query = {
        packageId: row.packageId,
        status: row.onHomepage,
      };
      homepage(query).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .title {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
  padding: 24px 20px;
  .title2 {
    font-size: 20px;
    color: #17191a;
    margin-bottom: 24px;
  }
  .table {
    margin-top: 20px;
  }
}
.block {
  text-align: right;
  margin-top: 20px;
}
</style>
