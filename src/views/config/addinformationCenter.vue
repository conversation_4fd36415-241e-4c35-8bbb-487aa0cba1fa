<template>
  <div id="app">
    <!-- <div class="head">
             <div class="title">客资中心</div>
             <div class="title1">Pages/客资中心/客户管理/新增</div>
         </div> -->
    <div class="content">
      <div class="content2">
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>客户姓名</p>
          </div>
          <input
            type="text"
            placeholder="请输入客户姓名"
            class="input"
            style="border: 1px solid #dcdcdc"
            v-model="ruleForm.name"
            :disabled="forbidden"
          />
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>电话号码</p>
          </div>
          <input
            type="text"
            placeholder="请输入电话号码"
            maxlength="11"
            class="input"
            style="border: 1px solid #dcdcdc"
            v-model="ruleForm.tel"
            :disabled="forbidden"
            @blur="inpt"
          />
        </div>
      </div>
      <div class="content2">
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>客户来源</p>
          </div>
          <el-select
            v-model="ruleForm.source"
            placeholder="请选择客户来源"
            clearable
            class="inputss"
            :disabled="forbidden"
          >
            <el-option
              v-for="item in source"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictLabel"
            >
            </el-option>
          </el-select>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>状态</p>
          </div>
          <el-select
            v-model="ruleForm.status"
            placeholder="全部"
            clearable
            class="input"
            :disabled="forbidden"
          >
            <el-option
              v-for="item in status"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="content2">
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>年龄</p>
          </div>
          <input
            type="text"
            placeholder="请输入年龄"
            style="border: 1px solid #dcdcdc"
            class="input"
            v-model="ruleForm.age"
            :disabled="forbidden"
          />
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>性别</p>
          </div>
          <el-select
            v-model="ruleForm.gender"
            placeholder="请选择"
            clearable
            class="inputss"
            :disabled="forbidden"
          >
            <el-option
              v-for="item in gender"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictLabel"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="content2">
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>妈妈生日</p>
          </div>
          <el-date-picker
            v-model="ruleForm.momBirthday"
            type="date"
            placeholder="请选择妈妈生日"
            value-format="yyyy-MM-dd HH:mm:ss"
            :disabled="forbidden"
            class="inputss"
          >
          </el-date-picker>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>微信号</p>
          </div>
          <input
            type="text"
            placeholder="请输入微信号"
            style="border: 1px solid #dcdcdc"
            class="input"
            v-model="ruleForm.wechat"
            :disabled="forbidden"
          />
        </div>
      </div>
      <div class="content2">
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>与宝宝关系</p>
          </div>
          <el-select
            v-model="ruleForm.relationToBaby"
            placeholder="请选择"
            clearable
            class="inputss"
            :disabled="forbidden"
          >
            <el-option
              v-for="item in relationToBaby"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictLabel"
            >
            </el-option>
          </el-select>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>意向</p>
          </div>
          <el-select
            v-model="ruleForm.intention"
            placeholder="请选择"
            clearable
            class="inputss"
            :disabled="forbidden"
          >
            <el-option
              v-for="item in intention"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictLabel"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="content2">
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>客资类型</p>
          </div>
          <el-select
            v-model="ruleForm.customerType"
            placeholder="请选择"
            clearable
            class="inputss"
            :disabled="forbidden"
          >
            <el-option
              v-for="item in customerType"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictLabel"
            >
            </el-option>
          </el-select>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>宝宝性别</p>
          </div>
          <el-select
            v-model="ruleForm.babyGender"
            placeholder="请选择"
            clearable
            class="inputss"
            :disabled="forbidden"
          >
            <el-option
              v-for="item in gender"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictLabel"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="content2">
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>宝宝姓名</p>
          </div>
          <input
            type="text"
            placeholder="请输入宝宝姓名"
            style="border: 1px solid #dcdcdc"
            class="input"
            v-model="ruleForm.babyName"
            :disabled="forbidden"
          />
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>宝宝生日</p>
          </div>
          <el-date-picker
            v-model="ruleForm.babyBirthday"
            type="date"
            placeholder="请选择宝宝生日"
            value-format="yyyy-MM-dd HH:mm:ss"
            :disabled="forbidden"
            class="inputss"
          >
          </el-date-picker>
        </div>
      </div>
      <div class="content2">
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>宝宝阶段&年龄</p>
          </div>
          <input
            type="text"
            placeholder="请输入宝宝阶段&年龄"
            style="border: 1px solid #dcdcdc"
            class="input"
            v-model="ruleForm.babyAgeStage"
            :disabled="forbidden"
          />
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>家庭住址</p>
          </div>
          <el-input
            type="textarea"
            v-model="ruleForm.address"
            placeholder="请输入家庭住址"
            class="inputs"
            :disabled="forbidden"
          ></el-input>
        </div>
      </div>
      <div class="contentTitle">其他信息</div>
      <div class="fgx"></div>
      <div class="content1">
        <div class="title5">
          <p class="required"></p>
          <p>备注</p>
        </div>
        <el-input
          type="textarea"
          v-model="ruleForm.notes"
          placeholder="请输入备注信息，最多支持200个字"
          class="inputs"
          :disabled="forbidden"
        ></el-input>
      </div>
      <div class="btn" v-if="!forbidden">
        <el-button type="primary" @click="confirm">保存</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
      <div class="btn" v-if="examine">
        <el-button @click="cancel">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import ImageUpload from "@/components/ImageUpload/index";
import { save, info, update } from "@/api/platform/informationCenter";
export default {
  components: { ImageUpload },
  name: "app",
  data() {
    return {
      examine: "",
      source: [
        //在线状态
        {
          dictLabel: "小程序",
          dictValue: "1",
        },
        {
          dictLabel: "大众点评",
          dictValue: "2",
        },
        {
          dictLabel: "美团",
          dictValue: "3",
        },
        {
          dictLabel: "抖音",
          dictValue: "4",
        },
        {
          dictLabel: "小红书",
          dictValue: "5",
        },
        {
          dictLabel: "转介绍",
          dictValue: "6",
        },
        {
          dictLabel: "自然进店",
          dictValue: "7",
        },
        {
          dictLabel: "其他",
          dictValue: "8",
        },
      ],
      status: [
        //状态
        {
          dictLabel: "未联系",
          dictValue: "0",
        },
        {
          dictLabel: "已联系",
          dictValue: "1",
        },
        {
          dictLabel: "已到店",
          dictValue: "2",
        },
        {
          dictLabel: "已交易",
          dictValue: "3",
        },
      ],
      gender: [
        //性别
        {
          dictLabel: "男",
          dictValue: "0",
        },
        {
          dictLabel: "女",
          dictValue: "1",
        },
      ],
      relationToBaby: [
        //与宝宝关系
        {
          dictLabel: "母女关系",
          dictValue: "0",
        },
        {
          dictLabel: "母子关系",
          dictValue: "1",
        },
      ],
      intention: [
        //意向
        {
          dictLabel: "有效客资",
          dictValue: "0",
        },
        {
          dictLabel: "无效客资",
          dictValue: "1",
        },
      ],
      customerType: [
        //意向
        {
          dictLabel: "新客",
          dictValue: "0",
        },
        {
          dictLabel: "老客",
          dictValue: "1",
        },
      ],
      customerId: "",
      forbidden: false,
      ruleForm: {
        name: "", //客户姓名
        tel: "", //电话
        source: "", //来源
        status: "", //状态
        age: "", //年龄
        gender: "", //性别
        momBirthday: "", //妈妈生日
        wechat: "", //微信号
        relationToBaby: "", //与宝宝关系
        intention: "", //意向
        customerType: "", //客资类型
        babyGender: "", //宝宝性别
        babyName: "", //宝宝姓名
        babyBirthday: "", //宝宝生日
        babyAgeStage: "", //宝宝阶段年龄
        address: "", //地址
        notes: "", //备注
      },
    };
  },
  watch: {},
  created() {
    this.customerId = this.$route.query.customerId;
    this.examine = this.$route.query.examine;
    this.forbidden = this.$route.query.forbidden ? true : false;
    if (this.customerId) {
      this.info(this.customerId);
    }
  },
  methods: {
    inpt() {
      const phoneReg = /^1[3|4|5|6|7|8|9][0-9]{9}$/;
      setTimeout(() => {
        if (!Number.isInteger(+this.ruleForm.tel)) {
          this.$message("请输入数字值");
          this.ruleForm.tel = "";
        } else {
          if (phoneReg.test(this.ruleForm.tel)) {
          } else {
            this.$message("电话号码格式不正确");
            this.ruleForm.tel = "";
          }
        }
      }, 100);
    },
    cancel() {
      this.$router.back(-1);
    },
    confirm() {
      let data = this.ruleForm;
      // if(data.name==''){
      //     this.$message('请输入客户姓名');
      //     return
      // }
      if (data.tel == "") {
        this.$message("请填写电话");
        return;
      }
      // if(data.source==''){
      //     this.$message('请选择来源');
      //     return
      // }
      if (data.status == "") {
        this.$message("请选择状态");
        return;
      }
      // if(data.age==''){
      //     this.$message('请填写年龄');
      //     return
      // }
      // if(data.gender==''){
      //     this.$message('请选择性别');
      //     return
      // }
      // if(data.momBirthday==''){
      //     this.$message('请选择妈妈生日');
      //     return
      // }
      // if(data.wechat==''){
      //     this.$message('请填写微信号');
      //     return
      // }
      // if(data.relationToBaby==''){
      //     this.$message('请选择与宝宝的关系');
      //     return
      // }
      // if(data.intention==''){
      //     this.$message('请选择意向');
      //     return
      // }
      // if(data.customerType==''){
      //     this.$message('请选择客资类型');
      //     return
      // }
      // if(data. babyGender==''){
      //     this.$message('请选择宝宝性别');
      //     return
      // }
      // if(data.babyName==''){
      //     this.$message('请填写宝宝姓名');
      //     return
      // }
      // if(data.babyBirthday==''){
      //     this.$message('请选择宝宝生日');
      //     return
      // }
      // if(data.babyAgeStage==''){
      //     this.$message('请填写宝宝阶段年龄');
      //     return
      // }
      // if(data.address==''){
      //     this.$message('请填写地址');
      //     return
      // }
      // if(data.notes==''){
      //     this.$message('请填写备注');
      //     return
      // }

      if (!this.customerId) {
        save(this.ruleForm).then((res) => {
          if (res.code == 200) {
            this.$message(res.msg);
            setTimeout(() => {
              this.$router.push({
                path: "/informationCenter",
              });
            }, 2000);
            return;
          } else {
            this.$message(res.msg);
          }
        });
      }
      if (this.customerId) {
        update(this.ruleForm).then((res) => {
          if (res.code == 200) {
            this.$message(res.msg);
            setTimeout(() => {
              this.$router.push({
                path: "/informationCenter",
              });
            }, 2000);
            return;
          } else {
            this.$message(res.msg);
          }
        });
      }
    },
    info(customerId) {
      //查询
      info(customerId).then((res) => {
        if (res.code == 200) {
          this.ruleForm = res.data;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .title {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
  padding: 24px 50px;
  .title2 {
    font-size: 20px;
    color: #17191a;
    margin-bottom: 24px;
  }
  .table {
    margin-top: 20px;
  }
  .title5 {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
    width: 100px;
    justify-content: flex-end;

    margin-right: 10px;
    .required {
      color: red;
    }
  }
  .content1 {
    display: flex;
    align-items: center;
    margin: 20px 0 10px 0;
    .input {
      width: 240px;
      height: 32px;
      border-radius: 3px;
    }
    .inputss {
      width: 240px;
    }
    .upload {
    }
  }
}
.btn {
  margin: 20px auto;
  text-align: center;
}
#cars {
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  color: #7c7d81;
  font-size: 14px;
  width: 129px;
  height: 32px;
  margin-left: 6px;
}
.postpartum {
  display: flex;
  align-items: center;
}
.units {
  width: 46px;
  height: 32px;
  background: #dcdcdc;
  border: 1px solid #dcdcdc;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
}
.textarea {
  width: 368px;
  height: 86px;
}
.content2 {
  width: 65%;
  display: flex;
  justify-content: space-between;
}
.contentTitle {
  color: #17191a;
  font-size: 20px;
  margin-top: 30px;
}
.fgx {
  width: 100%;
  height: 2px;
  background: #ebebeb;
  margin: 20px 0;
}
.inputs {
  width: 240px;
}
</style>
