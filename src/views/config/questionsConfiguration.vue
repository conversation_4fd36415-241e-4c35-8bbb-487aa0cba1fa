<template>
    <div class="app" v-loading="loading">
        <!-- <div class="head">
            <div class="title">签到管理</div>
            <div class="title1">Pages/签到管理</div>
        </div> -->
        <div class="content">
             <div class="title2">问答配置</div>
             <el-button type="primary" @click="addCheckManagement">新增问答</el-button>
             <el-table :data="listData" class="table">
                <el-table-column label="问题" prop="questionText" :show-overflow-tooltip="true" align="center" />
                <el-table-column label="答案" prop="answerText" :show-overflow-tooltip="true" align="center" />
      <el-table-column label="类型" prop="category" :show-overflow-tooltip="true" align="center">
        <template slot-scope="scope"> 
          <p v-if="scope.row.category=='room'">房间问答</p>
          <p v-if="scope.row.category=='meal_package'">膳食配送问答</p>
          <p v-if="scope.row.category=='recovery'">产康问答</p>   
          <p v-if="scope.row.category=='package'">套餐问答</p>
          <p v-if="scope.row.category=='meal'">月子膳食问答</p>
          <p v-if="scope.row.category=='nanny'">移动月嫂问答</p>
          </template>
          </el-table-column>
      <el-table-column label="显示状态" align="center" width="100">
        <template slot-scope="scope"> 
          <el-switch
          v-model="scope.row.isVisible"
            :active-value="true"
            :inactive-value="false"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope" v-if="scope.row.roleId !== 1">
          <!-- <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >编辑</el-button> -->
          <!-- <el-button
            size="mini"
            type="text"
            @click="clickOnline(scope.row)"
            v-if="!scope.row.onlineStatus"
          >上线</el-button>
          <el-button
            size="mini"
            type="text"
            @click="showing(scope.row)"
            v-if="scope.row.onlineStatus"
          >下线</el-button> -->
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="block">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="100"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>
  </div>
        </div>
<!--新增-->
<el-dialog
  title="提示"
  :visible.sync="addShow"
  width="30%">
  <el-form :model="from" :rules="rules" ref="from" label-width="100px" class="demo-ruleForm">
    <el-form-item label="类型" prop="category">
        <el-select
              v-model="from.category"
              placeholder="请选择类型"
              clearable
              class="textarea"
            >
              <el-option
                v-for="item in bedTypeList"
                :key="item.dictValue" 
                :label="item.dictLabel"
                 :value="item.dictValue"
              />
            </el-select>
    </el-form-item>
  <el-form-item label="问题" prop="questionText">
    <el-input v-model="from.questionText" placeholder="请输入问题"></el-input>
  </el-form-item>
  <el-form-item label="答案" prop="answerText">
    <el-input type="textarea" v-model="from.answerText" placeholder="请输入答案"></el-input>
  </el-form-item>
      </el-form>
  <span slot="footer" class="dialog-footer">
    <el-button @click="addShow = false">取 消</el-button>
    <el-button type="primary" @click="submitForm('from')">确 定</el-button>
  </span>
</el-dialog>
<el-dialog
  title="删除"
  :visible.sync="deleteDialogVisible"
  width="30%">
  <span>是否删除此问答配置？</span>
  <span slot="footer" class="dialog-footer">
    <el-button @click="deleteDialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
  </span>
</el-dialog>
    </div>
</template>

<script>
import { page,save,delTable,homepage } from "@/api/platform/questionsConfiguration";
import ImageUpload from "@/components/ImageUpload/index"
export default {
    name: "app",
    components: {ImageUpload},
  data() {
    return {
        bedTypeList: [//床型
                {
                    dictLabel: '房间问答',
                    dictValue: 'room'
                },
                {
                    dictLabel: '膳食配送问答',
                    dictValue: 'meal_package'
                },
                {
                    dictLabel: '产康问答',
                    dictValue: 'recovery'
                },
                {
                    dictLabel: '套餐问答',
                    dictValue: 'package'
                }, 
                {
                    dictLabel: '移动月嫂问答',
                    dictValue: 'nanny'
                },
                {
                    dictLabel: '月子膳食问答',
                    dictValue: 'meal'
                }, 
            ],
      listData:[],
      loading:false,
      nowAddress: [], //临时保存地址
            fixedAddress: 'https://txyoss.oss-cn-shenzhen.aliyuncs.com',
            currentIndex: '',//当前图片的下标
            fileCount: 0,
            picNum: 0,
            maxNum: 5,
            photoList: [], //照片列表
            dialogImageUrl: '',
            dialogVisible: false,
            disabled: false,
            total:0,
      ruleForm: {
        pageSize:10,
        pageNum:1
      },
      from:{
        questionText:'',
        category:'',
        answerText:''
      },
      addShow:false,
      deleteDialogVisible:false,
      questionId:'',
      rules: {
        category: [
            {required: true,message: '请选择类型', trigger: 'blur' }
          ],
          questionText: [
            {required: true,message: '请填写问题', trigger: 'blur' }
          ],
          answerText: [
            {required: true,message: '请填写答案', trigger: 'blur' }
          ],
      }
    }
},
created(){
  this.getList()
},
methods:{
  handleStatusChange(row){//更改问题显示状态
        let query={
          questionId:row.questionId,
          state:row.isVisible
        }
        homepage(query).then(res=>{
          if(res.code==200){
                    this.$message(res.msg);
                    this.getList()
                return 
                }else{
                    this.$message(res.msg); 
                }
        })
      },
  handleSizeChange(val) {
      this.ruleForm.pageSize=val
      this.getList()
      },
      handleCurrentChange(val) {
        this.ruleForm.pageNum=val
        this.getList()
      },
      handleDelete(row){//删除
        this.questionId=row.questionId
        this.deleteDialogVisible=true
      },
      confirmDelete(){//确定删除
        delTable(this.questionId).then(res=>{
          if(res.code==200){
                    this.$message(res.msg);
                    this.deleteDialogVisible=false
                   this.getList()
                return 
                }else{
                    this.$message(res.msg); 
                }
        })
      },
      addCheckManagement(){//节点
           this.addShow=true
      },
      getList(){//列表
        this.loading=true
        page(this.ruleForm).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.listData=res.rows
                    this.total=res.total
                    this.loading=false
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
      },
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
              this.$confirm('确定新增该问答吗, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          save(this.from).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.addShow=false
                    this.getList()
                    this.from={
        questionText:'',
        category:'',
        answerText:''
      }
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消新增'
          });          
        });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
}
}
</script>

<style scoped lang="scss">
.head{
    width: 100%;
    height: 80px;
    background: #ffff;
    color: #17191A;
    padding: 15px 20px;
    .title{
        font-size: 22px;
        font-weight: bold;
    }
    .title1{
        font-size: 14px;
    }
}
.content{
    background: #ffff;
    margin: 10px 10px;
    border-radius: 5px;
    padding: 24px 20px;
    .title2{
        font-size: 20px;
        color: #17191A;
        margin-bottom: 24px;
    }
    .table{
        margin-top: 20px;
    }
}
.block{
    text-align: right;
    margin-top: 20px;
}
</style>