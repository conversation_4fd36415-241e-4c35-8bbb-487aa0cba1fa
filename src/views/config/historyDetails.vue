<template>
    <div id="app">
   
     <!-- <div class="head">
             <div class="title">历史详情</div>
             <div class="title1">Pages/房间管理/历史记录/历史详情</div>
         </div> -->
         <div class="content">
             <div class="contentTitle">历史详情</div>
             <div class="fgx"></div>
             <div>
              <div class="history">
                <div class="history1">
                    <p class="history3">入住人员姓名</p>
                    <p class="history2">{{checkoutList.customerName}}</p>
                </div>
                <div class="history1">
                    <p class="history3">手机号</p>
                    <p class="history2">{{checkoutList.tel}}</p>
                </div>
              </div>
              <div class="history">
                <div class="history1">
                    <p class="history3">负责人员</p>
                    <p class="history2">{{checkoutList.staffName}}</p>
                </div>
                <div class="history1">
                    <p class="history3">房型</p>
                    <p class="history2">{{checkoutList.suiteName}}</p>
                </div>
              </div>
              <div class="history">
                <div class="history1">
                    <p class="history3">入住时间</p>
                    <p class="history2">{{checkoutList.checkinDate}}</p>
                </div>
                <div class="history1">
                    <p class="history3">退房时间</p>
                    <p class="history2">{{checkoutList.checkoutDate}}</p>
                </div>
              </div>
              <div class="history">
                <div class="history1">
                    <p class="history3">房间号</p>
                    <p class="history2">{{checkoutList.roomNumber}}</p>
                </div>
              
              </div>
            </div>
         </div>
    </div>
 </template>
 
 <script>
 import { checkoutPages } from "@/api/platform/roomManagement";
 export default {
    name:'app',
    data(){
     return{
        checkoutList:'',
     }
    },
    created(){
    let checkoutId=this.$route.query.checkoutId
    this.checkoutPages(checkoutId)
     },
    methods:{
        checkoutPages(checkoutId){
            checkoutPages(checkoutId).then(res=>{
                if(res.code==200){
                    this.$message(res.msg);
                    this.checkoutList=res.data
                return 
                }else{
                    this.$message(res.msg); 
                }
            })
        }
        
    }
 }
 </script>
 
 <style scoped lang="scss">
 .head{
     width: 100%;
     height: 80px;
     background: #ffff;
     color: #17191A;
     padding: 15px 20px;
     .title{
         font-size: 22px;
         font-weight: bold;
     }
     .title1{
         font-size: 14px;
     }
 }
 .content{
     background: #ffff;
     margin: 10px 10px;
     border-radius: 5px;
     padding: 20px 20px 24px 20px;
     .contentTitle{
         color: #17191A;
         font-size: 20px;
         margin-top: 30px;
     }
     .fgx{
         width: 100%;
         height: 2px;
         background: #EBEBEB;
         margin: 20px 0;
     }
    .input{
     width: 20%;
    }
    .content1{
     margin-left: 50px;
    }
    .unit{
     margin-left: 6px;
    }
    .title5{
     display: flex;
     align-items: center;
     font-size: 14px;
     color: #606266;
     box-sizing: border-box;
     font-weight: bold;
     .required{
         color: red;
     }
    }
 }
 .relevance{
     margin-top: 10px;
     margin-left: 150px;
 }
 .postpartum{
     display: flex;
     align-items: center;
 }
 .inputs{
     width: 80px;
     height: 32px;
     border: 1px solid #DCDCDC;
     border-radius: 3px;
     margin-left: 16px;
 }
 .units{
     width: 30px;
     height: 32px;
     background: #DCDCDC;
     border: 1px solid #DCDCDC;
     text-align: center;
     line-height: 32px;
     font-size: 14px;
 }
 .jia{
     font-size:28px;
     margin: 0 8px;
 }
 .BackgroundPicture{
     display: flex;
     margin-top: 20px;
     .uploadTitle{
         display: flex;
         color: #17191A;
         font-size: 14px;
         width: 100px;
         justify-content: flex-end;
         padding: 0 12px 0 0;
     }
     .el-upload--picture-card{
         width: 50px;
         height: 50px;
     }
 }
 .btn{
     margin: 20px auto;
     text-align: center;
 }
 .dialogTitle{
     display: flex;
     align-items: center;
 }
 .tables{
     margin-bottom: 10px;
 }
 .auditContent{
   width: 100%;
   padding: 20px 24px;
   background: #FF6C11;
   border-radius: 6px;
   font-size: 14px;
   line-height: 14px;
 }
 .pass{
   display: flex;
   align-items: center;
   color: #000000;
 }
 .passCause{
   color:#000000;
   margin-left:20px
 }

 .history{
    display: flex;
    font-size: 14px;
 }
 .history1{
    display: flex;
    width: 40%;
 }
 .history2{
    width: 50%;
    color: #17191A;
 }
 .history3{
    width: 50%;
    color: #A9AAAE;
 }
 </style>