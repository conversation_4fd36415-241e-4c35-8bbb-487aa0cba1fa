<template>
  <div class="app" v-loading="loading">
    <!-- <div class="head">
            <div class="titles">社区评论</div>
            <div class="title1">Pages/妈妈社区/社区评论</div>
        </div> -->
    <div class="content">
      <div class="contentHead">
        <h2 class="title2">社区评论管理</h2>
        <div class="contentHeads">
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">按话题筛选</p>
              <el-select
                v-model="ruleForm.topicId"
                placeholder="全部"
                clearable
                style="width: 159px; margin-left: 10px"
              >
                <el-option
                  v-for="item in topicList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
            <div class="sel">
              <p class="selTitle">排序</p>
              <el-select
                v-model="ruleForm.orderType"
                placeholder="全部"
                clearable
                style="width: 159px; margin-left: 10px"
              >
                <el-option
                  v-for="item in orderTypeList"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">选择时间</p>
              <div>
                <el-date-picker
                  v-model="ruleForm.startDate"
                  type="datetime"
                  placeholder="请选择评论时间"
                  value-format="yyyy-MM-dd"
                  style="margin: 0 10px"
                >
                </el-date-picker>
                <el-date-picker
                  v-model="ruleForm.endDate"
                  type="datetime"
                  placeholder="请选择评论时间"
                  value-format="yyyy-MM-dd"
                  style="margin: 0 10px"
                >
                </el-date-picker>
              </div>
            </div>
          </div>

          <el-button
            type="primary"
            style="margin-left: 8px; height: 36px"
            @click="inquire"
            >查询</el-button
          >
        </div>
      </div>
      <div class="conment">
        <div style="text-align: right">
          <el-button
            type="primary"
            @click="deleteinBatches"
            v-if="batchDeleteShow"
            >批量删除</el-button
          >
        </div>
        <div
          v-for="(item, index) in listData"
          :key="index"
          style="display: flex"
        >
          <el-checkbox
            v-model="item.checkbox"
            class="checkbox"
            v-if="checkboxShow"
            @change="handleCheckAllChange(item.postId, item.checkbox)"
          ></el-checkbox>
          <div style="width: 100%">
            <div class="conment1">
              <img
                :src="item.avatar"
                alt=""
                style="width: 50px; height: 50px; border-radius: 50px"
              />
              <div class="conment1_1">
                <p class="conmentTitle1">{{ item.nickname }}</p>
                <p class="conmentTitle2">{{ item.postIp }}</p>
              </div>
            </div>
            <div class="conmentTitle1_2">
              <p class="conmentTitle1_1">{{ item.topicName }}</p>
              {{ item.content }}
            </div>
            <div class="picture">
              <img
                :src="item"
                alt=""
                style="width: 100px; height: 100px; margin-right: 12px"
                v-for="(item, index) in item.imageUrl"
                :key="index"
              />
            </div>
            <div class="release">
              <div class="releaseTime">发布于{{ item.createTime }}</div>
              <div
                class="del"
                v-if="deleteShow"
                @click="deleteDynamic(item.postId)"
              >
                删除
              </div>
            </div>
            <div class="releasematter">
              <div>
                <div v-for="(res, index) in item.commentList" :key="index">
                  <div class="releasematter1">
                    <div class="releasematter7">
                      <img
                        :src="res.avatar"
                        alt=""
                        style="width: 45px; height: 45px"
                      />
                      <div class="releasematter2">
                        <p class="releasematter4">
                          <span class="releasematter3"
                            >{{ res.nickname }}:</span
                          >
                          {{ res.content }}
                        </p>
                        <p class="releasematter5">{{ res.createTime }}</p>
                      </div>
                    </div>
                    <div
                      class="releasematter8"
                      v-if="deleteShow"
                      @click="deleteComment(res.commentId)"
                    >
                      删除
                    </div>
                  </div>
                  <div class="releasematter6" @click="reply(item)">
                    <p>共{{ item.commentList.length }}条回复</p>
                    <img
                      src="http://cdn.xiaodingdang1.com/Arrow_drop_down%402x.png"
                      alt=""
                      style="width: 24px; height: 24px"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="btn" v-if="btnShow">
          <el-button type="primary" @click="delConfirm">确定</el-button>
          <el-button @click="delCancel">取消</el-button>
        </div>
      </div>
    </div>
    <el-dialog
      :title="replyLength"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <div>
        <div class="dialogVisible">
          <img
            :src="listStrip.avatar"
            alt=""
            style="width: 45px; height: 45px; border-radius: 50px"
          />
          <div class="dialogVisible1">
            <p class="dialogVisible2">
              <span class="dialogVisible3">{{ listStrip.nickname }}:</span>
              {{ listStrip.content }}
            </p>
            <p class="dialogVisible4">{{ listStrip.createTime }}</p>
          </div>
        </div>
        <div class="dialogVisible5">
          <div
            class="dialogVisible6"
            v-for="(item, index) in listStrip.commentList"
            :key="index"
          >
            <p class="dialogVisible7">
              <span class="dialogVisible8">{{ item.nickname }}:</span
              >{{ item.content }}
            </p>
            <p class="dialogVisible9">{{ item.createTime }}</p>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  list,
  delTable,
  delComment,
  options,
} from "@/api/platform/communityReview";
export default {
  name: "app",
  data() {
    return {
      topicList: [],
      replyLength: "",
      listStrip: "",
      loading: true,
      postId: [],
      batchDeleteShow: true,
      deleteShow: true,
      checkboxShow: false,
      btnShow: false,
      listData: [],
      orderTypeList: [
        {
          dictValue: "1",
          dictLabel: "最新",
        },
        {
          dictValue: "2",
          dictLabel: "最热",
        },
      ],
      dialogVisible: false,
      ruleForm: {
        topicId: "",
        orderType: "",
        startDate: "",
        endDate: "",
      },
    };
  },
  created() {
    this.getList();
    this.options();
  },
  methods: {
    handleCheckAllChange(postId, checkbox) {
      let id = this.postId;
      if (checkbox == false) {
        for (let i = 0; i < id.length; i++) {
          if (postId == id[i]) {
            id.splice(i, 1);
            return;
          }
        }
      } else {
        id.push(postId);
      }
    },
    delConfirm() {
      //批量删除确定
      console.log(this.postId);
      this.batchDeleteShow = !this.batchDeleteShow;
      this.deleteShow = !this.deleteShow;
      this.checkboxShow = !this.checkboxShow;
      this.btnShow = !this.btnShow;
      let postId = this.postId;
      if (postId.length == 0) {
        this.$message("请选择你需要删除的动态");
        return;
      }
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delTable(postId).then((res) => {
            if (res.code == 200) {
              this.$message(res.msg);
              this.getList();
              return;
            } else {
              this.$message(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    delCancel() {
      //批量删除取消
      this.postId = [];
      this.batchDeleteShow = !this.batchDeleteShow;
      this.deleteShow = !this.deleteShow;
      this.checkboxShow = !this.checkboxShow;
      this.btnShow = !this.btnShow;
    },
    deleteinBatches() {
      //批量删除
      this.batchDeleteShow = !this.batchDeleteShow;
      this.deleteShow = !this.deleteShow;
      this.checkboxShow = !this.checkboxShow;
      this.btnShow = !this.btnShow;
    },
    inquire() {
      //查询
      this.getList();
    },
    handleClose(done) {
      // this.$confirm('确认关闭？')
      //   .then(_ => {
      //     done();
      //   })
      //   .catch(_ => {});
      this.dialogVisible = false;
    },
    reply(e) {
      //回复
      this.listStrip = e;
      this.replyLength = e.commentList.length + "条回复";
      this.dialogVisible = true;
    },
    options() {
      options().then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.topicList = res.data;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    getList() {
      this.loading = true;
      list(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          let data = res.data;
          data.forEach((item) => {
            item.checkbox = false;
          });
          this.listData = data;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    deleteDynamic(postId) {
      this.$confirm("确定删除该动态吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.postId.push(postId);
          delTable(this.postId).then((res) => {
            if (res.code == 200) {
              this.$message(res.msg);
              this.getList();
              return;
            } else {
              this.$message(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    deleteComment(commentId) {
      this.$confirm("确定删除该评论吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delComment(commentId).then((res) => {
            if (res.code == 200) {
              this.$message(res.msg);
              this.getList();
              return;
            } else {
              this.$message(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style scoped lang="scss">
.app {
  background: #f0f1f5;
  padding-bottom: 30px;
}
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .titles {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  margin: 20px 20px;
  .contentHead {
    background: #ffff;
    padding: 5px 14px;
    border-radius: 10px;
    .title2 {
      color: #17191a;
    }
  }
}
.roomMessage {
  display: flex;
}
.sel {
  display: flex;
  align-items: center;
  margin-right: 10px;
  .selTitle {
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
    margin-right: 10px;
  }
}
#cars {
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  color: #7c7d81;
  font-size: 14px;
  width: 179px;
  height: 32px;
  margin-left: 6px;
}
.tiem {
  margin-left: 8px;
}
.conment {
  margin-top: 20px;
  background: #ffff;
  border-radius: 10px;
  padding: 20px 20px;
  .conment1 {
    display: flex;
    align-items: center;
    .conment1_1 {
      margin-left: 12px;
    }
  }
  .conmentTitle1 {
    font-size: 16px;
    color: #ff6e00;
  }
  .conmentTitle2 {
    font-size: 14px;
    color: #7c7d81;
  }
  .editor {
    font-size: 14px;
    color: #45464a;
  }
}
.picture {
  display: flex;
}
.release {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.releaseTime {
  font-size: 14px;
  color: #7c7d81;
  margin-top: 10px;
}
.releasematter {
  background: #fafafd;
  border-radius: 5px;
  padding: 20px 20px;
  margin-top: 10px;
  font-size: 14px;
  .releasematter1 {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .releasematter2 {
    margin-left: 10px;
  }
  .releasematter3 {
    color: #ff6e00;
  }
  .releasematter4 {
    display: flex;
    color: #45464a;
  }
  .releasematter5 {
    color: #7c7d81;
  }
  .releasematter7 {
    display: flex;
    align-items: center;
  }
  .releasematter8 {
    color: #7c7d81;
  }
}
.releasematter6 {
  display: flex;
  color: #ff6e00;
  font-size: 14px;
  align-items: center;
  margin-left: 55px;
  cursor: pointer;
}
.contentHeads {
  display: flex;
  align-items: center;
}
.conmentTitle1_1 {
  color: #5b6799;
  font-size: 14px;
}
.conmentTitle1_2 {
  display: flex;
  color: #45464a;
  font-size: 14px;
  align-items: center;
}
.del {
  color: #f84343;
  font-size: 14px;
}
.dialogVisible {
  display: flex;
  font-size: 14px;
}
.dialogVisible1 {
  margin-left: 10px;
}
.dialogVisible3 {
  color: #ff6e00;
}
.dialogVisible2 {
  color: #45464a;
}
.dialogVisible4 {
  color: #7c7d81;
}
.dialogVisible5 {
  width: 80%;
  height: 300px;
  font-size: 14px;
  overflow-y: auto;
  margin-left: 55px;
}
.dialogVisible7 {
  color: #45464a;
}
.dialogVisible8 {
  color: #ff6e00;
}
.dialogVisible9 {
  color: #7c7d81;
}
.checkbox {
  margin-top: 130px;
  margin-right: 24px;
}
.btn {
  text-align: center;
  margin-top: 10px;
}
</style>
