<template>
  <div id="app">
    <div class="content">
      <div class="auditContent" v-if="auditInfoList.auditStatus == 2">
        <div class="pass">
          <img
            src="http://cdn.xiaodingdang1.com/icon%402x.png"
            alt=""
            style="width: 20px; height: 20px"
          />
          <p>驳回原因</p>
        </div>
        <p class="passCause">{{ auditInfoList.rejectionReason }}</p>
      </div>
      <div class="contentTitle">套餐信息</div>
      <div class="fgx"></div>
      <div class="content1">
        <el-form label-width="100px" class="demo-ruleForm">
          <el-form-item label="套餐名称" prop="name">
            <el-input
              v-model="ruleForm.packageName"
              class="input"
              :disabled="forbidden"
            ></el-input>
          </el-form-item>
          <el-form-item label="套餐价格">
            <el-input
              v-model="ruleForm.packagePrice"
              class="input"
              :disabled="forbidden"
              @blur="inpt(1)"
            ></el-input>
            <span class="unit">元</span>
          </el-form-item>
          <el-form-item label="入住天数" prop="name">
            <el-input
              v-model="ruleForm.stayDays"
              class="input"
              :disabled="forbidden"
              @blur="inpt(2)"
            ></el-input>
            <span class="unit">天</span>
          </el-form-item>
          <el-form-item label="签约礼">
            <el-select
              v-model="ruleForm.contractGiftId"
              placeholder="请选择签约礼"
              clearable
              style="width: 240px"
              :disabled="forbidden"
            >
              <el-option
                v-for="item in giftList"
                :key="item.contractGiftId"
                :label="item.description"
                :value="item.contractGiftId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="排序" prop="name">
            <el-input-number
              v-model="ruleForm.sortKey"
              controls-position="right"
              :min="0"
              :max="1000"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="套餐图片">
            <image-upload
              :oneAll="3"
              :limit="8"
              :isShowTip="false"
              v-model="ruleForm.packagePhotos"
              v-if="!forbidden"
            />
            <div v-if="forbidden">
              <img
                :src="item"
                alt=""
                v-for="(item, index) in ruleForm.packagePhotos"
                :key="index"
                style="
                  width: 146px;
                  height: 146px;
                  margin-right: 10px;
                  border: 1px solid #c0ccda;
                  border-radius: 6px;
                "
              />
            </div>
          </el-form-item>
          <el-form-item label="套餐视频">
            <file-upload
              :limit="1"
              :isShowTip="false"
              v-model="ruleForm.packageVideos"
              v-if="!forbidden"
            />
            <div v-if="forbidden">
              <video
                controls="controls"
                :src="item"
                alt=""
                v-for="(item, index) in ruleForm.packageVideos"
                :key="index"
                style="
                  width: 146px;
                  height: 146px;
                  margin-right: 10px;
                  border: 1px solid #c0ccda;
                  border-radius: 6px;
                "
              ></video>
            </div>
          </el-form-item>
          <el-form-item label="图文详情">
            <image-upload
              :oneAll="3"
              :limit="8"
              :isShowTip="false"
              v-model="ruleForm.textPhotos"
              v-if="!forbidden"
            />
            <div v-if="forbidden">
              <img
                :src="item"
                alt=""
                v-for="(item, index) in ruleForm.textPhotos"
                :key="index"
                style="
                  width: 146px;
                  height: 146px;
                  margin-right: 10px;
                  border: 1px solid #c0ccda;
                  border-radius: 6px;
                "
              />
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="contentTitle">套餐信息</div>
      <div class="fgx"></div>
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>房型</p>
        </div>
        <el-table :data="ruleForm.suiteList" class="table">
          <el-table-column label="房间照片" align="center">
            <template slot-scope="scope">
              <img :src="scope.row.suitePhotos[0]" min-width="70" height="70" />
            </template>
          </el-table-column>
          <el-table-column label="名称" prop="roomName" align="center" />
          <el-table-column label="房型" prop="roomType" align="center">
          </el-table-column>
          <el-table-column label="朝向" prop="orientation" align="center" />
          <el-table-column label="楼层" align="center">
            <template slot-scope="scope">
              <p>{{ scope.row.minFloor }}-{{ scope.row.maxFloor }}楼</p>
            </template>
          </el-table-column>
          <el-table-column label="面积" align="center">
            <template slot-scope="scope">
              <p>{{ scope.row.minArea }}-{{ scope.row.maxArea }}m²</p>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="houseDelete(scope.$index, 1)"
                :disabled="forbidden"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-button class="relevance" @click="relevance(1)" v-if="!forbidden"
          >关联房型<i class="el-icon-plus"></i
        ></el-button>
        <div class="title5">
          <p class="required">*</p>
          <p>妈妈用品</p>
        </div>
        <el-table :data="ruleForm.mamaSuppliesList" class="table">
          <el-table-column label="序号" align="center">
            <template scope="scope">
              <span v-text="scope.$index + 1"></span>
            </template>
          </el-table-column>
          <el-table-column label="用品品牌" prop="brandName" align="center" />
          <el-table-column label="用品名称" prop="supplyName" align="center">
          </el-table-column>
          <el-table-column
            label="数量（想展示“按需”请 填写999）"
            prop="quantity"
            width="200"
            align="center"
          >
            <template slot-scope="scope">
              <input
                type="text"
                name=""
                id=""
                placeholder="请填写数量"
                v-model="scope.row.quantity"
                :disabled="forbidden"
              />
            </template>
          </el-table-column>
          <!-- <el-table-column label="限店内使用" prop="inStoreUse"  align="center" >
      </el-table-column> -->
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="houseDelete(scope.$index, 2)"
                :disabled="forbidden"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-button class="relevance" @click="relevance(2)" v-if="!forbidden"
          >关联用品<i class="el-icon-plus"></i
        ></el-button>
        <div class="title5">
          <p class="required">*</p>
          <p>宝宝用品</p>
        </div>
        <el-table :data="ruleForm.babySuppliesList" class="table">
          <el-table-column label="序号" align="center">
            <template scope="scope">
              <span v-text="scope.$index + 1"></span>
            </template>
          </el-table-column>
          <el-table-column label="用品品牌" prop="brandName" align="center" />
          <el-table-column label="用品名称" prop="supplyName" align="center">
          </el-table-column>
          <el-table-column
            label="数量（想展示“按需”请 填写999）"
            prop="quantity"
            align="center"
          >
            <template slot-scope="scope">
              <input
                type="text"
                name=""
                id=""
                placeholder="请填写数量"
                v-model="scope.row.quantity"
                :disabled="forbidden"
              />
            </template>
          </el-table-column>
          <!-- <el-table-column label="限店内使用" prop="inStoreUse"  align="center" >
      </el-table-column> -->
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="houseDelete(scope.$index, 3)"
                :disabled="forbidden"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-button class="relevance" @click="relevance(3)" v-if="!forbidden"
          >关联用品<i class="el-icon-plus"></i
        ></el-button>
        <div class="leave">
          <div class="title5">
            <p class="required">*</p>
            <p>家人陪住</p>
          </div>
          <el-radio
            v-model="ruleForm.isFamilyAccommodation"
            :label="true"
            :disabled="forbidden"
            >支持家人陪住</el-radio
          >
          <el-radio
            v-model="ruleForm.isFamilyAccommodation"
            :label="false"
            :disabled="forbidden"
            >不支持家人陪住</el-radio
          >
        </div>
      </div>
      <div class="contentTitle">护理模式</div>
      <div class="fgx"></div>
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>母婴照护模式</p>
        </div>
        <el-radio
          v-model="ruleForm.maternityCareModel"
          label="1"
          :disabled="forbidden"
          >母婴同室护理</el-radio
        >
        <el-radio
          v-model="ruleForm.maternityCareModel"
          label="2"
          :disabled="forbidden"
          >婴儿集中托管</el-radio
        >
        <el-radio
          v-model="ruleForm.maternityCareModel"
          label="3"
          :disabled="forbidden"
          >母婴同室（可托管）</el-radio
        >
        <div class="title5">
          <p class="required">*</p>
          <p>陪护人员类型</p>
        </div>
        <el-radio
          v-model="ruleForm.attendantType"
          label="1"
          :disabled="forbidden"
          >护士</el-radio
        >
        <el-radio
          v-model="ruleForm.attendantType"
          label="2"
          :disabled="forbidden"
          >月嫂</el-radio
        >
        <div class="title5">
          <p class="required">*</p>
          <p>陪护人员配置</p>
        </div>
        <el-radio
          v-model="ruleForm.attendantConfiguration"
          label="1"
          :disabled="forbidden"
          >1对1护理</el-radio
        >
        <el-radio
          v-model="ruleForm.attendantConfiguration"
          label="2"
          :disabled="forbidden"
          >2对1护理</el-radio
        >
        <el-radio
          v-model="ruleForm.attendantConfiguration"
          label="3"
          :disabled="forbidden"
          >3对1护理</el-radio
        >
        <el-radio
          v-model="ruleForm.attendantConfiguration"
          label="4"
          :disabled="forbidden"
          >集中护理</el-radio
        >
      </div>
      <div class="contentTitle">护理服务</div>
      <div class="fgx"></div>
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>妈妈护理</p>
        </div>
        <el-table :data="ruleForm.mamaNursingList" class="table">
          <el-table-column label="序号" align="center">
            <template scope="scope">
              <span v-text="scope.$index + 1"></span>
            </template>
          </el-table-column>
          <el-table-column
            label="项目名称"
            prop="projectName"
            :show-overflow-tooltip="true"
            align="center"
          />
          <el-table-column
            label="数量（想展示“按需”请
填写999）"
            prop="quantity"
            :show-overflow-tooltip="true"
            align="center"
          >
            <template slot-scope="scope">
              <input
                type="text"
                name=""
                id=""
                placeholder="请填写数量"
                v-model="scope.row.quantity"
                :disabled="forbidden"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="houseDelete(scope.$index, 4)"
                :disabled="forbidden"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-button class="relevance" @click="relevance(4)" v-if="!forbidden"
          >关联项目<i class="el-icon-plus"></i
        ></el-button>
        <div class="title5">
          <p class="required">*</p>
          <p>宝宝护理</p>
        </div>
        <el-table :data="ruleForm.babyNursingList" class="table">
          <el-table-column label="序号" width="120" align="center">
            <template scope="scope">
              <span v-text="scope.$index + 1"></span>
            </template>
          </el-table-column>
          <el-table-column
            label="项目名称"
            prop="projectName"
            :show-overflow-tooltip="true"
            align="center"
          />
          <el-table-column
            label="数量（想展示“按需”请
填写999）"
            prop="quantity"
            :show-overflow-tooltip="true"
            align="center"
          >
            <template slot-scope="scope">
              <input
                type="text"
                name=""
                id=""
                placeholder="请填写数量"
                v-model="scope.row.quantity"
                :disabled="forbidden"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="houseDelete(scope.$index, 5)"
                :disabled="forbidden"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-button class="relevance" @click="relevance(5)" v-if="!forbidden"
          >关联项目<i class="el-icon-plus"></i
        ></el-button>
      </div>

      <div class="contentTitle">产康服务</div>
      <div class="fgx"></div>
      <div class="content1">
        <el-table :data="ruleForm.postpartumList" class="table">
          <el-table-column label="序号" align="center">
            <template scope="scope">
              <span v-text="scope.$index + 1"></span>
            </template>
          </el-table-column>
          <el-table-column
            label="项目名称"
            prop="projectName"
            :show-overflow-tooltip="true"
            align="center"
          />
          <el-table-column
            label="数量（想展示“按需”请
填写999）"
            prop="quantity"
            :show-overflow-tooltip="true"
            align="center"
          >
            <template slot-scope="scope">
              <input
                type="text"
                name=""
                id=""
                placeholder="请填写数量"
                v-model="scope.row.quantity"
                :disabled="forbidden"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="houseDelete(scope.$index, 6)"
                :disabled="forbidden"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-button class="relevance" @click="relevance(6)" v-if="!forbidden"
          >关联项目<i class="el-icon-plus"></i
        ></el-button>
      </div>
      <div class="contentTitle">月子餐</div>
      <div class="fgx"></div>
      <div class="content1">
        <div class="postpartum">
          <div class="postpartum">
            <div class="title5">
              <p class="required">*</p>
              <p>每日正餐</p>
            </div>
            <div>
              <input
                type="text"
                placeholder="请输入"
                class="inputs"
                v-model="ruleForm.dailyMeals"
                :disabled="forbidden"
              />
            </div>
            <div class="units">顿</div>
          </div>
          <div class="jia">+</div>
          <div class="postpartum">
            <div class="title5">
              <p class="required">*</p>
              <p>每日加餐</p>
            </div>
            <div>
              <input
                type="text"
                placeholder="请输入"
                class="inputs"
                v-model="ruleForm.dailySnacks"
                :disabled="forbidden"
              />
            </div>
            <div class="units">顿</div>
          </div>
        </div>
        <div class="postpartum">
          <div class="title5">
            <p class="required">*</p>
            <p>每日餐标</p>
          </div>
          <div>
            <input
              type="text"
              placeholder="请输入"
              class="inputs"
              v-model="ruleForm.mealStandard"
              :disabled="forbidden"
            />
          </div>
          <div class="units">顿</div>
        </div>
        <!-- <div class="BackgroundPicture">
       <div class="uploadTitle"><p style="color: #F84343;">*</p> <p>补充说明</p></div>
       <div class="upload">
        <image-upload :limit="8" :isShowTip="false" v-model="ruleForm.additionalNotes" v-if="!forbidden"/>
        <div v-if="forbidden">
                    <img :src="item" alt="" v-for="item,index in ruleForm.additionalNotes" :key="index" style="width: 146px;height: 146px;margin-right: 10px;    border: 1px solid #c0ccda;
    border-radius: 6px;">
                    </div>
       </div>
    </div> -->
        <div class="btn" v-if="!forbidden">
          <el-button type="primary" @click="confirm">确定</el-button>
          <el-button @click="cancel">取消</el-button>
        </div>
        <div class="btn" v-if="forbidden">
          <el-button @click="cancel">返回</el-button>
        </div>
        <div class="btn" v-if="auditInfoList.auditStatus == 0">
          <el-button type="primary" @click="pass">通过</el-button>
          <el-button @click="reject">驳回</el-button>
        </div>
        <div class="btn">
          <el-button type="success" v-if="auditInfoList.auditStatus == 1"
            >已通过</el-button
          >
          <el-button type="warning" v-if="auditInfoList.auditStatus == 2"
            >已驳回</el-button
          >
        </div>
      </div>
    </div>
    <!--关联房型-->
    <el-dialog
      title="选择房型/改套餐类型仅支持关联1个房型"
      :visible.sync="dialogRole"
      width="50%"
      v-if="showType == 1"
    >
      <!--关联房型 房型-->
      <el-table
        :data="roleList"
        class="tables"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column
          label="房间照片"
          prop="name"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            <img
              :src="scope.row.suitePhotos ? scope.row.suitePhotos[0] : ''"
              min-width="70"
              height="70"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="名称"
          prop="roomName"
          width="150"
          align="center"
        />
        <el-table-column
          label="房型"
          prop="roomType"
          width="150"
          align="center"
        >
        </el-table-column>
        <el-table-column
          label="朝向"
          prop="orientation"
          width="150"
          align="center"
        >
        </el-table-column>
        <el-table-column label="楼层" width="150" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.minFloor }}-{{ scope.row.maxFloor }}楼</p>
          </template>
        </el-table-column>
        <el-table-column label="面积" width="150" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.minArea }}-{{ scope.row.maxArea }}m²</p>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :total="total"
      >
      </el-pagination>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="addHouse">{{showType==1?'去创建新房型':showType==2?'去创建妈妈用品':showType==3?'去创建宝宝用品':showType==4||showType==5?'去创建护理项目':''}}</el-button> -->
        <el-button @click="dialogRole = false">取 消</el-button>
        <el-button type="primary" @click="houseConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <!--关联房型-->
    <el-dialog
      title="选择用品"
      :visible.sync="dialogUse"
      width="50%"
      v-if="showType == 2 || showType == 3"
      @close="closeDialog"
    >
      <!--关联房型 用品-->
      <el-table
        :data="useList"
        class="tables"
        @selection-change="handleSelectionChange"
        row-key="supplyId"
        ref="suppliesTable"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true">
        </el-table-column>
        <el-table-column label="用品品牌" prop="brandName" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.brandName }}</p>
          </template>
        </el-table-column>
        <el-table-column label="用品名称" prop="supplyName" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.supplyName }}</p>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :total="total"
      >
      </el-pagination>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="addHouse">{{showType==1?'去创建新房型':showType==2?'去创建妈妈用品':showType==3?'去创建宝宝用品':showType==4||showType==5?'去创建护理项目':''}}</el-button> -->
        <el-button @click="dialogUse = false">取 消</el-button>
        <el-button type="primary" @click="houseConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <!--关联房型-->
    <el-dialog
      title="选择护理"
      :visible.sync="dialogNurse"
      width="50%"
      v-if="showType == 4 || showType == 5"
      @close="closeNursingCare"
    >
      <!--关联房型 护理-->
      <el-table
        :data="nurseList"
        class="tables"
        @selection-change="handleSelectionChange"
        v-if="showType == 4 || showType == 5"
        row-key="projectId"
        ref="nursingCareTable"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true">
        </el-table-column>
        <el-table-column label="护理项目" prop="projectName" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.projectName }}</p>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :total="total"
      >
      </el-pagination>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="addHouse">{{showType==1?'去创建新房型':showType==2?'去创建妈妈用品':showType==3?'去创建宝宝用品':showType==4||showType==5?'去创建护理项目':''}}</el-button> -->
        <el-button @click="dialogNurse = false">取 消</el-button>
        <el-button type="primary" @click="houseConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="选择护理"
      :visible.sync="dialogNurse"
      width="50%"
      v-if="showType == 6"
      @close="closeNursingCare"
    >
      <!--关联房型 护理-->
      <el-table
        :data="nurseList"
        class="tables"
        @selection-change="handleSelectionChange"
        v-if="showType == 6"
        row-key="projectId"
        ref="nursingCareTable"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true">
        </el-table-column>
        <el-table-column label="产康项目" prop="projectName" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.projectName }}</p>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :total="total"
      >
      </el-pagination>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" @click="addHouse">{{showType==1?'去创建新房型':showType==2?'去创建妈妈用品':showType==3?'去创建宝宝用品':showType==4||showType==5?'去创建护理项目':''}}</el-button> -->
        <el-button @click="dialogNurse = false">取 消</el-button>
        <el-button type="primary" @click="houseConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  houseList,
  usePage,
  nursePage,
  save,
  info,
  update,
} from "@/api/platform/discountPackage";
import ImageUpload from "@/components/ImageUpload/index";
import { giftList } from "@/api/platform/public";
export default {
  name: "app",
  components: { ImageUpload },
  data() {
    return {
      giftList: [],
      rejectionReasonList: "",
      rejectionReason: "",
      dialogVisibleReject: false,
      audit: "",
      auditId: "",
      forbidden: false,
      auditInfoList: "",
      packageId: "",
      showType: "", //1房型 2妈妈用品 3宝宝用品 4妈妈护理 5宝宝护理
      houseuseList: [], //房型选择数据
      mamauseList: [], //妈妈用品选择数据
      babyuseList: [], //宝宝用品选择数据
      mamanurseList: [], //宝宝护理选择数据
      babynurseList: [], //妈妈护理选择数据
      postnuresList: [], //产康选择数据
      total: 0,
      from: {
        pageSize: 10,
        pageNum: 1,
      },
      dialogRole: false,
      dialogUse: false,
      dialogNurse: false,
      radio: "1",
      roleList: [], //关联房型 房型列表
      useList: [], //关联宝宝，妈妈用品列表
      nurseList: [], //关联宝宝，妈妈护理列表
      ruleForm: {
        sortKey: "", //排序
        contractGiftId: "",
        packageName: "", //套餐名称
        packagePrice: "", //套餐价格
        stayDays: "", //入住天数
        suiteList: [], //房型
        mamaSuppliesList: [], //妈妈用品
        babySuppliesList: [], //宝宝用品
        isFamilyAccommodation: -1, //家人陪伴
        maternityCareModel: "", //母婴照护模式
        attendantType: "", //陪护人员类型
        attendantConfiguration: "", //陪护人员配置
        mamaNursingList: [], //妈妈护理
        babyNursingList: [], //宝宝护理
        postpartumList: [], //产康项目
        dailyMeals: "", //每日正餐
        dailySnacks: "", //每日加餐
        mealStandard: "", //每日餐标
        additionalNotes: [], //补充说明
        textPhotos: [], //图文详情、
        packagePhotos: [],
        packageVideos: [],
      },
      rules: {
        name: [
          { required: true, message: "请输入活动名称", trigger: "blur" },
          { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" },
        ],
      },
      suppliesList: [],
    };
  },
  created() {
    let rejectionReasonList = this.$route.query.rejectionReason;
    this.rejectionReasonList = rejectionReasonList;
    this.forbidden = this.$route.query.forbidden ? true : false;
    let packageId = this.$route.query.packageId;
    this.packageId = packageId;
    if (packageId) {
      this.info(packageId);
    }
    this.getList();
  },
  methods: {
    getList() {
      //列表
      this.loading = true;
      let data = {
        pageSize: 1000,
        pageNum: 1,
      };
      giftList(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.giftList = res.data;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    inpt(type) {
      if (type == 1) {
        console.log("11111111");
        var reg =
          /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        var money = this.ruleForm.packagePrice;
        if (reg.test(money)) {
        } else {
          this.$message("请输入正确的价格");
          this.ruleForm.packagePrice = "";
        }
      } else if (type == 2) {
        if (!Number.isInteger(+this.ruleForm.stayDays)) {
          this.$message("请输入正确的天数");
          this.ruleForm.stayDays = "";
        }
      }
    },
    reject() {
      //驳回
      this.dialogVisibleReject = true;
    },
    pass() {
      //通过
      this.$confirm("是否审核通过?", "审核通过", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // this.$message({
          //   type: 'success',
          //   message: '审核通过!'
          // });
          let data = {
            auditId: this.auditId,
            auditResult: true,
          };
          audit(data).then((res) => {
            if (res.code == 200) {
              this.$message(res.msg);
              this.$router.go(-1);
              return;
            } else {
              this.$message(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消审核",
          });
        });
    },
    info(packageId) {
      //查询月子套房信息
      info(packageId).then((res) => {
        if (res.code == 200) {
          let data = res.data;
          this.ruleForm = data;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    handleSizeChange(val) {
      this.from.pageSize = val;
      this.relevance(this.showType);
    },
    handleCurrentChange(val) {
      this.from.pageNum = val;
      this.relevance(this.showType);
    },
    relevance(type) {
      //关联房型
      // type 1房型 2妈妈用品 3宝宝用品 4妈妈护理 5宝宝护理6产康项目
      this.showType = type;
      if (type == 1) {
        this.dialogRole = true;
        houseList(this.from).then((res) => {
          if (res.code == 200) {
            this.useList = [];
            this.nurseList = [];
            this.roleList = res.rows;
            this.total = res.total;
            this.$message(res.msg);
          } else {
            this.$message(res.msg);
          }
        });
      }
      if (type == 2 || type == 3) {
        this.dialogUse = true;
        type == 2
          ? (this.from.category = "MAMA")
          : (this.from.category = "BABY");
        usePage(this.from).then((res) => {
          if (res.code == 200) {
            this.roleList = [];
            this.nurseList = [];
            this.useList = res.rows;
            this.total = res.total;
            this.$message(res.msg);
            console.log(this.useList);
          } else {
            this.$message(res.msg);
          }
        });
      }
      if (type == 4 || type == 5 || type == 6) {
        this.dialogNurse = true;
        type == 4
          ? (this.from.category = "MAMA")
          : type == 5
          ? (this.from.category = "BABY")
          : (this.from.category = "ck");
        nursePage(this.from).then((res) => {
          if (res.code == 200) {
            this.roleList = [];
            this.useList = [];
            this.nurseList = res.rows;
            this.total = res.total;
            this.$message(res.msg);
            console.log(this.nurseList);
          } else {
            this.$message(res.msg);
          }
        });
      }
    },
    addHouse() {
      //去创建新房型
      // showType==1?'去创建新房型':showType==2?'去创建妈妈用品':showType==3?'去创建宝宝用品':showType==4||showType==5?'去创建护理项目':''
      if (this.showType == 1) {
        this.$router.push({
          path: "/addMaternitySuite",
        });
      } else if (this.showType == 2 || this.showType == 3) {
        this.$router.push({
          path: "/suppliesManagement",
        });
      } else if (this.showType == 4 || this.showType == 5) {
        this.$router.push({
          path: "/nursingProject",
        });
      }
    },
    handleSelectionChange(val) {
      if (this.showType == 1) {
        this.houseuseList = val; //房型选择数据
      }
      if (this.showType == 2) {
        val.map((item) => {
          item.suppliesId = item.supplyId;
          item.quantity = "";
        });
        this.mamauseList = val; //妈妈用品选择数据
      }
      if (this.showType == 3) {
        val.map((item) => {
          item.suppliesId = item.supplyId;
          item.quantity = "";
        });
        this.babyuseList = val; //宝宝用品选择数据
      }
      if (this.showType == 4) {
        this.mamanurseList = val; //宝宝护理选择数据
      }
      if (this.showType == 5) {
        this.babynurseList = val; //妈妈护理选择数据
      }
      if (this.showType == 6) {
        this.postnuresList = val; //产康项目
      }
    },
    houseConfirm() {
      //房型确定
      if (this.showType == 1) {
        this.ruleForm.suiteList = this.houseuseList;
      }
      if (this.showType == 2) {
        this.ruleForm.mamaSuppliesList = this.mamauseList;
        this.ruleForm.mamaSuppliesList.forEach((item) => {
          item.quantity = "";
        });
      }
      if (this.showType == 3) {
        this.ruleForm.babySuppliesList = this.babyuseList;
        this.ruleForm.babySuppliesList.forEach((item) => {
          item.quantity = "";
        });
      }
      if (this.showType == 4) {
        this.ruleForm.mamaNursingList = this.mamanurseList;
        this.ruleForm.mamaNursingList.forEach((item) => {
          item.quantity = "";
        });
      }
      if (this.showType == 5) {
        this.ruleForm.babyNursingList = this.babynurseList;
        this.ruleForm.babyNursingList.forEach((item) => {
          item.quantity = "";
        });
      }
      if (this.showType == 6) {
        this.ruleForm.postpartumList = this.postnuresList;
        this.ruleForm.postpartumList.forEach((item) => {
          item.quantity = "";
        });
      }
      (this.dialogRole = false),
        (this.dialogUse = false),
        (this.dialogNurse = false);
      this.roleList = []; //关联房型 房型列表
      this.useList = []; //关联宝宝，妈妈用品列表
      this.nurseList = []; //关联宝宝，妈妈护理列表
    },
    houseDelete(index, showType) {
      //房型删除
      console.log(index, showType);
      if (showType == 1) {
        this.ruleForm.suiteList.splice(index, 1);
      }
      if (showType == 2) {
        this.ruleForm.mamaSuppliesList.splice(index, 1);
      }
      if (showType == 3) {
        this.ruleForm.babySuppliesList.splice(index, 1);
      }
      if (showType == 4) {
        this.ruleForm.mamaNursingList.splice(index, 1);
      }
      if (showType == 5) {
        this.ruleForm.babyNursingList.splice(index, 1);
      }
      if (showType == 6) {
        this.ruleForm.postpartumList.splice(index, 1);
      }
    },
    cancel() {
      //取消
      this.$router.go(-1);
    },
    confirm() {
      //确定
      console.log(this.ruleForm);
      let data = this.ruleForm;
      if (data.packageName == "") {
        this.$message("请输入套餐名称");
        return;
      }
      // if (data.packagePrice=='') {
      //     this.$message('请输入套餐价格');
      //     return
      // }
      if (data.stayDays == "") {
        this.$message("请输入入住天数");
        return;
      }
      if (!data.packagePhotos || data.packagePhotos.length == 0) {
        this.$message("请上传套餐图片");
        return;
      }
      if (!data.textPhotos || data.textPhotos.length == 0) {
        this.$message("请上传图文详情");
        return;
      }
      if (data.suiteList.length == 0) {
        this.$message("请选择房型");
        return;
      }
      if (data.mamaSuppliesList.length == 0) {
        this.$message("请选择妈妈用品");
        return;
      }

      if (data.babySuppliesList.length == 0) {
        this.$message("请选择宝宝用品");
        return;
      }
      if (data.isFamilyAccommodation == -1) {
        this.$message("请选择家人陪伴");
        return;
      }
      if (data.maternityCareModel == "") {
        this.$message("请选择母婴照护模式");
        return;
      }
      if (data.attendantType == "") {
        this.$message("请选择陪护人员类型");
        return;
      }
      if (data.attendantConfiguration == "") {
        this.$message("请选择陪护人员配置");
        return;
      }
      if (data.mamaNursingList.length == 0) {
        this.$message("请选择妈妈护理");
        return;
      }
      if (data.babyNursingList.length == 0) {
        this.$message("请选择宝宝护理");
        return;
      }
      if (data.postpartumList.length == 0) {
        this.$message("请选择产康服务");
        return;
      }
      if (data.dailyMeals == "") {
        this.$message("请输入每日正餐");
        return;
      }
      if (data.dailySnacks == "") {
        this.$message("请输入每日加餐");
        return;
      }
      if (data.mealStandard == "") {
        this.$message("请输入每日餐标");
        return;
      }
      // if (data.additionalNotes.length == 0) {
      //     this.$message('请上传补充说明');
      //     return
      // }
      let mamaSuppliesList = this.ruleForm.mamaSuppliesList;
      let babySuppliesList = this.ruleForm.babySuppliesList;
      let mamaNursingList = this.ruleForm.mamaNursingList;
      let babyNursingList = this.ruleForm.babyNursingList;
      let postpartumList = this.ruleForm.postpartumList;
      for (let i = 0; i < mamaSuppliesList.length; i++) {
        if (
          mamaSuppliesList[i].quantity == null ||
          mamaSuppliesList[i].quantity == ""
        ) {
          this.$message("妈妈用品数量不能为空");
          return;
        }
      }
      for (let i = 0; i < babySuppliesList.length; i++) {
        if (
          babySuppliesList[i].quantity == null ||
          babySuppliesList[i].quantity == ""
        ) {
          this.$message("宝宝用品数量不能为空");
          return;
        }
      }
      for (let i = 0; i < mamaNursingList.length; i++) {
        console.log(
          mamaNursingList[i].quantity == null,
          mamaNursingList[i].quantity
        );
        if (
          mamaNursingList[i].quantity == null ||
          mamaNursingList[i].quantity == ""
        ) {
          this.$message("妈妈护理数量不能为空");
          return;
        }
      }
      for (let i = 0; i < babyNursingList.length; i++) {
        if (
          babyNursingList[i].quantity == null ||
          babyNursingList[i].quantity == ""
        ) {
          this.$message("宝宝护理数量不能为空");
          return;
        }
      }
      for (let i = 0; i < postpartumList.length; i++) {
        if (
          postpartumList[i].quantity == null ||
          postpartumList[i].quantity == ""
        ) {
          this.$message("产康服务数量不能为空");
          return;
        }
      }
      if (!this.packageId) {
        this.ruleForm.mamaSuppliesList.forEach((item) => {
          item.suppliesId = item.supplyId;
        });
        this.ruleForm.babySuppliesList.forEach((item) => {
          item.suppliesId = item.supplyId;
        });
        this.ruleForm.isFamilyAccommodation = !data.isFamilyAccommodation;
        save(this.ruleForm).then((res) => {
          console.log(res);
          if (res.code == 200) {
            this.$message(res.msg);
            this.$router.go(-1);
            return;
          } else {
            this.$message(res.msg);
          }
        });
      }
      if (this.packageId) {
        if (this.ruleForm.suiteList.length > 0) {
          this.ruleForm.suiteId = this.ruleForm.suiteList[0].suiteId;
        }
        update(this.ruleForm).then((res) => {
          if (res.code == 200) {
            this.$message(res.msg);
            this.$router.go(-1);
            return;
          } else {
            this.$message(res.msg);
          }
        });
      }

      //     if(!this.suiteId){
      //     save(this.ruleForm).then(res => {
      //         console.log(res);
      //         if(res.code==200){
      //             this.$message(res.msg);
      //             this.$router.go(-1);
      //         return
      //         }else{
      //             this.$message(res.msg);
      //         }
      //     });
      // }
      // if(this.suiteId){
      //     update(this.ruleForm).then(res => {
      //         console.log(res);
      //         if(res.code==200){
      //             this.$message(res.msg);
      //             this.$router.go(-1);
      //         return
      //         }else{
      //             this.$message(res.msg);
      //         }
      //     });
      // }
    },
    closeDialog() {
      this.$refs.suppliesTable.clearSelection();
      this.from.pageNum = 1;
    },
    closeNursingCare() {
      //nursingCareTable
      this.$refs.nursingCareTable.clearSelection();
      this.from.pageNum = 1;
    },
  },
};
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;

  .title {
    font-size: 22px;
    font-weight: bold;
  }

  .title1 {
    font-size: 14px;
  }
}

.content {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
  padding: 20px 20px 24px 20px;

  .contentTitle {
    color: #17191a;
    font-size: 20px;
    margin-top: 30px;
  }

  .fgx {
    width: 100%;
    height: 2px;
    background: #ebebeb;
    margin: 20px 0;
  }

  .input {
    width: 20%;
  }

  .content1 {
    margin-left: 50px;
  }

  .unit {
    margin-left: 6px;
  }

  .title5 {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;

    .required {
      color: red;
    }
  }
}

.relevance {
  margin-top: 10px;
}

.postpartum {
  display: flex;
  align-items: center;
}

.inputs {
  width: 80px;
  height: 32px;
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  margin-left: 16px;
}

.units {
  width: 30px;
  height: 32px;
  background: #dcdcdc;
  border: 1px solid #dcdcdc;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
}

.jia {
  font-size: 28px;
  margin: 0 8px;
}

.BackgroundPicture {
  display: flex;
  margin-top: 20px;

  .uploadTitle {
    display: flex;
    color: #17191a;
    font-size: 14px;
    width: 100px;
    justify-content: flex-end;
    padding: 0 12px 0 0;
  }

  .el-upload--picture-card {
    width: 50px;
    height: 50px;
  }
}

.btn {
  margin: 20px auto;
  text-align: center;
}

.dialogTitle {
  display: flex;
  align-items: center;
}

.tables {
  margin-bottom: 10px;
}

.auditContent {
  width: 100%;
  padding: 20px 24px;
  background: #ff6c11;
  border-radius: 6px;
  font-size: 14px;
  line-height: 14px;
}

.pass {
  display: flex;
  align-items: center;
  color: #000000;
}

.passCause {
  color: #000000;
  margin-left: 20px;
}
</style>
