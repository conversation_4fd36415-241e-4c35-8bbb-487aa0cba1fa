<template>
  <div class="app" v-loading="loading">
    <!-- <div class="head">
            <div class="title">房间管理</div>
            <div class="title1">Pages/房间管理</div>
        </div> -->
    <div class="content">
      <div class="contentHead">
        <h2 class="title2">房间管理</h2>
        <el-button type="primary" @click="addRoom">新增房间</el-button>
        <div class="roomMessage">
          <div class="sel">
            <p class="selTitle">楼层</p>
            <el-select
              v-model="ruleForm.floorNumber"
              placeholder="全部"
              clearable
              style="width: 159px; margin-left: 10px"
            >
              <el-option
                v-for="(item, index) in floorNumberList"
                :key="index"
                :label="item.label"
                :value="item.label"
              >
              </el-option>
            </el-select>
          </div>
          <div class="sel">
            <p class="selTitle">入住状态</p>
            <el-select
              v-model="ruleForm.occupancyStatus"
              placeholder="全部"
              clearable
              style="width: 159px; margin-left: 10px"
            >
              <el-option
                v-for="item in occupancyStatusList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              >
              </el-option>
            </el-select>
          </div>
          <div>
            <el-button type="primary" @click="inquire">查询</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="record" @click="history">历史记录</div>
    <div v-for="(item, index) in listData" :key="index">
      <div class="floor">{{ item.floorNumber }}</div>
      <div class="content1">
        <div v-for="(res, indexs) in item.rooms" :key="indexs" class="content2">
          <div class="content1_1">
            <div class="content1_2">
              <p class="content1Title1">{{ res.suiteName }}</p>
              <p class="content1Title2">
                <!-- <span class="redact" @click="compile(res)" v-if="res.occupancyStatus !== '2'">编辑</span> -->
                <span class="redact" @click="compile(res)">编辑</span>
                <!-- <span class="checkOut" v-if="res.occupancyStatus === '2'">退房</span> -->
                <span
                  class="checkOut"
                  type="text"
                  @click="checkOut(res.roomId)"
                  v-if="res.occupancyStatus === '2'"
                  >退房</span
                >
              </p>
            </div>
            <div class="content1_3">
              <img
                :src="res.photos"
                alt=""
                style="width: 110px; height: 110px"
              />
              <el-progress
                type="circle"
                :percentage="
                  res.totalDaysBooked ? (20 / res.totalDaysBooked) * 100 : 0
                "
                :width="110"
                :height="110"
                :format="format"
              ></el-progress>
            </div>
            <div class="content1_5">
              <div class="content1Title3">
                <div class="content1Title3_1">
                  <p class="content1Title3_3">入住人员：</p>
                  <p class="content1Title3_4">
                    {{ res.customerName ? res.customerName : "无" }}
                  </p>
                </div>
                <div class="content1Title3_2">
                  <p class="content1Title3_3">入住状态：</p>
                  <p class="content1Title3_4">
                    <span
                      v-if="res.occupancyStatus === '0' || !res.occupancyStatus"
                      >未入住</span
                    >
                    <span v-if="res.occupancyStatus === '1'">待入住</span>
                    <span v-if="res.occupancyStatus === '2'">已入住</span>
                  </p>
                </div>
              </div>
              <div class="content1Title3">
                <div class="content1Title3_1">
                  <p class="content1Title3_3">负责人员：</p>
                  <p class="content1Title3_4">
                    {{ res.staffName ? staff(res.staffName) : "无" }}
                  </p>
                </div>
                <div class="content1Title3_2">
                  <p class="content1Title3_3">房间朝向：</p>
                  <p class="content1Title3_4" id="content1Title3_4">
                    {{ res.orientation }}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="backlog" v-if="res.isTodo" @click="backlog(res)">
            <img
              src="../../../image/Time_fill@2x (1).png"
              alt=""
              style="width: 18px; height: 18px"
            />
            <p class="backlogName">待办事项</p>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="请选择新增楼层数"
      :visible.sync="floorDialogVisible"
      width="30%"
    >
      <div class="floorTab1">
        <p
          :class="item.checked ? 'floorTabAActive' : 'floorTab'"
          v-for="(item, index) in floorList"
          :key="index"
          @click="tab(item, index)"
        >
          {{ item.name }}
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="floorDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="floorConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="添加房间" :visible.sync="roomDialogVisible" width="50%">
      <div v-for="(item, index) in floors" :key="index">
        <div class="addRoom">
          <p class="roomName">{{ item.floorNumber }}</p>
          <p
            class="roomNumber"
            v-for="(res, indexss) in item.rooms"
            :key="indexss"
            @click="update(res, index)"
          >
            {{ res.roomNumber }}
          </p>
          <p class="addbtn" @click="addRoomNumber(index, item)">添加</p>
        </div>
        <div class="addRoomNumber" v-if="roomIndex == index && addShow">
          <div class="addRoomNumber1">
            <div>房号</div>
            <input
              type="text"
              placeholder="请输入房号"
              class="addRoomNumber2"
              v-model="from.roomNumber"
            />
          </div>
          <div class="addRoomNumber1">
            <p class="addRoomNumber3">房型</p>
            <el-select
              v-model="from.suiteId"
              placeholder="请选择"
              clearable
              style="width: 90px; margin-left: 10px"
            >
              <el-option
                v-for="item in houseTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="addRoomNumber1">
            <p class="addRoomNumber3">朝向</p>
            <el-select
              v-model="from.orientation"
              placeholder="请选择"
              clearable
              style="width: 90px; margin-left: 10px"
            >
              <el-option
                v-for="item in orientationList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictLabel"
              >
              </el-option>
            </el-select>
          </div>
          <div class="addRoomNumber1">
            <p class="addRoomNumber4" @click="add">提交</p>
            <p class="addRoomNumber5" @click="remove">删除</p>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="roomCancel">取 消</el-button>
        <el-button type="primary" @click="roomConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  list,
  addLevel,
  listLevel,
  listLevelRoom,
  addRoom,
  options,
  roomOptions,
  checkOut,
} from "@/api/platform/roomManagement";
export default {
  name: "app",
  data() {
    return {
      roomIndex: "",
      from: {
        roomNumber: "",
        suiteId: "",
        orientation: "",
        levelId: "",
      },
      addShow: false,
      percentage: "",
      orientationList: [
        {
          dictLabel: "东",
          dictValue: "1",
        },
        {
          dictLabel: "南",
          dictValue: "2",
        },
        {
          dictLabel: "西",
          dictValue: "3",
        },
        {
          dictLabel: "北",
          dictValue: "4",
        },
        {
          dictLabel: "东南",
          dictValue: "5",
        },
        {
          dictLabel: "西南",
          dictValue: "6",
        },
        {
          dictLabel: "东北",
          dictValue: "7",
        },
        {
          dictLabel: "西北",
          dictValue: "7",
        },
      ],
      houseTypeList: [],
      floors: [],
      floorList: [
        {
          id: 1,
          name: "一楼",
          checked: false,
        },
        {
          id: 2,
          name: "二楼",
          checked: false,
        },
        {
          id: 3,
          name: "三楼",
          checked: false,
        },
        {
          id: 4,
          name: "四楼",
          checked: false,
        },
        {
          id: 5,
          name: "五楼",
          checked: false,
        },
        {
          id: 6,
          name: "六楼",
          checked: false,
        },
        {
          id: 7,
          name: "七楼",
          checked: false,
        },
        {
          id: 8,
          name: "八楼",
          checked: false,
        },
        {
          id: 9,
          name: "九楼",
          checked: false,
        },
        {
          id: 10,
          name: "十楼",
          checked: false,
        },
        {
          id: 11,
          name: "十一楼",
          checked: false,
        },
        {
          id: 12,
          name: "十二楼",
          checked: false,
        },
        {
          id: 13,
          name: "十三楼",
          checked: false,
        },
        {
          id: 14,
          name: "十四楼",
          checked: false,
        },
        {
          id: 15,
          name: "十五楼",
          checked: false,
        },
        {
          id: 16,
          name: "十六楼",
          checked: false,
        },
        {
          id: 17,
          name: "十七楼",
          checked: false,
        },
        {
          id: 18,
          name: "十八楼",
          checked: false,
        },
        {
          id: 19,
          name: "十九楼",
          checked: false,
        },
        {
          id: 20,
          name: "二十楼",
          checked: false,
        },
        {
          id: 21,
          name: "二十一楼",
          checked: false,
        },
        {
          id: 22,
          name: "二十二楼",
          checked: false,
        },
        {
          id: 23,
          name: "二十三楼",
          checked: false,
        },
        {
          id: 24,
          name: "二十四楼",
          checked: false,
        },
        {
          id: 25,
          name: "二十五楼",
          checked: false,
        },
        {
          id: 26,
          name: "二十六楼",
          checked: false,
        },
        {
          id: 27,
          name: "二十七楼",
          checked: false,
        },
        {
          id: 28,
          name: "二十八楼",
          checked: false,
        },
        {
          id: 29,
          name: "二十九楼",
          checked: false,
        },
        {
          id: 30,
          name: "三十楼",
          checked: false,
        },
      ],
      floorDialogVisible: false, //添加楼层弹框
      roomDialogVisible: false, //添加房间弹框
      listData: [],
      loading: true,
      ruleForm: {
        floorNumber: "", //楼层
        occupancyStatus: "", //入住状态
      },
      floorNumberList: [],
      occupancyStatusList: [
        {
          dictValue: "0",
          dictLabel: "未入住",
        },
        {
          dictValue: "1",
          dictLabel: "待入住",
        },
        {
          dictValue: "2",
          dictLabel: "已入住",
        },
      ],
    };
  },
  mounted() {},
  created() {
    this.getList();
    this.roomOptions();
  },
  methods: {
    staff(e) {
      if (e) {
        let staffName = e.split(",")[0];
        return staffName;
      }
    },
    history() {
      //历史记录
      this.$router.push({
        path: "/history",
      });
    },
    backlog(item) {
      this.$router.push({
        path: "/backlog",
        query: { item: JSON.stringify(item) },
      });
    },
    compile(item) {
      this.$router.push({
        path: "/addroomManagement",
        query: { item: JSON.stringify(item) },
      });
    },
    update(res, index) {
      //修改
      let data = this.from;
      data.roomNumber = res.roomNumber;
      data.suiteId = res.suiteId;
      data.orientation = res.orientation;
      data.levelId = res.levelId;
      data.roomId = res.roomId;
      this.roomIndex = index;
      this.addShow = true;
    },
    add() {
      //提交
      let data = this.from;
      if (data.roomNumber == "") {
        this.$message("请输入房间号");
        return;
      }
      if (data.suiteId == "") {
        this.$message("请选择房型");
        return;
      }
      if (data.orientation == "") {
        this.$message("请选择房间朝向");
        return;
      }
      addRoom(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.loading = false;
          this.listLevelRoom();
          return;
        } else {
          this.$message(res.msg);
        }
      });
      this.addShow = false;
    },
    remove(index, indexs) {
      //删除
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.addShow = false;
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    addRoomNumber(index, item) {
      //添加房号
      this.addShow = true;
      this.roomIndex = index;
      // this.from.levelId = item.levelId;
      this.from = {
        roomNumber: "",
        suiteId: "",
        orientation: "",
        levelId: item.levelId,
      };
    },
    addRoom() {
      //新增房间
      listLevel().then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.loading = false;
          this.floors = res.data;
          this.floorList.forEach((item) => {
            res.data.forEach((row) => {
              if (item.name == row.floorNumber) {
                item.checked = true;
              }
            });
          });
          this.floorDialogVisible = true;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    roomOptions() {
      //查询楼层下拉选项数据
      roomOptions().then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.floorNumberList = res.data;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    houseList() {
      //房型
      options().then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.loading = false;
          this.houseTypeList = res.data;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    roomCancel() {
      //房间取消
      this.roomDialogVisible = false;
      this.getList();
    },
    roomConfirm() {
      //房间确定
      this.roomDialogVisible = false;
      this.getList();
    },
    floorConfirm() {
      //楼层确定
      addLevel(this.floors).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.loading = false;
          this.floors = [];
          this.floorDialogVisible = false;
          this.roomDialogVisible = true;
          this.listLevelRoom();
          this.houseList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    listLevelRoom() {
      // 获取楼层房间列表
      listLevelRoom().then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.floors = res.data;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    tab(e, index) {
      this.floorList[index].checked = !this.floorList[index].checked;
      let item = e;
      if (item.checked) {
        this.floors.push({
          floorNumber: e.name,
        });
      } else {
        for (let i = 0; i < this.floors.length; i++) {
          if (this.floors[i].floorNumber == e.name) {
            this.floors.splice(i, 1);
          }
        }
      }
    },
    inquire() {
      this.getList();
    },
    format(percentage) {
      if (percentage) {
        return `${percentage.toFixed(2)}%`;
      } else {
        return "0";
      }
    },
    getList() {
      this.loading = true;
      list(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.data;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    checkOut(roomId) {
      this.$confirm("确定退房吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.loading = true;
        checkOut(roomId)
          .then((res) => {
            this.$modal.msgSuccess("退房成功");
            this.getList();
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.app {
  background: #f0f1f5;
  padding-bottom: 50px;
}

.floorTab1 {
  display: flex;
  flex-wrap: wrap;
}

.floorTab {
  border: 1px solid #ebebeb;
  border-radius: 34px;
  width: 76px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  color: #17191a;
  font-size: 14px;
}

.floorTabAActive {
  background: #3f85ff;
  border-radius: 34px;
  width: 76px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  color: #ffff;
  font-size: 14px;
}

.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;

  .title {
    font-size: 22px;
    font-weight: bold;
  }

  .title1 {
    font-size: 14px;
  }
}

.content {
  margin: 20px 20px;

  .contentHead {
    background: #ffff;
    padding: 5px 14px;
    border-radius: 10px;

    .title2 {
      color: #17191a;
    }
  }
}

.roomMessage {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.sel {
  display: flex;
  align-items: center;
  margin-right: 10px;

  .selTitle {
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
  }
}

#cars {
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  color: #7c7d81;
  font-size: 14px;
  width: 179px;
  height: 32px;
  margin-left: 6px;
}

.floor {
  color: #17191a;
  font-size: 16px;
  margin-left: 20px;
  font-weight: bold;
  margin-top: 30px;
  line-height: 0;
}

.content2 {
  margin-right: 20px;
}

.content1 {
  width: 100%;
  margin-left: 20px;
  position: relative;
  display: flex;
  flex-wrap: wrap;

  .content1_1 {
    background: #ffff;
    padding: 10px 10px;
    width: 280px;
    margin-top: 40px;

    .content1_3 {
      display: flex;
      justify-content: space-between;
      height: 110px;
    }

    .content1_2 {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .content1Title1 {
        font-size: 16px;
        color: #17191a;
        line-height: 10px;
      }

      .content1Title2 {
        font-size: 14px;
        color: #7c7d81;
        line-height: 10px;
      }
    }

    .content1Title3 {
      display: flex;
      font-size: 14px;
      justify-content: space-between;
      line-height: 0;

      .content1Title3_1 {
        display: flex;
      }

      .content1Title3_2 {
        display: flex;
        align-items: center;
      }

      .content1Title3_3 {
        color: #7c7d81;
      }

      .content1Title3_4 {
        color: #17191a;
      }

      #content1Title3_4 {
        color: #f84343;
        font-size: 14px;
      }
    }
  }
}

.redact {
  color: #3f85ff;
  cursor: pointer;
}

.checkOut {
  color: #f84343;
  margin-left: 10px;
}

.backlog {
  width: 90px;
  height: 25px;
  line-height: 0;
  text-align: center;
  background: #f84343;
  color: #ffff;
  display: flex;
  align-items: center;
  font-size: 14px;
  justify-content: space-around;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  position: absolute;
  top: 15px;
  cursor: pointer;
}

.backlogName {
  font-size: 14px;
}

.roomName {
  font-weight: bold;
  font-size: 18px;
}

.roomNumber {
  padding: 2px 10px;
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  text-align: center;
  line-height: 32px;
  margin: 0 16px;
}

.addRoom {
  display: flex;
  align-items: center;
}

.addbtn {
  color: #3f85ff;
  font-size: 14px;
  cursor: pointer;
  margin-left: 10px;
}

.addRoomNumber {
  display: flex;
  align-items: center;
}

.addRoomNumber1 {
  display: flex;
  align-items: center;
}

.addRoomNumber2 {
  width: 100px;
  height: 32px;
  border: 1px solid #dcdcdc;
  margin-left: 5px;
}

.addRoomNumber3 {
  margin: 0 5px;
}

.addRoomNumber4 {
  margin-left: 5px;
  color: #3f85ff;
  font-size: 14px;
  cursor: pointer;
}

.addRoomNumber5 {
  margin-left: 5px;
  color: #f84343;
  font-size: 14px;
  cursor: pointer;
}

.addRoomNumber6 {
  display: flex;
}

.record {
  text-align: right;
  margin-right: 50px;
  color: #7c7d81;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
}
</style>
