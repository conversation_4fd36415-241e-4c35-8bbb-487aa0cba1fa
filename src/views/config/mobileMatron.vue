<template>
    <div class="app" v-loading="loading">
        <!-- <div class="head">
            <div class="title">配送服务</div>
            <div class="title1">Pages/配送服务/移动月嫂</div>
        </div> -->
        <div class="content">
             <div class="title2">移动月嫂</div>
             <el-button type="primary" @click="addCheckManagement">新增</el-button>
             <el-table :data="listData" class="table">
      <el-table-column label="照片" width="120" align="center" >
        <template slot-scope="scope">
                    <img :src="scope.row.nursePhotoUrl[0]"  min-width="70" height="70" style="width: 100px;height: 100px;border-radius: 5px;"/>
          </template>
        </el-table-column>
      <el-table-column label="套餐名称" prop="packageName" :show-overflow-tooltip="true" width="150" align="center" />
      <el-table-column label="商品ID" prop="nurseId" :show-overflow-tooltip="true" width="150" align="center" >
    </el-table-column>
      <el-table-column label="内容" prop="nurseProfile" width="200" align="center">
        <template slot-scope="scope">
  <p class="productDescription">{{ scope.row.nurseProfile }}</p>
</template>    </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope" v-if="scope.row.roleId !== 1">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >编辑</el-button>
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="block">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="100"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>
  </div>
        </div>
        <el-dialog
  title="删除"
  :visible.sync="deleteDialogVisible"
  width="30%">
  <span>是否删除此移动月嫂？</span>
  <span slot="footer" class="dialog-footer">
    <el-button @click="deleteDialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
  </span>
  </el-dialog>
  <!--新增-->
  <el-dialog
  title="新增配送"
  :visible.sync="addShow"
  width="30%">
  <el-form :model="from" :rules="rules" ref="from" label-width="100px" class="demo-ruleForm">
    <el-form-item label="月嫂照片*" style="flex:1;" prop="nursePhotoUrl">
                    <div style="display:flex;flex-wrap: wrap;">
                      <image-upload :oneAll="2" :isShowTip="false" v-model="from.nursePhotoUrl" />
                    </div>
                </el-form-item>
                <el-form-item label="月嫂视频*" style="flex:1;" >
                    <div style="display:flex;flex-wrap: wrap;">
                      <file-upload :limit="1" :isShowTip="false" v-model="from.videos" />
                    </div>
                </el-form-item>
                <el-form-item label="签约礼*" style="flex:1;">
                  <el-select v-model="from.contractGiftId" placeholder="请选择签约礼" clearable
                            style="width: 240px;" >
                            <el-option v-for="item in giftList" :key="item.contractGiftId" :label="item.description"
                                :value="item.contractGiftId" />
                        </el-select>
                </el-form-item>
                <el-form-item label="月嫂简介" prop="nurseProfile">
    <el-input type="textarea" v-model="from.nurseProfile" placeholder="请输入月嫂简介"></el-input>
  </el-form-item>
  <el-form-item label="套餐名称" prop="packageName">
    <el-input v-model="from.packageName" placeholder="请输入套餐名称"></el-input>
  </el-form-item>
  <el-form-item label="图文详情*" style="flex:1;" prop="description">
                    <div style="display:flex;flex-wrap: wrap;">
                      <image-upload :oneAll="3" :limit="6" :isShowTip="false" v-model="from.description" />
                    </div>
                    <div class="imgHint">
                      <img src="http://cdn.xiaodingdang1.com/info-circle-filled.png" alt="" style="width: 14px;height: 14px;">
                      <div>最多可配置六张图片</div>
                    </div>
                </el-form-item>
  </el-form>
  <span slot="footer" class="dialog-footer">
    <el-button @click="addShow = false">取 消</el-button>
    <el-button type="primary" @click="submitForm('from')">确 定</el-button>
  </span>
  </el-dialog>
  <el-dialog :visible.sync="dialogVisible" width="80%">

  <img width="100%" :src="dialogImageUrl" alt="" />
  <i class="el-icon-arrow-left" @click="leftPhoto" v-if="currentIndex>0"
  style="background:rgba(0, 0, 0, 0.3);width:35px;height:70px;text-align: center;line-height: 70px;color:#fff;font-size:23px;position: absolute;top:48%;left:15px;">
  </i>
  <i class="el-icon-arrow-right" @click="rightPhoto" v-if="currentIndex<photoList.length-1"
  style="background:rgba(0, 0, 0, 0.3);width:35px;height:70px;text-align: center;line-height: 70px;color:#fff;font-size:23px;position: absolute;top:48%;right:15px;">
  </i>
  </el-dialog>
    </div>
  </template>

  <script>
  import { page,save,delTable,info,update } from "@/api/platform/mobileMatron";
  import {giftList} from "@/api/platform/public";
  import ImageUpload from "@/components/ImageUpload/index"
  import FileUpload from "@/components/FileUpload/index"
  export default {
    name: "app",
    components: {ImageUpload,FileUpload},
  data() {
    return {
      giftList:[],
      loading:false,
      nowAddress: [], //临时保存地址
            fixedAddress: 'https://txyoss.oss-cn-shenzhen.aliyuncs.com',
            currentIndex: '',//当前图片的下标
            fileCount: 0,
            picNum: 0,
            maxNum: 5,
            photoList: [], //照片列表
            dialogImageUrl: '',
            dialogVisible: false,
            disabled: false,
            total:0,
      ruleForm: {
        pageSize:10,
        pageNum:1
      },
      giftList:[],
      from:{
        contractGiftId:'',
        description:[],//图文详情
        packageName:'',//套餐名称
        nurseProfile:'',//月嫂简介
        nursePhotoUrl:[],//月嫂照片
        videos:[]
      },
      listData:[],
      addShow:false,
      deleteDialogVisible:false,
      nurseId:'',
      rules: {
        nursePhotoUrl: [
            {required: true,message: '请上传月嫂照片', trigger: 'blur' }
          ],
          nurseProfile: [
            {required: true,message: '请输入月嫂简介', trigger: 'blur' }
          ],
          packageName: [
            {required: true,message: '请输入套餐名称', trigger: 'blur' }
          ],
          description: [
            {required: true,message: '请上传图文详情', trigger: 'blur' }
          ],
      }
    }
  },
  created(){
  this.getList()
  this.getgiftList()
  },
  methods:{
    getgiftList(){//列表
        this.loading=true
        let data={
            pageSize:1000,
            pageNum:1
        }
        giftList(data).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.giftList=res.data
                    this.loading=false
                return
                }else{
                    this.$message(res.msg);
                }
            });
      },
  handleSizeChange(val) {
      this.ruleForm.pageSize=val
      this.getList()
      },
      handleCurrentChange(val) {
        this.ruleForm.pageNum=val
        this.getList()
      },
      handleUpdate(row){//编辑
        this.nurseId=row.nurseId
        this.addShow=true
        this.info(row.nurseId)
      },
      handleDelete(row){//删除
        this.nurseId=row.nurseId
        this.deleteDialogVisible=true
      },
      confirmDelete(){//确定删除
        delTable(this.nurseId).then(res=>{
          if(res.code==200){
                    this.$message(res.msg);
                    this.deleteDialogVisible=false
                   this.getList()
                return
                }else{
                    this.$message(res.msg);
                }
        })
      },
      addCheckManagement(){//签到管理
           this.addShow=true
      },
      getList(){//列表
        this.loading=true
        page(this.ruleForm).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.listData=res.rows
                    this.total=res.total
                    this.loading=false
                return
                }else{
                    this.$message(res.msg);
                }
            });
      },
      info(nurseId){//查询月子套房信息
            info(nurseId).then(res => {
                if(res.code==200){
                    this.from=res.data
                    this.$message(res.msg);
                return
                }else{
                    this.$message(res.msg);
                }
            });
        },
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            if(this.nurseId){
              this.$confirm('确定修改该移动月嫂吗, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (this.from.videos=='') {
           this.from.videos=[]
        }
          update(this.from).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.addShow=false
                    this.getList()
                return
                }else{
                    this.$message(res.msg);
                }
            });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消修改'
          });
        });
            }

            if(!this.nurseId){
              this.$confirm('确定新增该移动月嫂吗, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          save(this.from).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.addShow=false
                    this.getList()
                return
                }else{
                    this.$message(res.msg);
                }
            });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消新增'
          });
        });
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
  }
  }
  </script>

  <style scoped lang="scss">
  .head{
    width: 100%;
    height: 80px;
    background: #ffff;
    color: #17191A;
    padding: 15px 20px;
    .title{
        font-size: 22px;
        font-weight: bold;
    }
    .title1{
        font-size: 14px;
    }
  }
  .content{
    background: #ffff;
    margin: 10px 10px;
    border-radius: 5px;
    padding: 24px 20px;
    .title2{
        font-size: 20px;
        color: #17191A;
        margin-bottom: 24px;
    }
    .table{
        margin-top: 20px;
    }
  }
  .block{
    text-align: right;
    margin-top: 20px;
  }
  .imgHint{
    display: flex;
    align-items: center;
  }
  .productDescription{
  overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        -ms-text-overflow: ellipsis;
        text-overflow: ellipsis;
  }
  </style>
