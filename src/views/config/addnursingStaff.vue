<template>
  <div id="app">
    <!-- <div class="head">
              <div class="titles">护理人员配置</div>
              <div class="title1">Pages/护理人员配置</div>
          </div> -->

    <div class="app">
      <div class="auditContent" v-if="auditInfoList.auditStatus == 2">
        <div class="pass">
          <img
            src="http://cdn.xiaodingdang1.com/icon%402x.png"
            alt=""
            style="width: 20px; height: 20px"
          />
          <p>驳回原因</p>
        </div>
        <p class="passCause">{{ auditInfoList.rejectionReason }}</p>
      </div>
      <div class="titleName">基本信息</div>
      <div class="rule"></div>
      <div class="content">
        <div class="content1">
          <!-- <div class="title5">
            <p class="required">*</p>
            <p>人员类型</p>
          </div>
          <div>
            {{ ruleForm.staffPost }}
          </div> -->
          <div class="title5">
            <p class="required">*</p>
            <p>人员类型</p>
          </div>
          <div>
            <el-input
              type="text"
              maxlength="16"
              v-model="ruleForm.staffPost"
              class="textarea"
              placeholder="请输入姓名"
              :disabled="forbidden"
            ></el-input>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>真实姓名</p>
          </div>
          <div>
            <el-input
              type="text"
              maxlength="16"
              v-model="ruleForm.staffName"
              class="textarea"
              placeholder="请输入姓名"
              :disabled="forbidden"
            ></el-input>
          </div>
        </div>
        <div class="hintss">
          <div class="title5"></div>
          <div class="hints">
            <img
              src="http://cdn.xiaodingdang1.com/info-circle-filled.png"
              alt=""
            />
            <p>最多16个字符</p>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>手机号码</p>
          </div>
          <div>
            <el-input
              type="text"
              v-model="ruleForm.staffTel"
              maxlength="11"
              class="textarea"
              placeholder="请输入手机号码"
              :disabled="forbidden"
              @blur="inpt"
            ></el-input>
          </div>
        </div>
        <div class="BackgroundPicture">
          <div class="title5">
            <p class="required">*</p>
            <p>照片</p>
          </div>
          <div class="upload">
            <image-upload
              :oneAll="2"
              :limit="1"
              :isShowTip="false"
              v-model="ruleForm.staffPhotos"
              v-if="!forbidden"
            />
            <div v-if="forbidden">
              <img
                :src="item"
                alt=""
                v-for="(item, index) in ruleForm.staffPhotos"
                :key="index"
                style="
                  width: 146px;
                  height: 146px;
                  margin-right: 10px;
                  border: 1px solid #c0ccda;
                  border-radius: 6px;
                "
              />
            </div>
          </div>
        </div>
        <div class="hintss">
          <div class="title5"></div>
          <div class="hints">
            <img
              src="http://cdn.xiaodingdang1.com/info-circle-filled.png"
              alt=""
            />
            <p>
              请裁剪成正方形，图片大小不超过4M，尺寸不低于400*400，不高于2000*2000
            </p>
          </div>
        </div>
        <!-- <div class="BackgroundPicture">
          <div class="title5"><p class="required">*</p><p>视频</p></div>
         <div class="upload">
          <file-upload :limit="1" :isShowTip="false" v-model="ruleForm.videos" v-if="!forbidden"/>
          <div v-if="forbidden">
            <video  controls="controls" :src="item" alt="" v-for="item,index in ruleForm.videos" :key="index" style=" width: 146px;height: 146px;margin-right: 10px;border: 1px solid #c0ccda;
    border-radius: 6px;"></video>
                    </div>
         </div>
      </div> -->
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>从业开始年份</p>
          </div>
          <div>
            <el-date-picker
              v-model="ruleForm.practiceTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择从业开始年份"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>个人简介</p>
          </div>
          <div>
            <el-input
              type="textarea"
              v-model="ruleForm.staffDesc"
              class="textarea"
              placeholder="请输入内容"
              :disabled="forbidden"
            ></el-input>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>服务人员数量</p>
          </div>
          <div>
            <el-input
              type="number"
              maxlength="16"
              v-model="ruleForm.serviceNum"
              class="textarea"
              placeholder="请输入服务人员数量（人）"
              :disabled="forbidden"
            ></el-input>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>排序</p>
          </div>
          <div>
            <el-input-number
              v-model="ruleForm.sortKey"
              controls-position="right"
              :min="0"
              :max="1000"
            ></el-input-number>
          </div>
        </div>
        <div class="content1" v-if="!forbidden">
          <div class="title5">
            <p class="required">*</p>
            <p>人员标签</p>
          </div>
          <el-tag
            :key="tag"
            v-for="tag in ruleForm.tag"
            closable
            :disable-transitions="false"
            @close="handleCloses(tag)"
            class="tagShow"
          >
            {{ tag }}
          </el-tag>
        </div>
        <div class="content1" v-if="forbidden">
          <div class="title5">
            <p class="required">*</p>
            <p>人员标签</p>
          </div>
          <el-tag
            :key="tag"
            v-for="tag in ruleForm.tag"
            closable
            :disable-transitions="false"
            @close="handleCloses(tag)"
            class="tagShow"
          >
            {{ tag }}
          </el-tag>
        </div>
        <div class="content1" v-if="!forbidden">
          <div class="title5"></div>
          <div class="label">
            <el-tag
              :key="tag"
              v-for="tag in labelList"
              closable
              :disable-transitions="false"
              @close="handleClose(tag)"
              @click="choice(tag)"
              class="tagShow"
            >
              {{ tag }}
            </el-tag>
            <el-input
              class="input-new-tag"
              v-if="inputVisible"
              v-model="inputValue"
              ref="saveTagInput"
              size="small"
              @keyup.enter.native="handleInputConfirm"
              @blur="handleInputConfirm"
            >
            </el-input>
            <el-button
              v-else
              class="button-new-tag"
              size="small"
              @click="showInput"
              style="height: 26px; line-height: 26px"
              :disabled="forbidden"
              >+ 添加标签</el-button
            >
          </div>
        </div>

        <!-- <div class="hint">
          <div class="title5"></div>
          <div class="hints">
              <img src="http://cdn.xiaodingdang1.com/info-circle-filled.png" alt="">
              <p>
                请裁剪成正方形，图片大小不超过4M，尺寸不低于400*400，不高于2000*2000
              </p>
          </div>
      </div> -->
      </div>
      <!-- <div class="titleName">身份信息</div>
      <div class="rule"></div>
      <div class="content">
        <div class="content1" v-if="staffPostId == 1">
          <div class="title5">
            <p class="required">*</p>
            <p>厨师职位</p>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <el-select v-model="ruleForm.qualificationObject.post" placeholder="请选择" clearable
                style="width: 129px; margin-left: 10px" :disabled="forbidden">
                <el-option v-for="item in postList" :key="item.dictValue" :label="item.dictLabel"
                  :value="item.dictLabel" />
              </el-select>
            </div>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>证件类型</p>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <el-select v-model="ruleForm.documentType" placeholder="请选择证件类型" clearable
                style="width: 129px; margin-left: 10px" :disabled="forbidden">
                <el-option v-for="item in documentTypeList" :key="item.dictValue" :label="item.dictLabel"
                  :value="item.dictLabel" />
              </el-select>
            </div>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>证件号码</p>
          </div>
          <div>
            <el-input type="text" placeholder="请输入内容" v-model="ruleForm.documentNumber" maxlength="18" show-word-limit
              :disabled="forbidden">
            </el-input>
          </div>
        </div>
        <div class="BackgroundPicture">
          <div class="title5">
            <p class="required">*</p>
            <p>正面照</p>
          </div>
          <div class="upload">
            <image-upload :limit="1" :isShowTip="false" v-model="ruleForm.photoFront" v-if="!forbidden" />
            <div v-if="forbidden">
              <img :src="item" alt="" v-for="(item, index) in ruleForm.photoFront" :key="index" style="
                  width: 146px;
                  height: 146px;
                  margin-right: 10px;
                  border: 1px solid #c0ccda;
                  border-radius: 6px;
                " />
            </div>
          </div>
        </div>
        <div class="BackgroundPicture">
          <div class="title5">
            <p class="required">*</p>
            <p>反面照</p>
          </div>
          <div class="upload">
            <image-upload :limit="1" :isShowTip="false" v-model="ruleForm.photoBack" v-if="!forbidden" />
            <div v-if="forbidden">
              <img :src="item" alt="" v-for="(item, index) in ruleForm.photoBack" :key="index" style="
                  width: 146px;
                  height: 146px;
                  margin-right: 10px;
                  border: 1px solid #c0ccda;
                  border-radius: 6px;
                " />
            </div>
          </div>
        </div>
      </div> -->
      <div
        class="titleName"
        v-if="
          staffPostId == 2 ||
          staffPostId == 3 ||
          staffPostId == 4 ||
          staffPostId == 5 ||
          staffPostId == 9
        "
      >
        资质信息
      </div>
      <div
        class="rule"
        v-if="
          staffPostId == 2 ||
          staffPostId == 3 ||
          staffPostId == 4 ||
          staffPostId == 5 ||
          staffPostId == 9
        "
      ></div>
      <div class="content">
        <!--孕产医师-->
        <div v-if="staffPostId == 2">
          <div class="content1">
            <div class="title5">
              <p class="required">*</p>
              <p>科室</p>
            </div>
            <div class="roomMessage">
              <div class="sel">
                <el-select
                  v-model="ruleForm.qualificationObject.dept"
                  placeholder="请选择科室"
                  clearable
                  style="width: 139px; margin-left: 10px"
                  :disabled="forbidden"
                >
                  <el-option
                    v-for="item in deptList"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictLabel"
                  />
                </el-select>
              </div>
            </div>
          </div>
          <div class="hintss">
            <div class="title5"></div>
            <div class="hints">
              <img
                src="http://cdn.xiaodingdang1.com/info-circle-filled.png"
                alt=""
              />
              <p>需与卫健委查询结果中的执业范围一致，否则无法通过审核</p>
            </div>
          </div>
          <div class="content1">
            <div class="title5">
              <p class="required">*</p>
              <p>医师执业证书编号</p>
            </div>
            <div>
              <el-input
                type="text"
                placeholder="请输入医师执业证书编号"
                v-model="ruleForm.qualificationObject.licenseNumber"
                maxlength="15"
                show-word-limit
                :disabled="forbidden"
              >
              </el-input>
            </div>
          </div>
          <div class="hintss">
            <div class="title5"></div>
            <div class="hints">
              <img
                src="http://cdn.xiaodingdang1.com/info-circle-filled.png"
                alt=""
              />
              <p>需与卫健委查询结果一致，否则无法通过审核</p>
            </div>
          </div>
          <div class="BackgroundPicture">
            <div class="title5">
              <p class="required">*</p>
              <p>卫健委查询结果</p>
            </div>
            <div class="upload">
              <image-upload
                :limit="1"
                :isShowTip="false"
                v-model="ruleForm.qualificationObject.wjwQueryResult"
                v-if="!forbidden"
              />
              <div v-if="forbidden">
                <img
                  :src="item"
                  alt=""
                  v-for="(item, index) in ruleForm.qualificationObject
                    .wjwQueryResult"
                  :key="index"
                  style="
                    width: 146px;
                    height: 146px;
                    margin-right: 10px;
                    border: 1px solid #c0ccda;
                    border-radius: 6px;
                  "
                />
              </div>
            </div>
          </div>
        </div>
        <!--孕产护士-->
        <div v-if="staffPostId == 3">
          <div class="content1">
            <div class="title5">
              <p class="required">*</p>
              <p>护士执业证书编号</p>
            </div>
            <div>
              <el-input
                type="text"
                placeholder="请输入医师执业证书编号"
                v-model="ruleForm.qualificationObject.certificateNumber"
                maxlength="15"
                show-word-limit
                :disabled="forbidden"
              >
              </el-input>
            </div>
          </div>
          <div class="hintss">
            <div class="title5"></div>
            <div class="hints">
              <img
                src="http://cdn.xiaodingdang1.com/info-circle-filled.png"
                alt=""
              />
              <p>需与卫健委查询结果一致，否则无法通过审核</p>
            </div>
          </div>
          <div class="BackgroundPicture">
            <div class="title5">
              <p class="required">*</p>
              <p>卫健委查询结果</p>
            </div>
            <div class="upload">
              <image-upload
                :limit="1"
                :isShowTip="false"
                v-model="ruleForm.qualificationObject.wjwQueryResult"
                v-if="!forbidden"
              />
              <div v-if="forbidden">
                <img
                  :src="item"
                  alt=""
                  v-for="(item, index) in ruleForm.qualificationObject
                    .wjwQueryResult"
                  :key="index"
                  style="
                    width: 146px;
                    height: 146px;
                    margin-right: 10px;
                    border: 1px solid #c0ccda;
                    border-radius: 6px;
                  "
                />
              </div>
            </div>
          </div>
        </div>
        <!--孕产月嫂-->
        <div v-if="staffPostId == 4 || staffPostId == 5 || staffPostId == 9">
          <div class="content1">
            <div class="title5">
              <p class="required">*</p>
              <p>职业名称</p>
            </div>
            <div class="roomMessage">
              <div class="sel">
                <el-select
                  v-model="ruleForm.qualificationObject.occupation"
                  placeholder="请选择科室"
                  clearable
                  style="width: 139px; margin-left: 10px"
                  :disabled="forbidden"
                >
                  <el-option
                    v-for="item in occupationList"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  />
                </el-select>
              </div>
            </div>
          </div>

          <div class="content1">
            <div class="title5">
              <p class="required">*</p>
              <p>职业技能证书编号</p>
            </div>
            <div>
              <el-input
                type="text"
                placeholder="请输入职业技能证书编号"
                v-model="ruleForm.qualificationObject.certificateNumber"
                maxlength="40"
                show-word-limit
                :disabled="forbidden"
                style="width: 400px"
              >
              </el-input>
            </div>
          </div>
          <div class="BackgroundPicture">
            <div class="title5">
              <p class="required">*</p>
              <p>职业技能等级证书</p>
            </div>
            <div class="upload">
              <image-upload
                :limit="1"
                :isShowTip="false"
                v-model="ruleForm.qualificationObject.skillLevelPhotos"
                v-if="!forbidden"
              />
              <div v-if="forbidden">
                <img
                  :src="item"
                  alt=""
                  v-for="(item, index) in ruleForm.qualificationObject
                    .skillLevelPhotos"
                  :key="index"
                  style="
                    width: 146px;
                    height: 146px;
                    margin-right: 10px;
                    border: 1px solid #c0ccda;
                    border-radius: 6px;
                  "
                />
              </div>
            </div>
          </div>
          <div class="content1">
            <div class="title5">
              <p class="required">*</p>
              <p>健康证颁发机构</p>
            </div>
            <div>
              <el-input
                type="text"
                placeholder="请输入健康证颁发机构"
                v-model="
                  ruleForm.qualificationObject.healthCertificateIssuingAuthority
                "
                maxlength="40"
                show-word-limit
                :disabled="forbidden"
                style="width: 400px"
              >
              </el-input>
            </div>
          </div>
          <div class="hintss">
            <div class="title5"></div>
            <div class="hints">
              <img
                src="http://cdn.xiaodingdang1.com/info-circle-filled.png"
                alt=""
              />
              <p>
                健康证由当地疾病预防控制中心或经卫生行政部门审批承担预防性健康检查的医疗卫生机构颁发
              </p>
            </div>
          </div>
          <div class="BackgroundPicture">
            <div class="title5">
              <p class="required">*</p>
              <p>健康证正面照</p>
            </div>
            <div class="upload">
              <image-upload
                :limit="1"
                :isShowTip="false"
                v-model="
                  ruleForm.qualificationObject.healthCertificateFrontPhoto
                "
                v-if="!forbidden"
              />
              <div v-if="forbidden">
                <img
                  :src="item"
                  alt=""
                  v-for="(item, index) in ruleForm.qualificationObject
                    .healthCertificateFrontPhoto"
                  :key="index"
                  style="
                    width: 146px;
                    height: 146px;
                    margin-right: 10px;
                    border: 1px solid #c0ccda;
                    border-radius: 6px;
                  "
                />
              </div>
            </div>
          </div>
          <div class="content1">
            <div class="title5">
              <p class="required">*</p>
              <p>健康证有效日期</p>
            </div>
            <div>
              <el-date-picker
                v-model="
                  ruleForm.qualificationObject.healthCertificateValidStartDate
                "
                type="datetime"
                placeholder="有效期的开始时间"
                :disabled="forbidden"
              >
              </el-date-picker>
              <el-date-picker
                v-model="
                  ruleForm.qualificationObject.healthCertificateValidEndDate
                "
                type="datetime"
                placeholder="有效期的结束时间"
                :disabled="forbidden"
              >
              </el-date-picker>
            </div>
          </div>
        </div>
        <!-- 孕产育婴员
                  <div v-if="staffPostId=5">
                   <div class="content1">
                  <div class="title5"><p class="required">*</p><p>职业技能证书编号</p></div>
                  <div>
                    <el-input
  type="text"
  placeholder="请输入职业技能证书编号"
  v-model="ruleForm.qualificationObject.certificateNumber"
  maxlength="40"
  show-word-limit
>
</el-input>
                  </div>
                  </div>
                  <div class="BackgroundPicture">
          <div class="title5"><p class="required">*</p><p>职业技能等级证书</p></div>
         <div class="upload">
          <image-upload :limit="1" :isShowTip="false" v-model="ruleForm.qualificationObject.skillLevelPhotos" v-if="!forbidden"/>
         </div>
      </div>
      <div class="content1">
                  <div class="title5"><p class="required">*</p><p>健康证颁发机构</p></div>
                  <div>
                    <el-input
  type="text"
  placeholder="请输入健康证颁发机构"
  v-model="ruleForm.qualificationObject.healthCertificateIssuingAuthority"
  maxlength="40"
  show-word-limit
>
</el-input>
                  </div>
                  </div>
                  <div class="hint">
          <div class="title5"></div>
          <div class="hints">
              <img src="http://cdn.xiaodingdang1.com/info-circle-filled.png" alt="">
              <p>
                健康证由当地疾病预防控制中心或经卫生行政部门审批承担预防性健康检查的医疗卫生机构颁发
              </p>
          </div>
      </div>
      <div class="BackgroundPicture">
          <div class="title5"><p class="required">*</p><p>健康证正面照</p></div>
         <div class="upload">
          <image-upload :limit="1" :isShowTip="false" v-model="ruleForm.qualificationObject.healthCertificateIssuingAuthority" v-if="!forbidden"/>
         </div>
      </div>
      <div class="content1">
                  <div class="title5"><p class="required">*</p><p>健康证有效日期</p></div>
                  <div>
                    <el-time-select
    placeholder="有效期的开始时间"
    v-model="ruleForm.qualificationObject.healthCertificateValidStartDate"
    :picker-options="{
      start: '08:30',
      step: '00:15',
      end: '18:30'
    }">
  </el-time-select>
  <el-time-select
    placeholder="有效期的结束时间"
    v-model="ruleForm.qualificationObject.healthCertificateValidStartDate"
    :picker-options="{
      start: '08:30',
      step: '00:15',
      end: '18:30',
      minTime: startTime
    }">
  </el-time-select>
                  </div>
                  </div>
                  </div> -->
      </div>
      <div class="btn" v-if="!forbidden">
        <el-button type="primary" @click="confirm">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
      <div class="btn" v-if="forbidden">
        <el-button @click="cancel">返回</el-button>
      </div>
      <div class="btn" v-if="auditInfoList.auditStatus == 0">
        <el-button type="primary" @click="pass">通过</el-button>
        <el-button @click="reject">驳回</el-button>
      </div>
      <div class="btn">
        <el-button type="success" v-if="auditInfoList.auditStatus == 1"
          >已通过</el-button
        >
        <el-button type="warning" v-if="auditInfoList.auditStatus == 2"
          >已驳回</el-button
        >
      </div>
    </div>
    <!--驳回-->
    <el-dialog title="驳回原因" :visible.sync="dialogVisibleReject" width="30%">
      <el-input
        type="textarea"
        v-model="rejectionReason"
        placeholder="请输入驳回原因"
      ></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleReject = false">取 消</el-button>
        <el-button type="primary" @click="rejectConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { save, update, info } from "@/api/platform/nursingStaff";
import ImageUpload from "@/components/ImageUpload/index";
import FileUpload from "@/components/FileUpload/index";
export default {
  name: "app",
  components: { ImageUpload, FileUpload },
  data() {
    return {
      inputVisible: false,
      inputValue: "",
      labelList: [],
      staffId: "",
      occupationList: [
        {
          dictValue: "育婴员",
          dictLabel: "育婴员",
        },
        {
          dictValue: "母婴护理师",
          dictLabel: "母婴护理师",
        },
      ],
      deptList: [
        {
          dictValue: 1,
          dictLabel: "针灸科",
        },
        {
          dictValue: 2,
          dictLabel: "中医妇科",
        },
        {
          dictValue: 3,
          dictLabel: "中医肠胃科",
        },
        {
          dictValue: 4,
          dictLabel: "中医老年病科",
        },
        {
          dictValue: 5,
          dictLabel: "中医男科",
        },
        {
          dictValue: 6,
          dictLabel: "中医外科",
        },
        {
          dictValue: 7,
          dictLabel: "中医肿瘤科",
        },
        {
          dictValue: 8,
          dictLabel: "推拿科",
        },
        {
          dictValue: 9,
          dictLabel: "中医呼吸科",
        },
        {
          dictValue: 10,
          dictLabel: "中医儿科",
        },
        {
          dictValue: 11,
          dictLabel: "中医内科",
        },
        {
          dictValue: 12,
          dictLabel: "中医皮肤性病科",
        },
        {
          dictValue: 13,
          dictLabel: "中医其他科",
        },
        {
          dictValue: 14,
          dictLabel: "超声科",
        },
        {
          dictValue: 15,
          dictLabel: "核医学科",
        },
        {
          dictValue: 16,
          dictLabel: "放射科",
        },
        {
          dictValue: 17,
          dictLabel: "其他影像科",
        },
        {
          dictValue: 18,
          dictLabel: "介入科",
        },
        {
          dictValue: 19,
          dictLabel: "整形美容外科",
        },
        {
          dictValue: 20,
          dictLabel: "皮肤美容科",
        },
        {
          dictValue: 21,
          dictLabel: "普外科",
        },
        {
          dictValue: 22,
          dictLabel: "肝胆外科",
        },
        {
          dictValue: 23,
          dictLabel: "神经外科",
        },
        {
          dictValue: 24,
          dictLabel: "泌尿外科",
        },
        {
          dictValue: 25,
          dictLabel: "心血管外科",
        },
        {
          dictValue: 26,
          dictLabel: "胸外科",
        },
        {
          dictValue: 27,
          dictLabel: "肛肠外科",
        },
        {
          dictValue: 28,
          dictLabel: "其他外科",
        },
        {
          dictValue: 29,
          dictLabel: "烧伤科",
        },
        {
          dictValue: 30,
          dictLabel: "骨科",
        },
        {
          dictValue: 31,
          dictLabel: "皮肤科",
        },
        {
          dictValue: 32,
          dictLabel: "性病科",
        },
        {
          dictValue: 33,
          dictLabel: "内科其他",
        },
        {
          dictValue: 34,
          dictLabel: "呼吸内科",
        },
        {
          dictValue: 35,
          dictLabel: "老年病科",
        },
        {
          dictValue: 36,
          dictLabel: "免疫科",
        },
        {
          dictValue: 37,
          dictLabel: "内分泌科",
        },
        {
          dictValue: 38,
          dictLabel: "神经内科",
        },
        {
          dictValue: 39,
          dictLabel: "肾内科",
        },
        {
          dictValue: 40,
          dictLabel: "消化内科",
        },
      ],
      documentTypeList: [
        {
          dictValue: 1,
          dictLabel: "身份证",
        },
        {
          dictValue: 2,
          dictLabel: "护照",
        },
        {
          dictValue: 3,
          dictLabel: "港澳居民来往内地通行证",
        },
        {
          dictValue: 4,
          dictLabel: "台湾居民来往大陆通行证",
        },
      ],
      postList: [
        {
          dictValue: 1,
          dictLabel: "厨师长",
        },
        {
          dictValue: 2,
          dictLabel: "主厨",
        },
        {
          dictValue: 3,
          dictLabel: "厨师",
        },
        {
          dictValue: 4,
          dictLabel: "高级营养师",
        },
        {
          dictValue: 5,
          dictLabel: "营养师",
        },
      ],
      staffPostId: "", //1'孕产厨师'2'孕产医师'3'孕产护士'4'孕产月嫂'5'孕产育婴员'6'孕产心理咨询师'7孕产产后康复师'8孕产健康管理师'9'孕产母婴护理师'
      audit: "",
      auditId: "",
      forbidden: false,
      auditInfoList: [],
      dialogVisibleReject: false,
      rejectionReason: "",
      startTime: "",
      endTime: "",
      text: 4,
      ruleForm: {
        sortKey: "",
        staffTel: "", //手机号码
        staffPost: "", //人员类型
        staffName: "", //真实姓名
        staffPhotos: [], //照片
        // videos:[],
        practiceTime: "", //从业开始年份
        staffDesc: "", //个人简介
        documentType: "", //证件类型
        documentNumber: "", //证件号码
        photoFront: [], //正面照
        photoBack: [], //反面照
        qualificationObject: {
          post: "", //厨师职位
          dept: "", //科室
          licenseNumber: "", //医师执业证书编号
          wjwQueryResult: [], //卫健委查询结果
          certificateNumber: "", //护士执业证书编号
          occupation: "", //职业
          skillLevelPhotos: [], //技能等级照片
          healthCertificateIssuingAuthority: "", // 健康证颁发机构
          healthCertificateFrontPhoto: [], //健康证正面照片
          healthCertificateValidStartDate: "", //健康证有效日期开始
          healthCertificateValidEndDate: "", //健康证有效日期结束
        },
        tag: [],
        serviceNum: "",
      },
    };
  },
  watch: {
    //  'ruleForm.staffTel'(){
    //   if(this.ruleForm.staffTel.length==11){
    //     if (/^(13[0-9]|14[0-9]|15[0-9]|16[6]|18[0-9]|19[6,9]|17[0-9])\d{8}$/i.test(this.ruleForm.staffTel) == false) {
    //     this.$message('请填写正确的手机号码');
    //           } else {
    //           }
    //   }
    //  }
  },
  created() {
    this.ruleForm.staffPost = this.$route.query.staffPost;
    this.staffPostId = this.$route.query.staffPostId;
    this.staffId = this.$route.query.staffId;
    let audit = this.$route.query.audit;
    this.audit = audit;
    this.forbidden = this.$route.query.forbidden ? true : false;
    this.auditId = this.$route.query.auditId;
    if (audit) {
      this.auditInfo(this.auditId);
    }
    if (this.staffId) {
      this.info(this.staffId);
    }
  },
  methods: {
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm() {
      if (this.labelList.length > 1) {
        this.$message("标签最多添加2个");
        this.inputVisible = false;
        this.inputValue = "";
        return;
      }
      let inputValue = this.inputValue;
      if (inputValue) {
        this.labelList.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
    handleClose(tag) {
      this.labelList.splice(this.labelList.indexOf(tag), 1);
    },
    handleCloses(tag) {
      this.ruleForm.tag.splice(this.ruleForm.tag.indexOf(tag), 1);
    },
    choice(e) {
      if (this.ruleForm.tag.length > 1) {
        this.$message("标签最多添加2个");
        return;
      }
      this.ruleForm.tag.forEach((item) => {
        if (item == e) {
          this.$message("标签已添加");
          black;
        }
      });
      this.ruleForm.tag.push(e);
    },
    inpt() {
      const phoneReg = /^1[3|4|5|6|7|8|9][0-9]{9}$/;
      setTimeout(() => {
        if (!Number.isInteger(+this.ruleForm.staffTel)) {
          this.$message("请输入数字值");
          this.ruleForm.staffTel = "";
        } else {
          if (phoneReg.test(this.ruleForm.staffTel)) {
          } else {
            this.$message("电话号码格式不正确");
            this.ruleForm.staffTel = "";
          }
        }
      }, 100);
    },
    cancel() {
      this.$router.go(-1);
    },
    rejectConfirm() {
      //驳回确定
      if (this.rejectionReason == "") {
        this.$message("请填写驳回理由");
        return;
      }
      this.dialogVisibleReject = false;
      let data = {
        auditId: this.auditId,
        auditResult: false,
        rejectionReason: this.rejectionReason,
      };
      audit(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.$router.go(-1);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    reject() {
      //驳回
      this.dialogVisibleReject = true;
    },
    pass() {
      //通过
      this.$confirm("是否审核通过?", "审核通过", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // this.$message({
          //   type: 'success',
          //   message: '审核通过!'
          // });
          let data = {
            auditId: this.auditId,
            auditResult: true,
          };
          audit(data).then((res) => {
            if (res.code == 200) {
              this.$message(res.msg);
              this.$router.go(-1);
              return;
            } else {
              this.$message(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消审核",
          });
        });
    },

    auditInfo(auditId) {
      //查询审核信息
      auditInfo(auditId).then((res) => {
        if (res.code == 200) {
          this.auditInfoList = res.data;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },

    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    confirm() {
      //确定
      let data = this.ruleForm;
      let qualificationObject = this.ruleForm.qualificationObject;
      if (data.staffPost == "") {
        this.$message("请选择人员类型");
        return;
      }
      if (data.staffName == "") {
        this.$message("请输入真实姓名");
        return;
      }

      if (data.staffTel == "") {
        this.$message("请输入手机号码");
        return;
      }
      if (data.staffPhotos.length == 0) {
        this.$message("请输上传照片");
        return;
      }
      if (data.practiceTime == "") {
        this.$message("请输入从业开始年份");
        return;
      }
      if (data.staffDesc == "") {
        this.$message("请输入个人简介");
        return;
      }
      if (data.serviceNum == "") {
        this.$message("请输入服务人员数量");
        return;
      }
      if (data.tag.length == 0) {
        this.$message("请添加人员标签");
        return;
      }
      //if (this.staffPostId == 1) {
      //  if (qualificationObject.post == "") {
      //    this.$message("请选择厨师职位");
      //    return;
      //  }
      //}
      // if (data.documentType == "") {
      //   this.$message("请选择证件类型");
      //   return;
      // }

      // if (data.documentNumber == "") {
      //   this.$message("请输入证件号码");
      //   return;
      // }
      // if (data.photoFront.length == 0) {
      //   this.$message("请上传身份证正面照");
      //   return;
      // }
      // if (data.photoBack.length == 0) {
      //   this.$message("请上传身份证反面照");
      //   return;
      // }
      // if (this.staffPostId == 2) {
      //   //孕产医师
      //   if (qualificationObject.dept == "") {
      //     this.$message("请选择科室");
      //     return;
      //   }
      //   if (qualificationObject.licenseNumber == "") {
      //     this.$message("请输入医师执业证书编号");
      //     return;
      //   }
      //   if (qualificationObject.wjwQueryResult.length == 0) {
      //     this.$message("请上传卫健委查询结果");
      //     return;
      //   }
      // }
      // if (this.staffPostId == 3) {
      //   //孕产护士
      //   if (qualificationObject.certificateNumber == "") {
      //     this.$message("请输入证书编号");
      //     return;
      //   }
      //   if (qualificationObject.wjwQueryResult.length == 0) {
      //     this.$message("请上传卫健委查询结果");
      //     return;
      //   }
      // }
      // if (this.staffPostId == 4) {
      //   //孕产月嫂
      //   if (qualificationObject.occupation == "") {
      //     this.$message("请选择职业名称");
      //     return;
      //   }
      // }
      // if (
      //   this.staffPostId == 4 ||
      //   this.staffPostId == 5 ||
      //   this.staffPostId == 9
      // ) {
      //   //孕产月嫂 孕产育婴员 孕产母婴护理师
      //   if (qualificationObject.certificateNumber == "") {
      //     this.$message("请输入证书编号");
      //     return;
      //   }
      //   if (qualificationObject.skillLevelPhotos.length == 0) {
      //     this.$message("请上传技能等级照片");
      //     return;
      //   }
      //   if (qualificationObject.healthCertificateIssuingAuthority == "") {
      //     this.$message("请输入健康证颁发机构");
      //     return;
      //   }
      //   if (qualificationObject.healthCertificateFrontPhoto.length == 0) {
      //     this.$message("请上传健康证正面照片");
      //     return;
      //   }
      //   if (qualificationObject.healthCertificateValidStartDate == "") {
      //     this.$message("请选择健康证有效日期开始");
      //     return;
      //   }
      //   if (qualificationObject.healthCertificateValidEndDate == "") {
      //     this.$message("请选择健康证有效日期结束");
      //     return;
      //   }
      // }
      if (!this.staffId) {
        save(this.ruleForm).then((res) => {
          if (res.code == 200) {
            this.$message(res.msg);
            this.$router.go(-1);
            return;
          } else {
            this.$message(res.msg);
          }
        });
      }
      if (this.staffId) {
        update(this.ruleForm).then((res) => {
          console.log(res);
          if (res.code == 200) {
            this.$router.go(-1);
            return;
          } else {
            this.$message(res.msg);
          }
        });
      }
    },
    info(staffId) {
      //查询产后康复信息
      info(staffId).then((res) => {
        if (res.code == 200) {
          console.log(res.data);
          this.staffPostId =
            res.data.staffPost == "孕产厨师"
              ? 1
              : res.data.staffPost == "孕产医师"
              ? 2
              : res.data.staffPost == "孕产护士"
              ? 3
              : res.data.staffPost == "孕产月嫂"
              ? 4
              : res.data.staffPost == "孕产育婴员"
              ? 5
              : res.data.staffPost == "孕产心理咨询师"
              ? 6
              : res.data.staffPost == "孕产产后康复师"
              ? 7
              : res.data.staffPost == "孕产健康管理师"
              ? 8
              : res.data.staffPost == "孕产母婴护理师"
              ? 9
              : "";
          this.ruleForm = res.data;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;

  .titles {
    font-size: 22px;
    font-weight: bold;
  }

  .title1 {
    font-size: 14px;
  }
}

.titleName {
  padding: 24px 0 10px 24px;
  font-size: 20px;
  color: #17191a;
}

.rule {
  width: 98%;
  height: 2px;
  background: #ebebeb;
  margin: 0 auto;
}

.app {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
}

.BackgroundPicture {
  display: flex;
  margin-top: 20px;

  .uploadTitle {
    display: flex;
    color: #17191a;
    font-size: 14px;
    width: 100px;
    justify-content: flex-end;
    padding: 0 12px 0 0;
  }
}

.hintss {
  display: flex;
  color: #45464a;
  font-size: 14px;
  margin-top: 8px;
  line-height: 0;

  .hints {
    display: flex;
    align-items: center;

    .hints1 {
      color: #ff6c11;
    }
  }
}

.title5 {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #606266;
  box-sizing: border-box;
  font-weight: bold;
  width: 10%;

  .required {
    color: red;
  }
}

.content {
  margin-left: 100px;
}

.content1 {
  display: flex;
  align-items: center;
  margin: 20px 0 10px 0;

  .textarea {
    width: 368px;
  }
}

.btn {
  margin: 20px auto;
  text-align: center;
}

.sel {
  display: flex;
  align-items: center;
  margin-right: 10px;

  .selTitle {
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
  }

  #cars {
    border: 1px solid #dcdcdc;
    border-radius: 3px;
    color: #7c7d81;
    font-size: 14px;
    width: 129px;
    height: 32px;
    margin-left: 6px;
  }
}

.roomMessage {
  display: flex;
  margin-left: -10px;
}

.el-upload--picture-card {
  width: 100px;
  height: 100px;
}

.el-upload {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

.el-upload-list--picture-card .el-upload-list__item-thumbnail {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

.avatar {
  width: 100px;
  height: 100px;
}

.auditContent {
  width: 100%;
  padding: 20px 24px;
  background: #ff6c11;
  border-radius: 6px;
  font-size: 14px;
  line-height: 14px;
}

.pass {
  display: flex;
  align-items: center;
  color: #000000;
}

.passCause {
  color: #000000;
  margin-left: 20px;
}

.label {
  width: 368px;
  height: 86px;
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  display: flex;
  flex-wrap: wrap;
  padding: 10px 10px;

  .labelName {
    color: #7c7d81;
    font-size: 14px;
    height: 24px;
    border-radius: 3px;
    line-height: 24px;
    background: #ebebeb;
    margin-right: 16px;
    padding: 0 8px;
  }

  .inputLabel {
    width: 156px;
    font-size: 12px;
  }
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.tagShow {
  margin-right: 5px;
}
</style>
