<template>
    <div class="app" v-loading="loading">
        <!-- <div class="head">
            <div class="title">用品管理</div>
            <div class="title1">Pages/用品管理</div>
        </div> -->
        <div class="content">
            <el-tabs v-model="ruleForm.category" @tab-click="handleClick">
    <el-tab-pane label="妈妈用品" name="MAMA"></el-tab-pane>
    <el-tab-pane label="宝宝用品" name="BABY"></el-tab-pane>
  </el-tabs>
  <div class="operation" v-for="item,index in addList" :key="index">
    <el-input v-model="item.brandName" placeholder="请输入用品品牌（选填）" style="width:176px ;"></el-input>
    <el-input v-model="item.supplyName" placeholder="请输入用品名称（必填）" style="width:176px ;margin: 0  10px ;"></el-input>
    <!-- <el-button
            size="mini"
            type="text"
            @click="deleteList(index)"
            class="edit"
          >删除</el-button> -->
          <el-button
            size="mini"
            type="text"
            @click="add(index)"
          >提交</el-button>
  </div>
             <!-- <el-button type="primary" @click="addUse">添加用品</el-button> -->
             <el-table :data="listData"  class="table">
      <el-table-column label="序号" prop="category" align="center">
        <template scope="scope">
    <span v-text="scope.$index+1"></span>
</template>
        </el-table-column>
      <el-table-column label="用品ID" prop="supplyId" :show-overflow-tooltip="true"  align="center" />
      <el-table-column label="用品名称" prop="supplyName" :show-overflow-tooltip="true"  align="center" >
    </el-table-column>
      <el-table-column label="用品品牌" prop="brandName" align="center" />
      <el-table-column label="审核状态" prop="auditStatus" align="center" >
        <template scope="scope">
          <p v-if="scope.row.auditStatus==0" style="color: #FF6C11;">待审核</p>
          <p v-if="scope.row.auditStatus==1" style="color: #0BBD71;">已通过</p>
          <p v-if="scope.row.auditStatus==2" style="color: #F84343;">已驳回</p>
      </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope" v-if="scope.row.roleId !== 1">
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="block">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="100"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>
  </div>
        </div>
        <el-dialog
  title="删除"
  :visible.sync="deleteDialogVisible"
  width="30%">
  <span>是否删除此用品管理？</span>
  <span slot="footer" class="dialog-footer">
    <el-button @click="deleteDialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
  </span>
</el-dialog>
    </div>
</template>

<script>
import { page, save1,save2,delTable } from "@/api/platform/suppliesManagement";
export default {
  
    name: "app",
  data() {
    return {
      loading:false,
      listData:[],
      supplyId:'',
      total:0,
      ruleForm:{
        pageSize:10,
        pageNum:1,
        brandName:'',//用品品牌
        supplyName:'',//用品名称
        category:'MAMA'
      },
        input:'',
        addList:[
          {
            supplyName:'',
            brandName:''
          }
        ],
      deleteDialogVisible:false,
        roleList:[
            {
                name:'超值套餐',
                price:'298000',
                status:1,
                time:'2024年12月12日21:14:25',
                auditStatus:1,
                show:1,
            },
            {
                name:'超值套餐',
                price:'298000',
                status:0,
                time:'2024年12月12日21:14:25',
                auditStatus:0,
                show:0,
            }
        ]
    }
},
created(){
  this.getList()
},
methods:{
  handleClick(){
    let data=[
          {
            supplyName:'',
            brandName:''
          }
        ]
        this.addList=data
    this.getList()
  },
  addUse(){//添加用品
    this.addList.push( {
            supplyName:'',
            brandName:''
          })

  },
  deleteList(e){//删除
    let index=e
   this.addList.splice(index,1)
  },
  add(index){//新增
    let data=this.addList
   
            if (data[index].brandName == '') {
                this.$message('请输入用品品牌');
                return
            }
            if (data[index].supplyName == '') {
                this.$message('请输入用品名称');
                return
            }
            if(this.ruleForm.category=='MAMA'){
              save1(data[index]).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    data[index].supplyName='',
                    data[index].brandName='',
                    this.getList()
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
            }
            if(this.ruleForm.category=='BABY'){
              save2(data[index]).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    data[index].supplyName='',
                    data[index].brandName='',
                    this.getList()
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
            }
         
  },
    handleSizeChange(val) {
      this.ruleForm.pageSize=val
      this.getList()
      },
      handleCurrentChange(val) {
        this.ruleForm.pageNum=val
        this.getList()
      },
      handleDelete(res){
        this.supplyId=res.supplyId
        this.deleteDialogVisible=true
      },
      confirmDelete(){//确定删除
        delTable(this.supplyId).then(res=>{
          if(res.code==200){
                    this.$message(res.msg);
                    this.deleteDialogVisible=false
                   this.getList()
                return 
                }else{
                    this.$message(res.msg); 
                }
        })
      },
      getList(){//列表
        this.loading=true
        page(this.ruleForm).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.listData=res.rows
                    this.total=res.total
                    this.loading=false
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
      },
}
}
</script>

<style scoped lang="scss">
.head{
    width: 100%;
    height: 80px;
    background: #ffff;
    color: #17191A;
    padding: 15px 20px;
    .title{
        font-size: 22px;
        font-weight: bold;
    }
    .title1{
        font-size: 14px;
    }
}
.content{
    background: #ffff;
    margin: 10px 10px;
    border-radius: 5px;
    padding: 24px 20px;
    .title2{
        font-size: 20px;
        color: #17191A;
        margin-bottom: 24px;
    }
    .table{
        margin-top: 20px;
    }
}
.block{
    text-align: right;
    margin-top: 20px;
}
.operation{
    display: flex;
    margin-bottom: 10px;
}
.edit{
    color: #F84343;
}
</style>