<template>
    <div class="app" v-loading="loading">
        <!-- <div class="head">
            <div class="title">护理人员配置</div>
            <div class="title1">Pages/护理人员配置</div>
        </div> -->
        <div class="content">
             <div class="title2">孕期助手</div>
             <el-button type="primary" @click="addMaternitySuite">新增</el-button>
             <el-table :data="listData" class="table">
      <el-table-column label="周期" prop="gestationWeek" width="120" align="center" />
      <el-table-column label="特别关注" prop="specialAttention" :show-overflow-tooltip="true" width="150" align="center" />
      <el-table-column label="宝宝照片"  width="120" align="center" >
        <template slot-scope="scope">
                    <img :src="scope.row.babyImage"  min-width="70" height="70" style="width: 100px;height: 100px;border-radius: 5px;"/>
          </template>
      </el-table-column>
      <el-table-column label="宝宝情况详情" prop="babyDevelopmentDetail" :show-overflow-tooltip="true" width="150" align="center" >
    </el-table-column>
    <el-table-column label="宝宝情况图片1"  width="120" align="center" >
        <template slot-scope="scope">
                    <img :src="scope.row.babyDevelopmentImage1"  min-width="70" height="70" style="width: 100px;height: 100px;border-radius: 5px;"/>
          </template>
      </el-table-column>
      <el-table-column label="宝宝情况图片2"  width="120" align="center" >
        <template slot-scope="scope">
                    <img :src="scope.row.babyDevelopmentImage2"  min-width="70" height="70" style="width: 100px;height: 100px;border-radius: 5px;"/>
          </template>
      </el-table-column>
      <el-table-column label="注意事项" prop="attentionItem1" width="200" align="center" />
      <el-table-column label="食物1" prop="foodName1" width="200" align="center" />
      <el-table-column label="食物2" prop="foodName2" width="100" align="center" >
      </el-table-column>
      <el-table-column label="食物照片1"  width="120" align="center" >
        <template slot-scope="scope">
                    <img :src="scope.row.foodImage1"  min-width="70" height="70" style="width: 100px;height: 100px;border-radius: 5px;"/>
          </template>
      </el-table-column>
      <el-table-column label="食物照片2"  width="120" align="center" >
        <template slot-scope="scope">
                    <img :src="scope.row.foodImage2"  min-width="70" height="70" style="width: 100px;height: 100px;border-radius: 5px;"/>
          </template>
      </el-table-column>
      <el-table-column label="可以做" prop="canDoActivities" width="100" align="center" :show-overflow-tooltip="true">
      </el-table-column>
      <el-table-column label="不可以做" prop="cannotDoActivities" width="100" align="center" :show-overflow-tooltip="true">
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope" >
          <!-- <el-button
            size="mini"
            type="text"
            @click="examine(scope.row)"
          >查看</el-button> -->
          <el-button
            size="mini"
            type="text"
            @click="compile(scope.row)"
          >编辑</el-button>
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="block">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="100"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>
  
  </div>
        </div>
        <el-dialog
  title="删除"
  :visible.sync="deleteDialogVisible"
  width="30%">
  <span>是否删除此孕期助手？</span>
  <span slot="footer" class="dialog-footer">
    <el-button @click="deleteDialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
  </span>
</el-dialog>

    </div>
</template>

<script>
import { page,delTable,online } from "@/api/platform/pregnancyAssistant";
export default {
    name: "app",
  data() {
    return {
      loading:false,
      staffPost:'',
      staffPostId:'',
      rentId:-1,
      addShow:false,
      total:0
,      trackerIds:'',
      ruleForm:{
        pageSize:10,
        pageNum:1
      },
      deleteDialogVisible:false,
      listData:[],
      addRentList:[
        {
          id:1,
          name:'孕产厨师'
        },
        {
          id:2,
          name:'孕产医师'
        }, {
          id:3,
          name:'孕产护士'
        }, {
          id:4,
          name:'孕产月嫂'
        }, {
          id:5,
          name:'孕产育婴员'
        }, {
          id:6,
          name:'孕产心理咨询师'
        },
        {
          id:7,
          name:'孕产产后康复师'
        },
        {
          id:8,
          name:'孕产健康管理师'
        },
        {
          id:9,
          name:'孕产母婴护理师'
        },
      ]
    }
},
created(){
  this.getList()
},
methods:{
    handleSizeChange(val) {
      this.ruleForm.pageSize=val
      this.getList()
      },
      handleCurrentChange(val) {
        this.ruleForm.pageNum=val
        this.getList()
      },
      handleDelete(row){
        console.log(row.trackerIds);
        this.trackerIds=row.trackerId
        this.deleteDialogVisible=true
      },
      confirmDelete(){//确定删除
        delTable(this.trackerIds).then(res=>{
          if(res.code==200){
                    this.$message(res.msg);
                    this.deleteDialogVisible=false
                   this.getList()
                return 
                }else{
                    this.$message(res.msg); 
                }
        })
      },
      addMaternitySuite(){//新增房型
        this.$router.push({ path: "/addPregnancyAssistant", query: {
          staffPost: this.staffPost,
          staffPostId:this.staffPostId
    }, });
      },
      getList(){//列表
        this.loading=true
        page(this.ruleForm).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.listData=res.rows
                    this.total=res.total
                    this.loading=false
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
      },
    //   examine(row){//查看
    //     this.$router.push({ path: "/addnursingStaff", query: {
    //       staffId: row.staffId,
    //       forbidden:true
    // }, });
    //   },
      compile(row){//编辑
        this.$router.push({ path: "/addPregnancyAssistant", query: {
          trackerId: row.trackerId,
    }, });
      },
      addRent(index,item){
        this.rentId=index
        this.staffPost=item.name
        this.staffPostId=item.id
      },
    
}
}
</script>

<style scoped lang="scss">
.head{
    width: 100%;
    height: 80px;
    background: #ffff;
    color: #17191A;
    padding: 15px 20px;
    .title{
        font-size: 22px;
        font-weight: bold;
    }
    .title1{
        font-size: 14px;
    }
}
.content{
    background: #ffff;
    margin: 10px 10px;
    border-radius: 5px;
    padding: 24px 20px;
    .title2{
        font-size: 20px;
        color: #17191A;
        margin-bottom: 24px;
    }
    .table{
        margin-top: 20px;
    }
}
.block{
    text-align: right;
    margin-top: 20px;
}
.staffType{
    display: flex;
    flex-wrap: wrap;
    .type{
        border-radius: 34px;
border: 1px solid #EBEBEB;   
color: #17191A;
font-size: 14px;
padding: 5px 10px;
margin-right: 8px;
}
}

    .active{
        border-radius: 34px;
color: #ffff;
font-size: 14px;
padding: 5px 10px;
margin-right: 8px;
background: #3F85FF;
    }
</style>