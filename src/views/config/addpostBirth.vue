<template>
  <div id="app">
    <!-- <div class="head">
             <div class="title">产后康复配置</div>
             <div class="title1">Pages/产后康复配置/新增</div>
         </div> -->
    <div class="content">
      <div class="auditContent" v-if="auditInfoList.auditStatus == 2">
        <div class="pass">
          <img
            src="http://cdn.xiaodingdang1.com/icon%402x.png"
            alt=""
            style="width: 20px; height: 20px"
          />
          <p>驳回原因</p>
        </div>
        <p class="passCause">{{ auditInfoList.rejectionReason }}</p>
      </div>

      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>项目名称</p>
        </div>
        <el-input
          type="text"
          placeholder="请输入房间名称"
          class="input"
          v-model="ruleForm.projectName"
          :disabled="forbidden"
        ></el-input>
      </div>
      <div class="content1">
        <div class="title5"><p>商品价格</p></div>
        <el-input
          type="number"
          placeholder="请输入商品价格"
          class="input"
          v-model="ruleForm.price"
          :disabled="forbidden"
        ></el-input>
      </div>
      <div class="content1" v-if="!forbidden">
        <div class="title5">
          <p class="required">*</p>
          <p>产康标签</p>
        </div>
        <el-tag
          :key="tag"
          v-for="tag in ruleForm.tag"
          closable
          :disable-transitions="false"
          @close="handleClosess(tag)"
          class="elTag"
        >
          {{ tag }}
        </el-tag>
      </div>
      <div class="content1" v-if="forbidden">
        <div class="title5">
          <p class="required">*</p>
          <p>产康标签</p>
        </div>
        <el-tag
          :key="tag"
          v-for="tag in ruleForm.tag"
          closable
          :disable-transitions="false"
          @close="handleClosess(tag)"
          class="elTag"
        >
          {{ tag }}
        </el-tag>
      </div>
      <div class="content1">
        <div class="title5"></div>
        <div class="label">
          <el-tag
            :key="tag"
            v-for="tag in labelList"
            closable
            :disable-transitions="false"
            @close="handleClose(tag)"
            @click="choice(tag)"
            class="elTag"
          >
            {{ tag }}
          </el-tag>
          <el-input
            class="input-new-tag"
            v-if="inputVisible"
            v-model="inputValue"
            ref="saveTagInput"
            size="small"
            @keyup.enter.native="handleInputConfirm"
            @blur="handleInputConfirm"
          >
          </el-input>
          <el-button
            v-else
            class="button-new-tag"
            size="small"
            @click="showInput"
            style="height: 26px; line-height: 26px"
            :disabled="forbidden"
            >+ 添加标签</el-button
          >
        </div>
      </div>

      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>产康描述</p>
        </div>
        <el-input
          type="textarea"
          v-model="ruleForm.description"
          style="width: 368px"
          placeholder="请输入产康描述"
          :disabled="forbidden"
        ></el-input>
      </div>
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>项目照片</p>
        </div>
        <div class="upload">
          <div class="viewLocations">
            <image-upload
              :oneAll="2"
              :limit="8"
              :isShowTip="false"
              v-model="ruleForm.displayPhotos"
              v-if="!forbidden"
            />
            <div
              class="viewLocation"
              @click="
                onPreview(
                  'http://cdn.xiaodingdang1.com/2024/05/11/ace8a362c35641c1baa4de441efe3c67png'
                )
              "
            >
              <img
                src="http://cdn.xiaodingdang1.com/2024/05/11/ace8a362c35641c1baa4de441efe3c67png"
                alt=""
                style="width: 180px; height: 110px"
              />
              <div class="viewLocation1">查看位置</div>
            </div>
          </div>
          <div v-if="forbidden">
            <img
              :src="item"
              alt=""
              v-for="(item, index) in ruleForm.displayPhotos"
              :key="index"
              style="
                width: 146px;
                height: 146px;
                margin-right: 10px;
                border: 1px solid #c0ccda;
                border-radius: 6px;
              "
            />
          </div>
        </div>
      </div>
      <!-- <div class="content1">
                <div class="title5"><p class="required">*</p><p>视频</p></div>
                <div class="upload">
                    <file-upload :limit="8" :isShowTip="false" v-model="ruleForm.videos" v-if="!forbidden"/>
                    <div v-if="forbidden">
                        <video  controls="controls" :src="item" alt="" v-for="item,index in ruleForm.videos" :key="index" style=" width: 146px;height: 146px;margin-right: 10px;border: 1px solid #c0ccda;
    border-radius: 6px;"></video>
                    </div>
                </div>
            </div> -->
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>背景图片</p>
        </div>
        <div class="upload">
          <image-upload
            :oneAll="3"
            :limit="8"
            :isShowTip="false"
            v-model="ruleForm.backgroundPhotos"
            v-if="!forbidden"
          />
          <div v-if="forbidden">
            <img
              :src="item"
              alt=""
              v-for="(item, index) in ruleForm.backgroundPhotos"
              :key="index"
              style="
                width: 146px;
                height: 146px;
                margin-right: 10px;
                border: 1px solid #c0ccda;
                border-radius: 6px;
              "
            />
          </div>
        </div>
      </div>
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>图文详情</p>
        </div>
        <div class="upload">
          <image-upload
            :oneAll="3"
            :limit="8"
            :isShowTip="false"
            v-model="ruleForm.descriptionPhotos"
            v-if="!forbidden"
          />
          <div v-if="forbidden">
            <img
              :src="item"
              alt=""
              v-for="(item, index) in ruleForm.descriptionPhotos"
              :key="index"
              style="
                width: 146px;
                height: 146px;
                margin-right: 10px;
                border: 1px solid #c0ccda;
                border-radius: 6px;
              "
            />
          </div>
        </div>
      </div>
      <div class="content1">
        <div class="title5">
          <p>排序</p>
        </div>
        <el-input-number
          v-model="ruleForm.sortKey"
          controls-position="right"
          :min="0"
          :max="1000"
        ></el-input-number>
      </div>
      <div class="content1">
        <div class="title5">
          <p class="required">*</p>
          <p>模块开关</p>
        </div>
        <div>
          <el-checkbox v-model="ruleForm.isShowProjectDetails"
            >显示项目明细模块</el-checkbox
          >
          <el-checkbox v-model="ruleForm.isShowDevice"
            >显示仪器模块</el-checkbox
          >
        </div>
      </div>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="项目概览" name="first"></el-tab-pane>
        <el-tab-pane label="项目明细" name="second"></el-tab-pane>
        <el-tab-pane label="仪器信息" name="third"></el-tab-pane>
      </el-tabs>
      <div v-if="activeName == 'first'">
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>项目介绍</p>
          </div>
          <el-input
            type="textarea"
            v-model="ruleForm.projectIntroduction"
            style="width: 368px"
            placeholder="请输入项目介绍"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>项目别名</p>
          </div>
          <el-input
            type="text"
            placeholder="请输入项目别名"
            class="input"
            v-model="ruleForm.projectAlias"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1" v-if="!forbidden">
          <div class="title5">
            <p class="required"></p>
            <p>项目功效</p>
          </div>
          <el-tag
            :key="tag"
            v-for="tag in ruleForm.projectEfficacy"
            closable
            :disable-transitions="false"
            @close="handleClosess1(tag)"
            class="elTag"
          >
            {{ tag }}
          </el-tag>
        </div>
        <div class="content1" v-if="forbidden">
          <div class="title5">
            <p class="required"></p>
            <p>项目功效</p>
          </div>
          <el-tag
            :key="tag"
            v-for="tag in ruleForm.projectEfficacy"
            closable
            :disable-transitions="false"
            @close="handleClosess1(tag)"
            class="elTag"
          >
            {{ tag }}
          </el-tag>
        </div>
        <div class="content1">
          <div class="title5"></div>
          <div class="label">
            <el-tag
              :key="tag"
              v-for="tag in projectEfficacy"
              closable
              :disable-transitions="false"
              @close="handleClose1(tag)"
              @click="choice1(tag)"
              class="elTag"
            >
              {{ tag }}
            </el-tag>
            <el-input
              class="input-new-tag"
              v-if="inputVisible1"
              v-model="inputValue"
              ref="saveTagInput"
              size="small"
              @keyup.enter.native="handleInputConfirm1"
              @blur="handleInputConfirm1"
            >
            </el-input>
            <el-button
              v-else
              class="button-new-tag"
              size="small"
              @click="showInput1"
              style="height: 26px; line-height: 26px"
              :disabled="forbidden"
              >+ 请输入您要添加的标签</el-button
            >
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>适用人群</p>
          </div>
          <el-input
            type="textarea"
            v-model="ruleForm.suitableCrowd"
            style="width: 368px"
            placeholder="请输入内容"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>禁忌人群</p>
          </div>
          <el-input
            type="textarea"
            v-model="ruleForm.tabooGroups"
            style="width: 368px"
            placeholder="请输入内容"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>项目优点</p>
          </div>
          <el-input
            type="textarea"
            v-model="ruleForm.projectAdvantages"
            style="width: 368px"
            placeholder="请输入内容"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>项目缺点</p>
          </div>
          <el-input
            type="textarea"
            v-model="ruleForm.projectDisadvantages"
            style="width: 368px"
            placeholder="请输入内容"
            :disabled="forbidden"
          ></el-input>
        </div>
      </div>
      <div v-if="activeName == 'second'">
        <!-- <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>签约礼</p>
          </div>
          <div>
            <el-select
              v-model="ruleForm.contractGiftId"
              placeholder="请选择签约礼"
              clearable
              style="width: 240px"
              :disabled="forbidden"
            >
              <el-option
                v-for="item in giftList"
                :key="item.contractGiftId"
                :label="item.description"
                :value="item.contractGiftId"
              />
            </el-select>
          </div>
        </div> -->
        <!-- <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>服务方式</p>
          </div>
          <div>
            <el-radio-group
              v-model="ruleForm.serviceMode"
              class="checkboxList"
              :disabled="forbidden"
            >
              <el-radio
                :label="item.dictValue"
                v-for="item in serviceModeList"
                :key="item.dictValue"
                :value="item.dictValue"
                class="checkboxLists"
                >{{ item.dictLabel }}</el-radio
              >
            </el-radio-group>
          </div>
        </div> -->
        <div class="tabTitle">项目明细</div>
        <div class="fgx"></div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>服务分类</p>
          </div>
          <div>
            <el-select
              v-model="ruleForm.serviceCategory"
              placeholder="请选择类别"
              clearable
              :disabled="forbidden"
              class="sel"
            >
              <el-option
                v-for="item in serviceCategoryList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictLabel"
              />
            </el-select>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>服务次数</p>
          </div>
          <el-input
            placeholder="请输入服务次数"
            class="input"
            v-model="ruleForm.serviceCount"
            :disabled="forbidden"
          >
            <template slot="append">次</template>
          </el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>单次时长</p>
          </div>
          <el-input
            placeholder="请输入单次时长"
            class="input"
            v-model="ruleForm.singleDuration"
            :disabled="forbidden"
            type="number"
          >
            <template slot="append">分钟</template>
          </el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>服务功效</p>
          </div>
          <el-input
            type="text"
            placeholder="请输入产品功效"
            class="input"
            v-model="ruleForm.serviceEffect"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>服务仪器</p>
          </div>
          <el-input
            type="text"
            placeholder="请输入服务仪器"
            class="input"
            v-model="ruleForm.serviceDevice"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>服务流程</p>
          </div>
          <el-input
            type="textarea"
            v-model="ruleForm.serviceProcess"
            style="width: 368px"
            placeholder="请输入服务流程"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>服务次数类型</p>
          </div>
          <div>
            <el-select
              v-model="ruleForm.serviceCountType"
              placeholder="请选择服务类型"
              clearable
              :disabled="forbidden"
              class="sel"
            >
              <el-option
                v-for="item in serveTime"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictLabel"
              />
            </el-select>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>修护方式</p>
          </div>
          <div>
            <el-select
              v-model="ruleForm.maintenanceMethod"
              placeholder="请选择修护方式"
              clearable
              :disabled="forbidden"
              class="sel"
            >
              <el-option
                v-for="item in recoveryMethod"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictLabel"
              />
            </el-select>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>仪器类型</p>
          </div>
          <div>
            <el-select
              v-model="ruleForm.deviceType"
              placeholder="请选择仪器类型"
              clearable
              :disabled="forbidden"
              class="sel"
            >
              <el-option
                v-for="item in InstrumentType"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictLabel"
              />
            </el-select>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>仪器生产来源</p>
          </div>
          <div>
            <el-select
              v-model="ruleForm.deviceManufacturer"
              placeholder="请选择仪器生产来源"
              clearable
              :disabled="forbidden"
              class="sel"
            >
              <el-option
                v-for="item in productionSource"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictLabel"
              />
            </el-select>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>是否含耗材费</p>
          </div>
          <div>
            <el-radio-group
              v-model="ruleForm.includeConsumablesFee"
              class="checkboxList"
              :disabled="forbidden"
            >
              <el-radio
                :label="item.dictValue"
                v-for="item in textureList"
                :key="item.dictValue"
                :value="item.dictValue"
                class="checkboxLists"
                >{{ item.dictLabel }}</el-radio
              >
            </el-radio-group>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>费用内耗材信息</p>
          </div>
          <el-input
            type="textarea"
            v-model="ruleForm.feeDetails"
            style="width: 368px"
            placeholder="请输入内耗材信息 如:一次性床单"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>服务方式</p>
          </div>
          <div>
            <el-radio-group
              v-model="ruleForm.serviceMode"
              class="checkboxList"
              :disabled="forbidden"
            >
              <el-radio
                :label="item.dictValue"
                v-for="item in serviceModeList"
                :key="item.dictValue"
                :value="item.dictValue"
                class="checkboxLists"
                >{{ item.dictLabel }}</el-radio
              >
            </el-radio-group>
          </div>
        </div>
        <div class="tabTitle">项目周期</div>
        <div class="fgx"></div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>操作时长</p>
          </div>
          <el-input
            type="text"
            v-model="ruleForm.operationDuration"
            style="width: 368px"
            placeholder="请输入操作时长"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>作用呈现</p>
          </div>
          <el-input
            type="text"
            v-model="ruleForm.effectPresentation"
            style="width: 368px"
            placeholder="请输入项目呈现周期"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>恢复周期</p>
          </div>
          <el-input
            type="text"
            v-model="ruleForm.recoveryCycle"
            style="width: 368px"
            placeholder="请输入项目恢复周期"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>间隔周期</p>
          </div>
          <el-input
            type="text"
            v-model="ruleForm.intervalCycle"
            style="width: 368px"
            placeholder="请输入项目问隔周期"
            :disabled="forbidden"
          ></el-input>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>项目痛感</p>
          </div>
          <el-input
            type="text"
            v-model="ruleForm.projectPainLevel"
            style="width: 368px"
            placeholder="请输入项目痛感 如:无明显痛感"
            :disabled="forbidden"
          ></el-input>
        </div>
      </div>
      <div v-if="activeName == 'third'">
        <div
          v-for="(item, index) in ruleForm.postpartumDeviceDetails"
          :key="index"
        >
          <div class="content1">
            <div class="title5">
              <p class="required"></p>
              <p>仪器图片</p>
            </div>
            <div class="upload">
              <image-upload
                :oneAll="3"
                :limit="8"
                :isShowTip="false"
                v-model="item.devicePhotos"
                v-if="!forbidden"
              />
              <div v-if="forbidden">
                <img
                  :src="item"
                  alt=""
                  v-for="(item, index) in item.devicePhotos"
                  :key="index"
                  style="
                    width: 146px;
                    height: 146px;
                    margin-right: 10px;
                    border: 1px solid #c0ccda;
                    border-radius: 6px;
                  "
                />
              </div>
            </div>
          </div>
          <div class="content1">
            <div class="title5">
              <p class="required"></p>
              <p>仪器名称</p>
            </div>
            <el-input
              type="text"
              v-model="item.deviceName"
              style="width: 368px"
              placeholder="请输入仪器名称"
              :disabled="forbidden"
            ></el-input>
          </div>
          <div class="content1">
            <div class="title5">
              <p class="required"></p>
              <p>仪器介绍</p>
            </div>
            <el-input
              type="textarea"
              v-model="item.deviceIntro"
              style="width: 368px"
              placeholder="请输入仪器介绍"
              :disabled="forbidden"
            ></el-input>
          </div>
          <div class="content1" v-if="!forbidden">
            <div class="title5">
              <p class="required"></p>
              <p>仪器功效</p>
            </div>
            <el-tag
              :key="tag"
              v-for="tag in item.deviceEffect"
              closable
              :disable-transitions="false"
              @close="handleClosess2(tag, index)"
              class="elTag"
            >
              {{ tag }}
            </el-tag>
          </div>
          <div class="content1" v-if="forbidden">
            <div class="title5">
              <p class="required"></p>
              <p>仪器功效</p>
            </div>
            <el-tag
              :key="tag"
              v-for="tag in item.deviceEffect"
              closable
              :disable-transitions="false"
              @close="handleClosess2(tag, index)"
              class="elTag"
            >
              {{ tag }}
            </el-tag>
          </div>
          <div class="content1">
            <div class="title5"></div>
            <div class="label">
              <el-tag
                :key="tag"
                v-for="tag in deviceEffect"
                closable
                :disable-transitions="false"
                @close="handleClose2(tag)"
                @click="choice2(tag, index)"
                class="elTag"
              >
                {{ tag }}
              </el-tag>
              <el-input
                class="input-new-tag"
                v-if="inputVisible2"
                v-model="inputValue"
                ref="saveTagInput"
                size="small"
                @keyup.enter.native="handleInputConfirm2"
                @blur="handleInputConfirm2"
              >
              </el-input>
              <el-button
                v-else
                class="button-new-tag"
                size="small"
                @click="showInput2"
                style="height: 26px; line-height: 26px"
                :disabled="forbidden"
                >+ 请输入您要添加的标签</el-button
              >
            </div>
          </div>
          <div class="content1">
            <div class="title5">
              <p class="required"></p>
              <p>适用人群</p>
            </div>
            <div>
              <el-input
                type="textarea"
                v-model="item.applicablePeople"
                style="width: 368px"
                placeholder="请输入适用人群"
                :disabled="forbidden"
              ></el-input>
            </div>
            <div class="deleteBtn" @click="deleteInstrument(index)">删除</div>
          </div>
        </div>
        <div class="addyq">
          <el-button type="primary" icon="el-icon-plus" @click="addInstrument"
            >添加仪器</el-button
          >
        </div>
      </div>
      <div class="btn" v-if="!audit">
        <el-button type="primary" @click="confirm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
      <div class="btn" v-if="auditInfoList.auditStatus == 0">
        <el-button type="primary" @click="pass">通过</el-button>
        <el-button @click="reject">驳回</el-button>
      </div>
      <div class="btn">
        <el-button type="success" v-if="auditInfoList.auditStatus == 1"
          >已通过</el-button
        >
        <el-button type="warning" v-if="auditInfoList.auditStatus == 2"
          >已驳回</el-button
        >
      </div>
    </div>
    <!--驳回-->
    <el-dialog title="驳回原因" :visible.sync="dialogVisibleReject" width="30%">
      <el-input
        type="textarea"
        v-model="rejectionReason"
        placeholder="请输入驳回原因"
      ></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleReject = false">取 消</el-button>
        <el-button type="primary" @click="rejectConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-image-viewer
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="[url]"
    />
  </div>
</template>

<script>
import ImageUpload from "@/components/ImageUpload/index";
import { save, update, info } from "@/api/platform/postBirth";
import FileUpload from "@/components/FileUpload/index";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import { giftList } from "@/api/platform/public";
export default {
  name: "app",
  components: { ImageUpload, FileUpload, ElImageViewer },
  data() {
    return {
      deviceEffect: [],
      projectEfficacy: [],
      postpartumDeviceDetails: [
        {
          devicePhotos: [], //仪器图片
          deviceName: "", //仪器名称
          deviceIntro: "", //仪器介绍
          deviceEffect: [], //仪器功效
          applicablePeople: "", //适应人群
        },
      ],
      activeName: "first",
      giftList: [],
      showViewer: false, // 显示查看器
      url: "",
      inputVisible: false,
      inputVisible2: false,
      inputVisible1: false,
      inputValue: "",
      labelList: [],
      projectId: "",
      audit: "",
      auditId: "",
      forbidden: false,
      auditInfoList: [],
      dialogVisibleReject: false,
      rejectionReason: "",
      recoveryMethod: [
        {
          dictLabel: "纯仪器",
          dictValue: "1",
        },
        {
          dictLabel: "纯手法",
          dictValue: "2",
        },
        {
          dictLabel: "仪器+手法",
          dictValue: "3",
        },
      ],
      productionSource: [
        {
          dictLabel: "进口",
          dictValue: "1",
        },
        {
          dictLabel: "国产",
          dictValue: "2",
        },
      ],
      InstrumentType: [
        {
          dictLabel: "电刺激",
          dictValue: "1",
        },
        {
          dictLabel: "磁刺激",
          dictValue: "2",
        },
        {
          dictLabel: "磁绥込电联合",
          dictValue: "3",
        },
      ],
      serveTime: [
        {
          dictLabel: "有限次数",
          dictValue: "1",
        },
      ],
      serviceCategoryList: [
        {
          dictLabel: "产后发汗",
          dictValue: "1",
        },
        {
          dictLabel: "气血调理",
          dictValue: "2",
        },
        {
          dictLabel: "产后药浴",
          dictValue: "3",
        },
        {
          dictLabel: "产后艾灸",
          dictValue: "4",
        },
        {
          dictLabel: "其他调理服务",
          dictValue: "5",
        },
      ],
      serviceModeList: [
        {
          dictLabel: "可上门",
          dictValue: "0",
        },
        {
          dictLabel: "仅到店",
          dictValue: "1",
        },
        {
          dictLabel: "在线服务",
          dictValue: "2",
        },
      ],
      textureList: [
        //是否含耗材费
        {
          dictLabel: "包含",
          dictValue: true,
        },
        {
          dictLabel: "不包含",
          dictValue: false,
        },
      ],
      checkList: ["选中且禁用", "复选框 A"],
      cities: [
        {
          name: "可上门",
          check: false,
        },
        {
          name: "仅到店",
          check: false,
        },
        {
          name: "在线服务",
          check: false,
        },
      ],
      ruleForm: {
        sortKey: "",
        contractGiftId: "",
        FileUpload: [],
        projectName: "", //项目名称
        displayPhotos: [], //项目照片
        descriptionPhotos: [], //图文详情
        serviceMode: "", //服务方式
        serviceCategory: "", //服务分类
        serviceCount: "", //服务次数
        singleDuration: "", //单次时长
        serviceEffect: "", //服务功效
        dialogVisible: false,
        tag: [], //产后康复标签
        price: "", //价格
        description: "", //描述
        projectIntroduction: "", //项目介绍
        projectAlias: "", //项目别名
        projectEfficacy: [], //项目功效
        suitableCrowd: "", //适用人群
        projectAdvantages: "", //项目优点
        projectDisadvantages: "", //项目缺点
        serviceDevice: "", //服务仪器
        serviceProcess: "", //服务流程
        serviceCountType: "", //服务次数类型
        maintenanceMethod: "", //维护方式
        deviceType: "", //仪器类型
        deviceManufacturer: "", //仪器生产来源
        includeConsumablesFee: false, //是否含耗材费
        feeDetails: "", //费用内耗信息
        serviceMethod: "", //服务方式
        operationDuration: "", //操作时长
        effectPresentation: "", //作用呈现
        recoveryCycle: "", //恢复周期
        intervalCycle: "", //间隔周期
        projectPainLevel: "", //项目痛感
        isShowProjectDetails: true, //是否显示项目明细
        isShowDevice: false, //是否显示仪器
        postpartumDeviceDetails: [
          {
            devicePhotos: [], //仪器图片
            deviceName: "", //仪器名称
            deviceIntro: "", //仪器介绍
            deviceEffect: [], //仪器功效
            applicablePeople: "", //适应人群
          },
        ], //关联仪器
        tabooGroups: "", //禁忌人群
      },
    };
  },
  created() {
    let projectId = this.$route.query.projectId;
    let audit = this.$route.query.audit;
    this.audit = audit;
    this.forbidden = this.$route.query.forbidden ? true : false;
    this.auditId = this.$route.query.auditId;
    if (audit) {
      this.auditInfo(this.auditId);
    }
    this.projectId = projectId;
    if (projectId) {
      this.infos(projectId);
    }
    this.getList();
  },
  methods: {
    deleteInstrument(index) {
      this.ruleForm.postpartumDeviceDetails.splice(index, 1);
    },
    addInstrument() {
      this.ruleForm.postpartumDeviceDetails.push({
        devicePhotos: [], //仪器图片
        deviceName: "", //仪器名称
        deviceIntro: "", //仪器介绍
        deviceEffect: [], //仪器功效
        applicablePeople: "", //适应人群
      });
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    getList() {
      //列表
      this.loading = true;
      let data = {
        pageSize: 1000,
        pageNum: 1,
      };
      giftList(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.giftList = res.data;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    onPreview(e) {
      this.showViewer = true;
      this.url = e;
    },
    // 关闭查看器
    closeViewer() {
      this.showViewer = false;
    },
    cancel() {
      this.$router.go(-1);
    },
    showInput2() {
      this.inputVisible2 = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm2() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.deviceEffect.push(inputValue);
      }
      console.log(this.inputValue);
      this.inputVisible2 = false;
      this.inputValue = "";
    },
    handleClosess2(tag, index) {
      this.ruleForm.postpartumDeviceDetails[index].deviceEffect.splice(
        this.ruleForm.postpartumDeviceDetails[index].deviceEffect.indexOf(tag),
        1
      );
    },
    handleClose2(tag) {
      this.deviceEffect.splice(this.deviceEffect.indexOf(tag), 1);
    },
    choice2(e, index) {
      this.ruleForm.postpartumDeviceDetails[index].deviceEffect.forEach(
        (item) => {
          console.log(item, e);
          if (item == e) {
            this.$message("标签已添加");
            black;
          }
        }
      );
      this.ruleForm.postpartumDeviceDetails[index].deviceEffect.push(e);
      console.log(this.ruleForm.postpartumDeviceDetails);
    },

    showInput1() {
      this.inputVisible1 = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm1() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.projectEfficacy.push(inputValue);
      }
      this.inputVisible1 = false;
      this.inputValue = "";
    },
    handleClosess1(tag) {
      this.ruleForm.projectEfficacy.splice(
        this.ruleForm.projectEfficacy.indexOf(tag),
        1
      );
    },
    handleClose1(tag) {
      this.projectEfficacy.splice(this.projectEfficacy.indexOf(tag), 1);
    },
    choice1(e) {
      console.log(this.ruleForm.projectEfficacy);
      this.ruleForm.projectEfficacy.forEach((item) => {
        if (item == e) {
          this.$message("标签已添加");
          black;
        }
      });
      this.ruleForm.projectEfficacy.push(e);
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.labelList.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
    handleClosess(tag) {
      this.ruleForm.tag.splice(this.ruleForm.tag.indexOf(tag), 1);
    },
    handleClose(tag) {
      this.labelList.splice(this.labelList.indexOf(tag), 1);
    },
    choice(e) {
      this.ruleForm.tag.forEach((item) => {
        if (item == e) {
          this.$message("标签已添加");
          black;
        }
      });
      this.ruleForm.tag.push(e);
    },
    rejectConfirm() {
      //驳回确定
      if (this.rejectionReason == "") {
        this.$message("请填写驳回理由");
        return;
      }
      this.dialogVisibleReject = false;
      let data = {
        auditId: this.auditId,
        auditResult: false,
        rejectionReason: this.rejectionReason,
      };
      audit(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.$router.go(-1);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    reject() {
      //驳回
      this.dialogVisibleReject = true;
    },
    pass() {
      //通过
      this.$confirm("是否审核通过?", "审核通过", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // this.$message({
          //   type: 'success',
          //   message: '审核通过!'
          // });
          let data = {
            auditId: this.auditId,
            auditResult: true,
          };
          audit(data).then((res) => {
            if (res.code == 200) {
              this.$message(res.msg);
              this.$router.go(-1);
              return;
            } else {
              this.$message(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消审核",
          });
        });
    },

    auditInfo(auditId) {
      //查询审核信息
      auditInfo(auditId).then((res) => {
        if (res.code == 200) {
          this.auditInfoList = res.data;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },

    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    confirm() {
      //确定
      let data = this.ruleForm;
      if (data.projectName == "") {
        this.$message("请输入项目名称");
        return;
      }
      if (data.tag.length == 0) {
        this.$message("请添加产康标签");
        return;
      }
      if (data.description == "") {
        this.$message("请输入描述");
        return;
      }
      if (data.displayPhotos.length == 0) {
        this.$message("请上传项目照片");
        return;
      }
      if (data.descriptionPhotos.length == 0) {
        this.$message("请上传图文详情");
        return;
      }
      if (data.serviceMode == "") {
        this.$message("请选择服务方式");
        return;
      }
      // if (data.serviceCategory == "") {
      //   this.$message("请选择服务分类");
      //   return;
      // }
      // if (data.serviceCount == "") {
      //   this.$message("请输入服务次数");
      //   return;
      // }

      // if (data.singleDuration == "") {
      //   this.$message("请输入单次时长");
      //   return;
      // }
      // if (data.serviceEffect == "") {
      //   this.$message("请输入服务功效");
      //   return;
      // }
      // if (data.price == '') {
      //     this.$message('请输入价格');
      //     return
      // }
      if (!this.projectId) {
        save(this.ruleForm).then((res) => {
          console.log(res);
          if (res.code == 200) {
            this.$message(res.msg);
            this.$router.go(-1);
            return;
          } else {
            this.$message(res.msg);
          }
        });
      }
      if (this.projectId) {
        update(this.ruleForm).then((res) => {
          console.log(res);
          if (res.code == 200) {
            this.$message(res.msg);
            this.$router.go(-1);
            return;
          } else {
            this.$message(res.msg);
          }
        });
      }
    },
    infos(projectId) {
      //查询产后康复信息
      info(projectId).then((res) => {
        console.log(res.code);
        if (res.code == 200) {
          console.log(res.data);
          this.ruleForm = res.data;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .title {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
  padding: 24px 50px;
  .title2 {
    font-size: 20px;
    color: #17191a;
    margin-bottom: 24px;
  }
  .table {
    margin-top: 20px;
  }
  .title5 {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
    width: 10%;
    .required {
      color: red;
    }
  }
  .content1 {
    display: flex;
    align-items: center;
    margin: 20px 0 10px 0;
    .input {
      width: 240px;
      height: 32px;
      border-radius: 3px;
    }
    .upload {
      width: 90%;
    }
  }
}
.btn {
  margin: 20px auto;
  text-align: center;
}
#cars {
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  color: #7c7d81;
  font-size: 14px;
  width: 129px;
  height: 32px;
  margin-left: 6px;
}
.postpartum {
  display: flex;
}
.inputs {
  width: 100px;
  height: 30px;
  border-radius: 3px;
}
.units {
  width: 46px;
  height: 32px;
  background: #dcdcdc;
  border: 1px solid #dcdcdc;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
}
.auditContent {
  width: 100%;
  padding: 20px 24px;
  background: #ff6c11;
  border-radius: 6px;
  font-size: 14px;
  line-height: 14px;
}
.pass {
  display: flex;
  align-items: center;
  color: #000000;
}
.passCause {
  color: #000000;
  margin-left: 20px;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.label {
  width: 368px;
  height: 86px;
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  display: flex;
  flex-wrap: wrap;
  padding: 10px 10px;
  .labelName {
    color: #7c7d81;
    font-size: 14px;
    height: 24px;
    border-radius: 3px;
    line-height: 24px;
    background: #ebebeb;
    margin-right: 16px;
    padding: 0 8px;
  }
  .inputLabel {
    width: 156px;
    font-size: 12px;
  }
}
.demo-ruleForm {
  margin-left: 100px;
  margin-top: 20px;
}
.sel {
  width: 240px;
}
.elTag {
  margin-right: 5px;
  margin-bottom: 5px;
}
.viewLocations {
  display: flex;
  .viewLocation {
    padding: 5px 5px;
    border-radius: 10px;
    border: 1px solid #dcdcdc;
    margin-left: 10px;
    height: 150px;
    .viewLocation1 {
      color: #3f85ff;
      font-size: 14px;
      text-align: center;
      margin-top: 8px;
    }
  }
}
.tabTitle {
  color: #17191a;
  font-size: 18px;
  font-weight: bold;
  padding: 10px 40px;
}
.fgx {
  width: 100%;
  height: 1px;
  background: #ebebeb;
}
.deleteBtn {
  color: #f84343;
  font-size: 14px;
  font-weight: bold;
  margin-left: 20px;
  cursor: pointer;
}
.addyq {
  margin-left: 140px;
  cursor: pointer;
}
</style>
