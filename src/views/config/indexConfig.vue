<template>
  <div class="app-container" style="background: #ffff">
    <el-form
      :model="form"
      label-width="200px"
      :rules="rules"
      ref="form"
      v-loading.fullscreen.lock="fullscreenLoading"
    >
      <h2>基本信息</h2>
      <el-divider></el-divider>
      <el-form-item label="LOGO" prop="logo">
        <image-upload :limit="1" :isShowTip="false" v-model="form.logo" />
      </el-form-item>
      <el-form-item label="会所名称" prop="clubName">
        <el-input
          v-model="form.clubName"
          maxlength="15"
          minlength="3"
          placeholder="请输入会所名称字符长度：3~15"
          style="width: 60%"
        ></el-input>
      </el-form-item>

      <el-form-item label="会所标签" :rules="[{ required: true }]">
        <el-tag
          :key="tag"
          v-for="tag in dynamicTags"
          closable
          :disable-transitions="false"
          @close="handleClose(tag)"
        >
          {{ tag }}
        </el-tag>
        <el-input
          class="input-new-tag"
          v-if="inputVisible"
          v-model="inputValue"
          ref="saveTagInput"
          size="small"
          @keyup.enter.native="handleInputConfirm"
          @blur="handleInputConfirm"
        >
        </el-input>
        <el-button v-else class="button-new-tag" size="small" @click="showInput"
          >+ 添加标签</el-button
        >
      </el-form-item>

      <el-form-item label="背景照片" prop="clubBackgroundPhotos">
        <image-upload
          :limit="1"
          :isShowTip="false"
          v-model="form.clubBackgroundPhotos"
        />
      </el-form-item>

      <!-- <el-form-item label="活动区照片" prop="clubActivityAreaPhotos">
        <image-upload :limit="6" :size="1" :isShowTip="false" v-model="form.clubActivityAreaPhotos" />
      </el-form-item> -->

      <el-form-item label="地址" prop="locationAddress">
        <el-input
          v-model="form.locationAddress"
          placeholder="请输入详情地址,格式：XX省XX市XX区（县）XXXXXX"
          style="width: 60%"
        ></el-input>
      </el-form-item>

      <el-form-item label="客服电话" prop="servicePhone">
        <el-input
          v-model="form.servicePhone"
          placeholder="请输入客服电话"
          style="width: 60%"
        ></el-input>
      </el-form-item>

      <h2>品牌介绍</h2>
      <el-divider></el-divider>

      <el-form-item label="Banner" prop="clubFacilityPhotos">
        <image-upload
          :limit="1"
          v-model="form.clubFacilityPhotos"
          :is-show-tip="false"
        />
      </el-form-item>

      <el-form-item label="会所介绍" prop="clubDescription">
        <el-input
          type="textarea"
          v-model="form.clubDescription"
          style="width: 60%"
          placeholder="请输入会所介绍"
          :rows="4"
        ></el-input>
      </el-form-item>

      <el-form-item label="图册" prop="clubFacilitiesPhotos">
        <image-upload
          :limit="12"
          v-model="form.clubFacilitiesPhotos"
          :is-show-tip="false"
        />
      </el-form-item>
    </el-form>

    <div style="margin: 20px auto; text-align: center">
      <el-button type="primary" @click="submitForm">确定</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script>
import ImageUpload from "@/components/ImageUpload/index";
import FileUpload from "@/components/FileUpload/index";
import { save, info, update } from "@/api/platform/indexConfig";
export default {
  name: "CLUB",
  components: { ImageUpload, FileUpload },
  data() {
    return {
      inputVisible: false,
      inputValue: "",
      form: {
        clubName: "", //会所名称
        clubTagNames: [], //会所标签
        clubBackgroundPhotos: [], //背景照片
        clubActivityAreaPhotos: [], //活动区照片
        locationAddress: "", //店铺地址
        clubFacilityPhotos: [], //会所照片
        clubDescription: "", //会所介绍
        clubFacilitiesPhotos: [], //会所设施照片
        bossTel: "", //管理者电话
        dialogVisible: false,
        desc: "",
        videos: [],
        clubActivities: [],
        servicePhone: "",
        logo: [],
      },
      dynamicTags: [],
      rules: {
        logo: [
          { required: true, message: "会所logo不能为空", trigger: "blur" },
        ],
        clubName: [
          { required: true, message: "会所名称不能为空", trigger: "blur" },
        ],
        clubBackgroundPhotos: [
          { required: true, message: "背景图片不能为空", trigger: "blur" },
        ],
        locationAddress: [
          { required: true, message: "会所地址不能为空", trigger: "blur" },
        ],
        servicePhone: [
          { required: true, message: "会所客服电话不能为空", trigger: "blur" },
        ],
        clubFacilityPhotos: [
          { required: true, message: "品牌Banner不能为空", trigger: "blur" },
        ],
        clubDescription: [
          { required: true, message: "品牌介绍不能为空", trigger: "blur" },
        ],
        clubFacilitiesPhotos: [
          { required: true, message: "会所图册不能空为空", trigger: "blur" },
        ],
      },
      fullscreenLoading: false,
    };
  },
  created() {
    this.info();
  },
  watch: {
    dynamicTags() {
      if (this.dynamicTags.length > 0) {
        this.form.clubTagNames = this.dynamicTags;
      }
    },
  },
  methods: {
    cancel() {
      //取消
      this.info();
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.dynamicTags.length <= 0) {
            this.$modal.msgError("请至少添加一个会所标签");
            return;
          }
          if (this.form.logo.length >= 0) {
            this.form.logo = this.form.logo[0];
          }
          this.fullscreenLoading = true;
          if (this.form.clubId != null) {
            update(this.form)
              .then((res) => {
                this.info();
              })
              .finally(() => {
                this.fullscreenLoading = false;
              });
          } else {
            save(this.form)
              .then((res) => {
                this.info();
              })
              .finally(() => {
                this.fullscreenLoading = false;
              });
          }
        }
      });
    },
    info() {
      this.fullscreenLoading = true;
      info()
        .then((res) => {
          this.form = res.data;
          this.dynamicTags = res.data.clubTagNames ? res.data.clubTagNames : [];
        })
        .finally(() => {
          this.fullscreenLoading = false;
        });
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        if (this.dynamicTags.length >= 2) {
          this.$modal.msgError("最多添加两个标签");
        } else {
          this.dynamicTags.push(inputValue);
        }
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
  },
};
</script>

<style scoped lang="scss">
.el-tag + .el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

h2 {
  margin: 0px 0;
}

.el-divider {
  margin: 10px 0;
}
</style>
