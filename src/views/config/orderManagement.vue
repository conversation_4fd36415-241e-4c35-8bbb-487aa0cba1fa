<template>
    <div class="app" v-loading="loading">
        <!-- <div class="head">
            <div class="titles">订单管理</div>
            <div class="title1">Pages/订单管理</div>
        </div> -->
        <div class="content">
            <div class="operation">
        <el-input v-model="input" placeholder="请在此输入券码进行验证" style="width:176px ;margin-right:10px ;"></el-input>
    <div class="inquire">验券</div>
  </div>
            <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="全部订单" name="first"></el-tab-pane>
    <el-tab-pane label="已验证" name="second"></el-tab-pane>
    <el-tab-pane label="未验证" name="seconds"></el-tab-pane>
  </el-tabs>
  <div class="operation">
    <div class="phone">
        <p class="phoneTitle">电话号码</p>
        <el-input v-model="input" placeholder="请输入电话号码" style="width:176px ;margin: 0  10px ;"></el-input>
    </div>
    <div class="inquire">查询</div>
  </div>
             <el-table :data="roleList"  class="table">
      <el-table-column label="商品名称" prop="name"align="center" />
      <el-table-column label="商品编号" prop="price" :show-overflow-tooltip="true"  align="center" />
      <el-table-column label="商品价格" prop="status" :show-overflow-tooltip="true" align="center" >
    </el-table-column>
      <el-table-column label="用户电话" prop="auditStatus" align="center" >
      </el-table-column>
      <el-table-column label="验证状态" prop="auditStatus"  align="center" >
      </el-table-column>
      <el-table-column label="验证时间" prop="auditStatus" align="center" >
      </el-table-column>
    </el-table>
    <div class="block">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-sizes="[100, 200, 300, 400]"
      :page-size="100"
      layout="total, sizes, prev, pager, next, jumper"
      :total="400">
    </el-pagination>
  </div>
        </div>
        <el-dialog
  title="删除"
  :visible.sync="deleteDialogVisible"
  width="30%">
  <span>是否删除此优惠套餐？</span>
  <span slot="footer" class="dialog-footer">
    <el-button @click="deleteDialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="deleteDialogVisible = false">确 定 删 除</el-button>
  </span>
</el-dialog>
    </div>
</template>

<script>
export default {
    name: "app",
  data() {
    return {
      loading:false,
      input:'',
        addList:[1],
        activeName: 'second',
      deleteDialogVisible:false,
        roleList:[
            {
                name:'超值套餐',
                price:'298000',
                status:1,
                time:'2024年12月12日21:14:25',
                auditStatus:1,
                show:1,
            },
            {
                name:'超值套餐',
                price:'298000',
                status:0,
                time:'2024年12月12日21:14:25',
                auditStatus:0,
                show:0,
            }
        ]
    }
},methods:{
  handleClick(){

  },
    handleSizeChange(val) {
        console.log(`每页 ${val} 条`);
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`);
      },
      addMaternitySuite(){//新增房型
        this.$router.push({ path: "/addMaternitySuite" });
      },
      addProject(){
        this.addList.push(1)
      },
      handleDelete(e){//删除
        let index=e
        this.addList.splice(index,1)
      }
}
}
</script>

<style scoped lang="scss">
.head{
    width: 100%;
    height: 80px;
    background: #ffff;
    color: #17191A;
    padding: 15px 20px;
    .titles{
        font-size: 22px;
        font-weight: bold;
    }
    .title1{
        font-size: 14px;
    }
}
.content{
    background: #ffff;
    margin: 10px 10px;
    border-radius: 5px;
    padding: 24px 20px;
    .title2{
        font-size: 20px;
        color: #17191A;
        margin-bottom: 24px;
    }
    .table{
        margin-top: 20px;
    }
}
.block{
    text-align: right;
    margin-top: 20px;
}
.operation{
    display: flex;
    margin-bottom: 10px;
    align-items: center;
}
.edit{
    color: #F84343;
}
.phone{
    display: flex;
    align-items: center;
    .phoneTitle{
        font-size: 14px;
        color: #45464A;
    }
   
}
.inquire{
        width: 60px;
        height: 32px;
        background: #3F85FF;
        color: #ffff;
        border-radius: 3px;
        text-align: center;                                                     
        line-height: 32px;   
        font-size: 12px;
}
</style>