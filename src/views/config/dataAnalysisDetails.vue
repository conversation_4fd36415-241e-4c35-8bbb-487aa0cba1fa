<template>
  <div class="app-container home" id="app">
    <div class="tab" v-if="type == 1">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="用户分析" name="1"></el-tab-pane>
        <el-tab-pane label="用户详情" name="2"></el-tab-pane>
      </el-tabs>
    </div>
    <div v-if="activeName == '2'" class="userDetails">
      <p class="userDetailsTitle">用户详情</p>
      <p class="fgx"></p>
      <div class="userDetails2">
        <div class="userDetails1">
          <p class="userDetails1_1">用户姓名</p>
          <p class="userDetails1_2">{{ row.nickname }}</p>
        </div>
        <div class="userDetails1">
          <p class="userDetails1_1">电话号码</p>
          <p class="userDetails1_2">{{ row.tel }}</p>
        </div>
      </div>
      <div class="userDetails2">
        <div class="userDetails1">
          <p class="userDetails1_1">性别</p>
          <p class="userDetails1_2">{{ row.sex == 0 ? '男' : '女' }}</p>
        </div>
        <div class="userDetails1">
          <p class="userDetails1_1">年龄</p>
          <p class="userDetails1_2">{{ row.age }}</p>
        </div>
      </div>
      <div class="userDetails2">
        <div class="userDetails1">
          <p class="userDetails1_1">用户类型</p>
          <p class="userDetails1_2">{{ row.type == 'new' ? '新用户' : '老用户' }}</p>
        </div>
        <div class="userDetails1">
          <p class="userDetails1_1">页面访问量</p>
          <p class="userDetails1_2">{{ row.pageNum }}</p>
        </div>
      </div>
      <div class="userDetails2">
        <div class="userDetails1">
          <p class="userDetails1_1">访问次数</p>
          <p class="userDetails1_2">{{ row.pv }}</p>
        </div>
        <div class="userDetails1">
          <p class="userDetails1_1">停留时间</p>
          <p class="userDetails1_2">{{ row.viewTime }}</p>
        </div>
      </div>
      <div class="userDetails2">
        <div class="userDetails1">
          <p class="userDetails1_1">预产期</p>
          <p class="userDetails1_2">{{ row.dueDate }}</p>
        </div>

      </div>
    </div>
    <div v-if="activeName == '1'">
      <div class="dataDisplay">
        <div class="dataDisplay1">
          <p class="dataDisplay1_1">用户姓名</p>
          <div class="evaluationManagement3">
            <img src="http://cdn.xiaodingdang1.com/2024/04/15/9fa2b8690d80451baeb0a4d29cd53dc0png" alt=""
              style="width: 88px;height: 88px;">
            <div class="evaluationManagement3_1">
              <p class="evaluationManagement3_2">用户姓名</p>
              <p class="evaluationManagement3_3">{{ listData.nickname }}</p>
            </div>
          </div>
        </div>
        <div class="dataDisplay1">
          <p class="dataDisplay1_1">联系电话</p>
          <div class="evaluationManagement3">
            <img src="http://cdn.xiaodingdang1.com/2024/04/15/710581bbc514462eaa4d90a44fd117b1png" alt=""
              style="width: 88px;height: 88px;">
            <div class="evaluationManagement3_1">
              <p class="evaluationManagement3_2">联系电话</p>
              <p class="evaluationManagement3_3">{{ listData.tel }}</p>
            </div>
          </div>
        </div>
        <div class="dataDisplay1" v-if="listData.dueDate">
          <p class="dataDisplay1_1">预产期</p>
          <div class="evaluationManagement3">
            <img src="http://cdn.xiaodingdang1.com/2024/04/15/48faf4f00cd4438ab2aa4721789831efpng" alt=""
              style="width: 88px;height: 88px;">
            <div class="evaluationManagement3_1">
              <p class="evaluationManagement3_2">预产期</p>
              <p class="evaluationManagement3_3">{{ listData.dueDate }}</p>
            </div>
          </div>
        </div>
        <div class="dataDisplay1" id="dataDisplay1" v-if="!listData.dueDate">
          <p class="dataDisplay1_1">预产期</p>
          <div class="evaluationManagement3">
            <img src="http://cdn.xiaodingdang1.com/2024/04/15/48faf4f00cd4438ab2aa4721789831efpng" alt=""
              style="width: 88px;height: 88px;">
            <div class="evaluationManagement3_1">
              <p class="evaluationManagement3_2">预产期</p>
              <p class="evaluationManagement3_3">无</p>
            </div>
          </div>
        </div>
      </div>
      <div class="dataStatistics">
        <div class="dataStatistics1">
          <img src="http://cdn.xiaodingdang1.com/2024/04/15/9cdfa26cb63a4263adad3a375492808apng" alt=""
            style="width: 60px;height: 60px;">
          <p class="dataStatistics2">{{ listData.roomStayTimeStr }}</p>
          <p class="dataStatistics3">房间停留时间</p>
          <!-- <div class="dataStatistics3_3">
                  <p class="dataStatistics3_3_1">较前一日</p>
                  <div class="dataStatistics3_3_2">
                    <img src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png" alt="" style="width: 8px;height: 16px;">
                    <img src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png" alt="" style="width: 8px;height: 16px;" v-if="listData.pvDayBeforeType==1">
                    <p class="dataStatistics3_3_3">2人</p>
                    <p class="dataStatistics3_3_4">2人</p>
                  </div>
                </div> -->
        </div>
        <div class="dataStatistics1">
          <img src="http://cdn.xiaodingdang1.com/2024/04/15/51eb6afacb224a78be5d101eb1d48e92png" alt=""
            style="width: 60px;height: 60px;">
          <p class="dataStatistics2">{{ listData.communityStayTimeStr }}</p>
          <p class="dataStatistics3">社区停留时间</p>
          <!-- <div class="dataStatistics3_3">
                  <p class="dataStatistics3_3_1">较前一日</p>
                  <div class="dataStatistics3_3_2">
                    <img src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png" alt="" style="width: 8px;height: 16px;">
                    <img src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png" alt="" style="width: 8px;height: 16px;" v-if="listData.pvDayBeforeType==1">
                    <p class="dataStatistics3_3_3">2人</p>
                    <p class="dataStatistics3_3_4">2人</p>
                  </div>
                </div> -->
        </div>
        <div class="dataStatistics1">
          <img src="http://cdn.xiaodingdang1.com/2024/04/15/4366d813f2224718a7531dfc37a65258png" alt=""
            style="width: 60px;height: 60px;">
          <p class="dataStatistics2">{{ listData.mealStayTimeStr }}</p>
          <p class="dataStatistics3">膳食停留时间</p>
          <!-- <div class="dataStatistics3_3">
                  <p class="dataStatistics3_3_1">较前一日</p>
                  <div class="dataStatistics3_3_2">
                    <img src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png" alt="" style="width: 8px;height: 16px;">
                    <img src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png" alt="" style="width: 8px;height: 16px;" v-if="listData.pvDayBeforeType==1">
                    <p class="dataStatistics3_3_3">2人</p>
                    <p class="dataStatistics3_3_4">2人</p>
                  </div>
                </div> -->
        </div>
        <div class="dataStatistics1">
          <img src="http://cdn.xiaodingdang1.com/2024/04/15/fcae5348ae1f453bb4e5bb0f377cb3capng" alt=""
            style="width: 60px;height: 60px;">
          <p class="dataStatistics2">{{ listData.recoveryStayTimeStr }}</p>
          <p class="dataStatistics3">产康停留时间</p>
          <!-- <div class="dataStatistics3_3">
                  <p class="dataStatistics3_3_1">较前一日</p>
                  <div class="dataStatistics3_3_2">
                    <img src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png" alt="" style="width: 8px;height: 16px;">
                    <img src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png" alt="" style="width: 8px;height: 16px;" v-if="listData.pvDayBeforeType==1">
                    <p class="dataStatistics3_3_3">2人</p>
                    <p class="dataStatistics3_3_4">2人</p>
                  </div>
                </div> -->
        </div>
      </div>
      <div class="information">
        <div class="information1">
          <div id="chartLineBox" style="width:100%;height:349px"> </div>
        </div>
        <div class="information2">
          <div class="information2_1">
            <img src="http://cdn.xiaodingdang1.com/2024/04/15/91e7504561c94f55af7cbc30f25108b6png" alt=""
              style="width: 60px;height: 60px;">
            <p class="information2_2">{{ listData.reviewStayTimeStr }}</p>
            <p class="information2_3">评价停留时间</p>
            <!-- <div class="information2_4">
                  <p class="information2_5">较前一日</p>
                  <div class="information2_6">
                    <img src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png" alt="" style="width: 8px;height: 16px;">
                    <img src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png" alt="" style="width: 8px;height: 16px;" v-if="listData.pvDayBeforeType==1">
                    <p class="dataStatistics3_3_3">2人</p>
                    <p class="dataStatistics3_3_4">2人</p>
                  </div>
                </div> -->
          </div>
          <div class="information2_1" id="information2_1">
            <img src="http://cdn.xiaodingdang1.com/2024/04/15/1d81081bc43e4cd7831907ab7b9cbe73png" alt=""
              style="width: 60px;height: 60px;">
            <p class="information2_2">{{ listData.staffStayTimeStr }}</p>
            <p class="information2_3">护理人员停留时间</p>
            <!-- <div class="information2_4">
                  <p class="information2_5">较前一日</p>
                  <div class="information2_6">
                    <img src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png" alt="" style="width: 8px;height: 16px;">
                    <img src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png" alt="" style="width: 8px;height: 16px;" v-if="listData.pvDayBeforeType==1">
                    <p class="dataStatistics3_3_3">2人</p>
                    <p class="dataStatistics3_3_4">2人</p>
                  </div>
                </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { detailVisit } from "@/api/platform/index";
import * as echarts from 'echarts'
export default {
  name: "Index",
  data() {
    return {
      row: '',
      eventTime: [],
      pv: [],
      listData: '',
      userId: '',
      activeName: '1',
      aa: 2,
      // 版本号
      version: "3.8.7",
      type: this.$route.query.type
    };
  },
  created() {
    this.userId = this.$route.query.userId
    //this.row = JSON.parse(this.$route.query.row)
    this.detailVisit()
  },
  methods: {
    handleClick(e) {
      console.log(this.activeName);
    }, goTarget(href) {
      window.open(href, "_blank");
    },
    detailVisit() {//查询
      let data = {
        userId: this.userId
      }
      detailVisit(data).then(res => {
        if (res.code == 200) {
          this.listData = res.data
          // this.$message(res.msg);
          let eventTime = []
          let pv = []
          res.data.viewsCountStats.forEach(item => {
            eventTime.push(item.eventTime)
            pv.push(item.pv)
          });
          this.eventTime = eventTime
          this.pv = pv
          this.getchartLineBox()
          return
        } else {
          this.$message(res.msg);
        }
      });
    },
    getchartLineBox() {
      this.chartLine = echarts.init(document.getElementById('chartLineBox'));
      // 指定图表的配置项和数据
      var option = {
        tooltip: {              //设置tip提示
          trigger: 'axis'
        },
        color: ['#3058FF', '#3F85FF'],       //设置区分（每条线是什么颜色，和 legend 一一对应）
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.eventTime
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: this.pv,
            type: 'line',
            areaStyle: {},
            itemStyle: {
              normal: { //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0.2, color: '#3F85FF' // 0% 处的颜色
                }, {
                  offset: 0.3, color: '#3F85FF' // 100% 处的颜色
                }, {
                  offset: 1, color: '#fff' // 100% 处的颜色
                }]
                ), //背景渐变色    
                lineStyle: {        // 系列级个性化折线样式  
                  width: 2,
                  type: 'solid',
                  color: "#3F85FF" //折线的颜色
                }
              },
            },
            // 设置折线弧度，取值：0-1之间
            smooth: 0.5,
          }
        ]
      };

      // 使用刚指定的配置项和数据显示图表。
      this.chartLine.setOption(option);
    }
  },
  mounted() {

  },
};
</script>

<style scoped lang="scss">
#app {
  background: #F0F1F5;
  padding-bottom: 40px;
  padding: 20px 20px;
}

.dataDisplay {
  display: flex;
  justify-content: space-between;
  line-height: 0;

  #dataDisplay1 {
    background: #EBEBEB;
  }

  .dataDisplay1 {
    background-image: linear-gradient(#3F85FF, #0054E8);
    border-radius: 10px;
    width: 32%;
    padding: 24px 24px;

    .dataDisplay1_1 {
      color: #FFFFFF;
      font-size: 18px;
    }

    .evaluationManagement3 {
      display: flex;
      align-items: center;

      .evaluationManagement3_1 {
        margin-left: 20px;

        .evaluationManagement3_2 {
          font-size: 14px;
          color: #FFFFFF;
        }

        .evaluationManagement3_3 {
          line-height: 0;
          font-size: 30px;
          color: #FFFFFF;
          font-weight: bold;
          padding-top: 4px;
        }
      }
    }
  }
}

.dataStatistics {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;

  .dataStatistics1 {
    line-height: 0;
    width: 24%;
    background: #fff;
    padding: 20px 20px;
    border-radius: 10px;
    text-align: center;

    .dataStatistics3 {
      color: #17191A;
      font-size: 14px;
      margin-top: 12px;
    }

    .dataStatistics2 {
      color: #17191A;
      font-size: 30px;
      font-weight: bold;
      margin-top: 16px;
    }

    .dataStatistics3_3 {
      display: flex;
      line-height: 0;
      align-items: center;
      justify-content: center;

      .dataStatistics3_3_1 {
        color: #7C7D81;
        font-size: 14px;
      }

      .dataStatistics3_3_2 {
        display: flex;
        margin-left: 22px;
        align-items: center;

        .dataStatistics3_3_3 {
          color: #F84343;
          font-size: 14px;
          margin-left: 8px;
        }

        .dataStatistics3_3_4 {
          color: #0BBD71;
          font-size: 14px;
          margin-left: 8px;
        }

        #dataStatistics3_3_3 {
          color: #0BBD71;
          font-size: 14px;
          margin-left: 8px;
        }
      }
    }
  }

}

.information {
  margin-top: 20px;
  border-radius: 10px;
  display: flex;
  justify-content: space-between;

  .information1 {
    width: 75%;
    background: #fff;
    border-radius: 10px;
  }

  .information2 {
    width: 24%;
    border-radius: 10px;
    line-height: 0;

    #information2_1 {
      margin-top: 20px;
    }

    .information2_1 {
      background: #fff;
      text-align: center;
      padding: 16px 0;
      border-radius: 10px;

      .information2_2 {
        color: #17191A;
        font-size: 30px;
        font-weight: bold;
        margin-top: 13px;
      }

      .information2_3 {
        color: #17191A;
        font-size: 14px;
        margin-top: 12px;
      }

      .information2_4 {
        display: flex;
        align-items: center;
        justify-content: center;

        .information2_6 {
          display: flex;
          align-items: center;
          margin-left: 17px;

          .information2_7 {
            margin-left: 10px;
          }

          .dataStatistics3_3_3 {
            color: #F84343;
            font-size: 14px;
            margin-left: 8px;
          }

          .dataStatistics3_3_4 {
            color: #0BBD71;
            font-size: 14px;
            margin-left: 8px;
          }
        }
      }
    }
  }

}

.table {
  margin-top: 20px;
  background: #fff;
  padding: 20px 20px;
  border-radius: 10px;
  width: 100%;
}

.block {
  text-align: right;
  margin-top: 20px;
}

.tab {
  background: #fff;
  padding: 20px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.userDetails {
  background: #fff;
  border-radius: 10px;
  padding: 20px 20px;

  .userDetailsTitle {
    font-size: 20px;
    color: #17191A;
  }

  .fgx {
    height: 2px;
    width: 100%;
    background: #EBEBEB;
  }

  .userDetails2 {
    display: flex;
    justify-content: space-between;
    width: 100%;

    .userDetails1 {
      width: 50%;
      display: flex;
      font-size: 14px;

      .userDetails1_1 {
        width: 50%;
        color: #A9AAAE;
      }

      .userDetails1_2 {
        width: 50%;
        color: #17191A;
      }
    }
  }

}
</style>