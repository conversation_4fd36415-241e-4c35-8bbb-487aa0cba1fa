<template>
    <div id="app">
      <!-- <div class="head">
              <div class="titles">护理人员配置</div>
              <div class="title1">Pages/护理人员配置</div>
          </div> -->
     
      <div class="app">
          <div class="titleName"> 
            基本信息
      </div>
      <div class="rule"></div>
      <div class="content">
        <div class="content1">
                  <div class="title5"><p class="required">*</p><p>周期</p></div>
                  <div>
                    <div>
                      <el-select
              v-model="ruleForm.gestationWeek"
              placeholder="请选择周期"
              clearable
              class="textarea"
              :disabled="forbidden"
            >
              <el-option
                v-for="item in postList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
                    </div>
                  </div>
                  </div>
        <div class="content1">
                  <div class="title5"><p class="required">*</p><p>特别关注</p></div>
                  <div>
                      <el-input type="text" v-model="ruleForm.specialAttention" class="textarea" placeholder="请输入特别关注" :disabled="forbidden"></el-input> 
                  </div>
                  </div>
                  <div class="content1">
                  <div class="title5"><p class="required">*</p><p>宝宝照片</p></div>
                  <div class="upload">
          <image-upload :limit="1" :isShowTip="false" v-model="ruleForm.babyImage" v-if="!forbidden"/>
          <div v-if="forbidden">
                    <img :src="item" alt="" v-for="item,index in ruleForm.babyImage" :key="index" style="width: 146px;height: 146px;margin-right: 10px;    border: 1px solid #c0ccda;
    border-radius: 6px;">
                    </div>
         </div>
                  </div>

      </div>
      <div class="titleName"> 
        宝宝情况
      </div>
      <div class="rule"></div>
      <div class="content">
        <div class="content1">
                  <div class="title5"><p class="required">*</p><p>宝宝情况图1</p></div>
                  <div>
                      <el-input type="text" v-model="ruleForm.babyDevelopmentLabel1" class="textarea" placeholder="请输入宝宝情况图1" :disabled="forbidden"></el-input> 
                  </div>
                  </div>
                  <div class="BackgroundPicture">
          <div class="title5"><p class="required"></p><p></p></div>
         <div class="upload">
          <image-upload :limit="1" :isShowTip="false" v-model="ruleForm.babyDevelopmentImage1" v-if="!forbidden"/>
          <div v-if="forbidden">
                    <img :src="item" alt="" v-for="item,index in ruleForm.babyDevelopmentImage1" :key="index" style="width: 146px;height: 146px;margin-right: 10px;    border: 1px solid #c0ccda;
    border-radius: 6px;">
                    </div>
         </div>
      </div>
      <div class="content1">
                  <div class="title5"><p class="required">*</p><p>宝宝情况图2</p></div>
                  <div>
                      <el-input type="text" v-model="ruleForm.babyDevelopmentLabel2" class="textarea" placeholder="请输入宝宝情况图2" :disabled="forbidden"></el-input> 
                  </div>
                  </div>
                  <div class="BackgroundPicture">
          <div class="title5"><p class="required"></p><p></p></div>
         <div class="upload">
          <image-upload :limit="1" :isShowTip="false" v-model="ruleForm.babyDevelopmentImage2" v-if="!forbidden"/>
          <div v-if="forbidden">
                    <img :src="item" alt="" v-for="item,index in ruleForm.babyDevelopmentImage2" :key="index" style="width: 146px;height: 146px;margin-right: 10px;    border: 1px solid #c0ccda;
    border-radius: 6px;">
                    </div>
         </div>
      </div>
      <div class="content1">
                <div class="title5"><p class="required">*</p><p>宝宝情况详情</p></div>
                <el-input type="textarea" class="textarea" v-model="ruleForm.babyDevelopmentDetail" placeholder="请输入宝宝情况详情"  :disabled="forbidden"></el-input>
            </div>
   
            </div>
            <div class="titleName"> 
                注意事项
      </div>
      <div class="rule"></div>
      <div class="content">
      <div class="content1">
                  <div class="title5"><p class="required">*</p><p>注意事项1</p></div>
                  <div>
                      <el-input type="text" v-model="ruleForm.attentionItem1" class="textarea" placeholder="请输入注意事项1" :disabled="forbidden"></el-input> 
                  </div>
                  </div>
                  <div class="content1">
                <div class="title5"><p class="required">*</p><p>注意事项1详情</p></div>
                <el-input type="textarea" v-model="ruleForm.attentionDesc1" class="textarea" placeholder="请输入注意事项1详情"  :disabled="forbidden"></el-input>
            </div>
            <div class="content1">
                  <div class="title5"><p class="required">*</p><p>注意事项2</p></div>
                  <div>
                      <el-input type="text" v-model="ruleForm.attentionItem2" class="textarea" placeholder="请输入注意事项2" :disabled="forbidden"></el-input> 
                  </div>
                  </div>
                  <div class="content1">
                <div class="title5"><p class="required">*</p><p>注意事项2详情</p></div>
                <el-input type="textarea" v-model="ruleForm.attentionDesc2" class="textarea" placeholder="请输入注意事项2详情"  :disabled="forbidden"></el-input>
            </div>
      </div>
      <div class="titleName"> 
        饮食建议
      </div>
      <div class="rule"></div>
      <div class="content">
        <div class="BackgroundPicture">
          <div class="title5"><p class="required">*</p><p></p>食物图1</div>
         <div class="upload">
          <image-upload :limit="1" :isShowTip="false" v-model="ruleForm.foodImage1" v-if="!forbidden"/>
          <div v-if="forbidden">
                    <img :src="item" alt="" v-for="item,index in ruleForm.foodImage1" :key="index" style="width: 146px;height: 146px;margin-right: 10px;    border: 1px solid #c0ccda;
    border-radius: 6px;">
                    </div>
         </div>
      </div>
      <div class="BackgroundPicture">
          <div class="title5"><p class="required">*</p><p></p>食物图2</div>
         <div class="upload">
          <image-upload :limit="1" :isShowTip="false" v-model="ruleForm.foodImage2" v-if="!forbidden"/>
          <div v-if="forbidden">
                    <img :src="item" alt="" v-for="item,index in ruleForm.foodImage2" :key="index" style="width: 146px;height: 146px;margin-right: 10px;    border: 1px solid #c0ccda;
    border-radius: 6px;">
                    </div>
         </div>
      </div>
      <div class="content1">
                  <div class="title5"><p class="required">*</p><p>食物名称1</p></div>
                  <div>
                      <el-input type="text" v-model="ruleForm.foodName1" class="textarea" placeholder="请输入食物名称1" :disabled="forbidden"></el-input> 
                  </div>
                  </div>
                  <div class="content1">
                <div class="title5"><p class="required">*</p><p>食物描述1</p></div>
                <el-input type="textarea" v-model="ruleForm.foodDescription1" class="textarea" placeholder="请输入食物描述1"  :disabled="forbidden"></el-input>
            </div>
            <div class="content1">
                  <div class="title5"><p class="required">*</p><p>食物名称2</p></div>
                  <div>
                      <el-input type="text" v-model="ruleForm.foodName2" class="textarea" placeholder="请输入食物名称2" :disabled="forbidden"></el-input> 
                  </div>
                  </div>
                  <div class="content1">
                <div class="title5"><p class="required">*</p><p>食物描述2</p></div>
                <el-input type="textarea" v-model="ruleForm.foodDescription2" class="textarea" placeholder="请输入食物描述2"  :disabled="forbidden"></el-input>
            </div>
    </div>
    <div class="titleName"> 
        能不能做
      </div>
      <div class="rule"></div>
      <div class="content">
        <div class="content1">
                <div class="title5"><p class="required">*</p><p>可以做</p></div>
                <el-input type="textarea" v-model="ruleForm.canDoActivities" class="textarea" placeholder="请输入可以做的详情"  :disabled="forbidden"></el-input>
            </div>
            <div class="content1">
                <div class="title5"><p class="required">*</p><p>不可以做</p></div>
                <el-input type="textarea" v-model="ruleForm.cannotDoActivities" class="textarea" placeholder="请输入不可以做的详情"  :disabled="forbidden"></el-input>
            </div>
        </div>
        </div>
      <div class="btn">
      <el-button type="primary" @click="confirm">提交</el-button>
  <el-button @click="cancel">取消</el-button>
  </div>
    <!--驳回-->
<el-dialog
  title="驳回原因"
  :visible.sync="dialogVisibleReject"
  width="30%">
  <el-input type="textarea" v-model="rejectionReason" placeholder="请输入驳回原因" ></el-input>
  <span slot="footer" class="dialog-footer">
    <el-button @click="dialogVisibleReject = false">取 消</el-button>
    <el-button type="primary" @click="rejectConfirm">确 定</el-button>
  </span>
</el-dialog>
    </div>
  </template>
  
  <script>
  import { save,update,info } from "@/api/platform/pregnancyAssistant";
  import ImageUpload from "@/components/ImageUpload/index"
  export default {
      name: "app",
      components: {ImageUpload},
    data() {
      return {
        trackerId:'',
        postList:42,
        staffPostId:'',//1'孕产厨师'2'孕产医师'3'孕产护士'4'孕产月嫂'5'孕产育婴员'6'孕产心理咨询师'7孕产产后康复师'8孕产健康管理师'9'孕产母婴护理师'
        audit:'',
      auditId:'',
      forbidden:false,
auditInfoList:[],
      dialogVisibleReject:false,
 rejectionReason:'',
        startTime:'',
        endTime:'',
        text:4,
          ruleForm:{
            babyImage:[],//宝宝照片
            gestationWeek:'',//孕期周数（1-42）
            specialAttention:'',//特别关注
            babyDevelopmentImage1:[],//宝宝情况图片1 URL
            babyDevelopmentLabel1:'',//宝宝情况1标题
            babyDevelopmentImage2:[],//宝宝情况图片2 URL
            babyDevelopmentLabel2:'',//宝宝情况2标题
            babyDevelopmentDetail:'',//宝宝情况详情
            attentionItem1:'',//注意事项1
            attentionDesc1:'',//注意事项1描述
            attentionItem2:'',//注意事项2
            attentionDesc2:'',//注意事项2描述
            foodImage1:[],//食物图1 URL
            foodImage2:[],//食物图2 URL
            foodName1:'',//食物名称1
            foodDescription1:'',//食物描述1
            foodName2:'',//食物名称2
            foodDescription2:'',//食物描述2
            canDoActivities:'',//能做的事
            cannotDoActivities:'',//不能做的事
      }
    }
  },
  created(){
  //  this.ruleForm.staffPost=this.$route.query.staffPost
   this.staffPostId=this.$route.query.staffPostId
   this.trackerId=this.$route.query.trackerId
   let audit=this.$route.query.audit
    this.audit=audit
   this.forbidden=this.$route.query.forbidden?true:false
   this.auditId=this.$route.query.auditId
   if(audit){
    this.auditInfo(this.auditId)
   }
   if(this.trackerId){
    this.info(this.trackerId)
   }
    },
  methods:{
    cancel(){
            this.$router.go(-1);
        },
    rejectConfirm(){//驳回确定
      if(this.rejectionReason==''){
        this.$message('请填写驳回理由');
                return
      }
      this.dialogVisibleReject=false
      let data={
        auditId:this.auditId,
        auditResult:false,
        rejectionReason:this.rejectionReason
      }
      audit(data).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.$router.go(-1);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
    },
    reject(){//驳回
     this.dialogVisibleReject=true
    },
    pass(){//通过
      this.$confirm('是否审核通过?', '审核通过', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // this.$message({
          //   type: 'success',
          //   message: '审核通过!'
          // });
          let data={
        auditId:this.auditId,
        auditResult:true,
      }
      audit(data).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.$router.go(-1);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消审核'
          });          
        });
    },



  auditInfo(auditId){//查询审核信息
      auditInfo(auditId).then(res => {
                if(res.code==200){
                  this.auditInfoList=res.data
                    this.$message(res.msg);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
        },



      handleRemove(file, fileList) {
          console.log(file, fileList);
        },
        handlePictureCardPreview(file) {
          this.dialogImageUrl = file.url;
          this.dialogVisible = true;
        },
        confirm() {//确定
            let data = this.ruleForm
            if (data.gestationWeek == '') {
                this.$message('请选择孕期周数');
                return
            }
            if (data.specialAttention=='') {
                this.$message('请输入特别关注');
                return
            }
            if (data.babyImage.length==0) {
                this.$message('请上传宝宝照片');
                return
            }
            if (data.babyDevelopmentImage1.length==0) {
                this.$message('请上传宝宝情况图片1');
                return
            }
            if (data.babyDevelopmentLabel1=='') {
                this.$message('请填写宝宝情况1标题');
                return
            }
            if (data.babyDevelopmentImage2.length==0) {
                this.$message('请上传宝宝情况图片2 URL');
                return
            }
            if (data.babyDevelopmentLabel2 == '') {
                this.$message('请输入宝宝情况2标题');
                return
            }
            if (data.babyDevelopmentDetail == '') {
                this.$message('请填写宝宝情况详情');
                return
            }
            if (data.attentionItem1 == '') {
                this.$message('请输入注意事项1');
                return
            }
            if (data.attentionDesc1=='') {
                this.$message('请填写注意事项1描述');
                return
            }
            if (data.attentionItem2=='') {
                this.$message('请填写注意事项2');
                return
            }
            if (data.attentionDesc2=='') {
                this.$message('请填写注意事项2描述');
                return
            }
            if (data.foodImage1.length==0) {
                this.$message('请上传食物图1');
                return
            }

            if (data.foodImage2.length==0) {
                this.$message('请上传食物图2');
                return
            }
            if (data.foodName1=='') {
                this.$message('请填写食物名称1');
                return
            }
            if (data.foodDescription1=='') {
                this.$message('请填写食物描述1');
                return
            }
            if (data.foodName2=='') {
                this.$message('请填写食物名称2');
                return
            }
            if (data.foodDescription2=='') {
                this.$message('请填写食物描述2');
                return
            }
            if (data.canDoActivities=='') {
                this.$message('请填写能做的事');
                return
            }
            if (data.cannotDoActivities=='') {
                this.$message('请填写不能做的事');
                return
            }
            if(data.babyImage.length==1){
                data.babyImage=data.babyImage[0]
              }
              if(data.babyDevelopmentImage1.length==1){
                data.babyDevelopmentImage1=data.babyDevelopmentImage1[0]
              }
              if(data.babyDevelopmentImage2.length==1){
                data.babyDevelopmentImage2=data.babyDevelopmentImage2[0]
              }
              if(data.foodImage1.length==1){
                data.foodImage1=data.foodImage1[0]
              }
              if(data.foodImage2.length==1){
                data.foodImage2=data.foodImage2[0]
              }
            if (!this.trackerId) {
                save(this.ruleForm).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.$router.go(-1);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
            }
            if (this.trackerId) { 
            update(this.ruleForm).then(res => { 
                console.log(res);
                if(res.code==200){
                  this.$message(res.msg);
                    this.$router.go(-1);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
        }
        },
        info(trackerId){//查询产后康复信息
            info(trackerId).then(res => {
                if(res.code==200){
                    this.ruleForm=res.data
                    this.$message(res.msg);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
        },
  }
  }
  </script>
  
  <style scoped lang="scss">
  .head{
      width: 100%;
      height: 80px;
      background: #ffff;
      color: #17191A;
      padding: 15px 20px;
      .titles{
          font-size: 22px;
          font-weight: bold;
      }
      .title1{
          font-size: 14px;
      }
  }
  .titleName{
      padding: 24px 0 10px 24px; 
      font-size: 20px;
      color: #17191A;
   }
   .rule{
      width: 98%;
      height: 2px;
      background: #EBEBEB;
      margin: 0 auto;
  }
  .app{
      background: #ffff;
      margin: 10px 10px;
      border-radius: 5px;
   }
   .BackgroundPicture{
      display: flex;
      margin-top: 20px;
      .uploadTitle{
          display: flex;
          color: #17191A;
          font-size: 14px;
          width: 100px;
          justify-content: flex-end;
          padding: 0 12px 0 0;
      }
  }
  .hint{
      display: flex;
      color: #45464A;
      font-size: 14px;
      margin-top: 8px;    
      line-height: 0;
      .hints{
          display: flex;
          align-items: center;
          .hints1{
              color:#FF6C11 ;
          }
      }
  }
  .title5{
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #606266;
      box-sizing: border-box;
      font-weight: bold;
      width:10%;
      .required{
          color: red;
      }
     }
     .content{
      margin-left: 100px;
     }
     .content1{
      display: flex;
      align-items: center;
      margin: 20px 0 10px 0;
      .textarea{
          width: 368px;
      }
     }
     .btn{
      margin: 20px auto;
      text-align: center;
  }
  .sel{
    display: flex;
    align-items: center;
    margin-right: 10px;
    .selTitle{
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
}
#cars{
    border: 1px solid #DCDCDC;
    border-radius: 3px;
    color: #7C7D81;
    font-size: 14px;
    width: 129px;
    height: 32px;
    margin-left: 6px;
}
}
.roomMessage{
    display: flex;
}

.el-upload--picture-card{
                    width: 100px;
                    height: 100px;
                }
               .el-upload{
                    width: 100px;
                    height: 100px;
                    line-height: 100px;
                }
                .el-upload-list--picture-card .el-upload-list__item{
                    width: 100px;
                    height: 100px;
                    line-height: 100px;
                }
                .el-upload-list--picture-card .el-upload-list__item-thumbnail{
                    width: 100px;
                    height: 100px;
                    line-height: 100px;
                }
              .avatar{
                    width: 100px;
                    height: 100px;
                }
                .auditContent{
  width: 100%;
  padding: 20px 24px;
  background: #FF6C11;
  border-radius: 6px;
  font-size: 14px;
  line-height: 14px;
}
.pass{
  display: flex;
  align-items: center;
  color: #000000;
}
.passCause{
  color:#000000;
  margin-left:20px
}
  </style>