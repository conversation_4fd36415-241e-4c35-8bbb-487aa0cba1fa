<template>
  <div id="app">
    <!-- <div class="head">
            <div class="titles">月子膳食配置</div>
            <div class="title1">Pages/基础配置/月子膳食配置</div>
        </div> -->

    <div class="app">
      <div class="auditContent" v-if="auditInfoList.auditStatus == 2">
        <div class="pass">
          <img
            src="http://cdn.xiaodingdang1.com/icon%402x.png"
            alt=""
            style="width: 20px; height: 20px"
          />
          <p>驳回原因</p>
        </div>
        <p class="passCause">{{ auditInfoList.rejectionReason }}</p>
      </div>
      <!-- <div class="titleName">基础信息</div>
      <div class="rule"></div> -->
      <div class="elTabs">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="膳食信息" name="first"></el-tab-pane>
          <el-tab-pane label="膳食科普" name="second"></el-tab-pane>
        </el-tabs>
      </div>
      <div v-if="activeName == 'second'">
        <div class="content">
          <div class="employ">
            <div class="employ_1">是否使用</div>
            <el-checkbox
              v-model="isTags"
              class="employ_2"
              @change="checkboxChange($event)"
            ></el-checkbox>
          </div>
          <div class="BackgroundPicture">
            <div class="title5">
              <p class="required">*</p>
              <p>照片</p>
            </div>
            <div class="upload">
              <image-upload
                :width="350"
                :width1="2100"
                :height="170"
                :height1="1020"
                :limit="6"
                :isShowTip="false"
                v-model="mealTags"
              />
            </div>
          </div>
        </div>
      </div>
      <div v-if="activeName == 'first'">
        <div class="content">
          <div class="BackgroundPicture">
            <div class="title5">
              <p class="required">*</p>
              <p>照片</p>
            </div>
            <div class="upload">
              <image-upload
                :width="350"
                :width1="2100"
                :height="170"
                :height1="1020"
                :limit="6"
                :isShowTip="false"
                v-model="ruleForm.mealPhotos"
                v-if="!forbidden"
              />
              <div v-if="forbidden">
                <img
                  :src="item"
                  alt=""
                  v-for="(item, index) in ruleForm.mealPhotos"
                  :key="index"
                  style="
                    width: 146px;
                    height: 146px;
                    margin-right: 10px;
                    border: 1px solid #c0ccda;
                    border-radius: 6px;
                  "
                />
              </div>
            </div>
          </div>
          <div class="hint">
            <div class="title5"></div>
            <div class="hints">
              <img
                src="http://cdn.xiaodingdang1.com/info-circle-filled.png"
                alt=""
              />
              <p>照片尺寸：高度：宽度（１：２）照片数量1～6张</p>
            </div>
          </div>
          <div class="BackgroundPicture">
            <div class="title5"><p>视频</p></div>
            <div class="upload">
              <file-upload
                :limit="1"
                :isShowTip="false"
                v-model="ruleForm.videos"
                v-if="!forbidden"
              />
              <div v-if="forbidden">
                <video
                  controls="controls"
                  :src="item"
                  alt=""
                  v-for="(item, index) in ruleForm.videos"
                  :key="index"
                  style="
                    width: 146px;
                    height: 146px;
                    margin-right: 10px;
                    border: 1px solid #c0ccda;
                    border-radius: 6px;
                  "
                ></video>
              </div>
            </div>
          </div>
          <div class="content1">
            <div class="title5">
              <p class="required"></p>
              <p>签约礼</p>
            </div>
            <div>
              <el-select
                v-model="ruleForm.contractGiftId"
                placeholder="请选择签约礼"
                clearable
                style="width: 240px"
                :disabled="forbidden"
              >
                <el-option
                  v-for="item in giftList"
                  :key="item.contractGiftId"
                  :label="item.description"
                  :value="item.contractGiftId"
                />
              </el-select>
            </div>
          </div>
          <div class="content1">
            <div class="title5">
              <p class="required">*</p>
              <p>月子餐简介</p>
            </div>
            <div>
              <el-input
                type="textarea"
                v-model="ruleForm.mealDescription"
                class="textarea"
                :disabled="forbidden"
              ></el-input>
            </div>
          </div>
          <!-- <div class="hint">
        <div class="title5"></div>
        <div class="hints">
            <img src="http://cdn.xiaodingdang1.com/info-circle-filled.png" alt="">
            <p>
                照片尺寸：高度：宽度（１：２）照片数量1～6张
            </p>
        </div>
    </div> -->
          <div class="hint">
            <div class="title5"></div>
            <div class="hints">
              月子餐简介字符长度为：20～100 不可出现下列词汇:
              <span class="hints1"
                >权威、顶级、独家、立竿见影、国家级、公益、食疗、药膳、100%、慈善、祈福、解释权、唯一，</span
              >请填写时注意
            </div>
          </div>
        </div>
        <div class="titleName">菜品信息</div>
        <div class="content">
          <div class="cuisineTab">
            <div class="cuisineTab_1">
              <p
                :class="tabIndex == index ? 'cuisineTab_2' : 'cuisineTab_3'"
                v-for="(item, index) in listTab"
                :key="index"
                @click="tabClick(index)"
              >
                {{ item }}
              </p>
            </div>
            <div class="cuisineTab_4" @click="cuisineAdd(tabIndex)">新增</div>
          </div>
          <div class="cuisineTable">
            <el-table :data="listData" class="table" v-if="tabIndex == 0">
              <el-table-column
                label="图片"
                prop="name"
                width="120"
                align="center"
              >
                <template slot-scope="scope">
                  <img
                    :src="scope.row.photos[0]"
                    min-width="70"
                    height="70"
                    style="width: 100px; height: 100px; border-radius: 5px"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="菜品名称"
                prop="dishName"
                align="center"
              />
              <el-table-column label="套餐功效" prop="effect" align="center" />
              <el-table-column
                label="是否推荐"
                prop="isTags"
                width="150"
                align="center"
              >
                <template slot-scope="scope">
                  <el-switch
                    v-model="scope.row.isRec"
                    :active-value="true"
                    :inactive-value="false"
                    @change="isTagsClick(scope.row.itemId)"
                  >
                  </el-switch>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button size="mini" type="text" @click="examine(scope.row)"
                    >查看</el-button
                  >
                  <el-button size="mini" type="text" @click="compile(scope.row)"
                    >编辑</el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleDeletes(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <el-table :data="listData" class="table" v-if="tabIndex == 1">
              <el-table-column label="图片" prop="name" align="center">
                <template slot-scope="scope">
                  <img
                    :src="scope.row.productPhotoUrl[0]"
                    min-width="70"
                    height="70"
                    style="width: 100px; height: 100px; border-radius: 5px"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="菜品名称"
                prop="productName"
                align="center"
              />
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope" v-if="scope.row.roleId !== 1">
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleUpdate(scope.row)"
                    >编辑</el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleDelete(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <div class="block" v-if="tabIndex == 1">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
              >
              </el-pagination>
            </div>
          </div>
        </div>

        <!-- <div class="rule"></div> -->
        <!-- <div
        class="content"
        v-for="(item, index) in ruleForm.mealItems"
        :key="index"
      >
        <div class="BackgroundPicture">
          <div class="title5" v-if="index == 0">
            <p class="required">*</p>
            <p>早餐</p>
          </div>
          <div class="title5" v-if="index == 4">
            <p class="required">*</p>
            <p>午餐</p>
          </div>
          <div class="title5" v-if="index == 8">
            <p class="required">*</p>
            <p>晚餐</p>
          </div>
          <div class="title5" v-if="index == 12">
            <p class="required">*</p>
            <p>茶点</p>
          </div>
          <div
            class="title5"
            v-if="
              index == 1 ||
              index == 2 ||
              index == 3 ||
              index == 5 ||
              index == 6 ||
              index == 7 ||
              index == 9 ||
              index == 10 ||
              index == 11 ||
              index == 15 ||
              index == 13 ||
              index == 14
            "
          ></div>
          <div class="upload">
            <image-upload
              :oneAll="2"
              :limit="1"
              :isShowTip="false"
              v-model="item.photos"
              v-if="!forbidden"
            />
            <div v-if="forbidden">
              <img
                :src="item"
                alt=""
                v-for="(item, index) in item.photos"
                :key="index"
                style="
                  width: 146px;
                  height: 146px;
                  margin-right: 10px;
                  border: 1px solid #c0ccda;
                  border-radius: 6px;
                "
              />
            </div>
          </div>
          <div>
            <div class="content2">
              <div class="title6">菜品名称</div>
              <el-input
                v-model="item.dishName"
                class="input"
                placeholder="请输入菜品名称"
                :disabled="forbidden"
                maxlength="10"
                show-word-limit
              ></el-input>
              <div class="title7">0/10</div>
            </div>
            <div class="content2">
              <div class="title6">菜品简介</div>
              <el-input
                v-model="item.dishDescription"
                class="input"
                placeholder="请输入菜品介绍"
                :disabled="forbidden"
                maxlength="10"
                show-word-limit
              ></el-input>
              <div class="title7">0/10</div>
            </div>
          </div>
          <div class="viewLocations">
            <div
              class="viewLocation"
              @click="
                onPreview(
                  'http://cdn.xiaodingdang1.com/2024/05/11/9cc82d7d11954502b652def3f8f8adacpng'
                )
              "
            >
              <img
                src="http://cdn.xiaodingdang1.com/2024/05/11/9cc82d7d11954502b652def3f8f8adacpng"
                alt=""
                style="width: 180px; height: 110px"
              />
              <div class="viewLocation1">查看位置</div>
            </div>
          </div>
        </div> -->
        <!-- <div class="content1">
                <div class="title5"></div>
                <div>
                    <el-button @click="addCuisine(item.category)" :disabled="forbidden">增加菜品<i class="el-icon-plus"></i></el-button>
                </div>
                </div> -->
        <!-- <div class="hint">
        <div class="title5"></div>
        <div class="hints">
            <img src="http://cdn.xiaodingdang1.com/info-circle-filled.png" alt="">
            <p>
                至少添加四种菜品
            </p>
        </div>
    </div> -->
        <!-- </div> -->
        <div class="titleName">其他信息</div>
        <div class="rule"></div>
        <div class="content">
          <div class="BackgroundPicture">
            <div class="title5">
              <p class="required">*</p>
              <p>资历证书</p>
            </div>
            <div class="upload">
              <image-upload
                :width="330"
                :width1="1980"
                :height="225"
                :height1="1350"
                :limit="8"
                :isShowTip="false"
                v-model="ruleForm.certificateUrl"
                v-if="!forbidden"
              />
              <div v-if="forbidden">
                <img
                  :src="item"
                  alt=""
                  v-for="(item, index) in ruleForm.certificateUrl"
                  :key="index"
                  style="
                    width: 146px;
                    height: 146px;
                    margin-right: 10px;
                    border: 1px solid #c0ccda;
                    border-radius: 6px;
                  "
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="btn" v-if="!audit">
        <el-button type="primary" @click="confirm" v-if="activeName == 'first'"
          >确定</el-button
        >
        <el-button
          type="primary"
          @click="confirm1"
          v-if="activeName == 'second'"
          >确定</el-button
        >
        <!-- <el-button>取消</el-button> -->
      </div>
      <div class="btn" v-if="auditInfoList.auditStatus == 0">
        <el-button type="primary" @click="pass">通过</el-button>
        <el-button @click="reject">驳回</el-button>
      </div>
      <div class="btn">
        <el-button type="success" v-if="auditInfoList.auditStatus == 1"
          >已通过</el-button
        >
        <el-button type="warning" v-if="auditInfoList.auditStatus == 2"
          >已驳回</el-button
        >
      </div>
    </div>
    <el-image-viewer
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="[url]"
    />
    <!--驳回-->
    <el-dialog title="驳回原因" :visible.sync="dialogVisibleReject" width="30%">
      <el-input
        type="textarea"
        v-model="rejectionReason"
        placeholder="请输入驳回原因"
      ></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleReject = false">取 消</el-button>
        <el-button type="primary" @click="rejectConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <!--新增热门餐饮-->
    <!--新增-->
    <el-dialog title="新增配送" :visible.sync="addShow" width="30%">
      <!-- <el-form :model="from" :rules="rules" ref="from" label-width="100px" class="demo-ruleForm"> -->
      <el-form
        :model="from"
        ref="from"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="商品照片*" style="flex: 1" prop="productPhotoUrl">
          <div style="display: flex; flex-wrap: wrap">
            <image-upload
              :oneAll="2"
              :isShowTip="false"
              v-model="from.productPhotoUrl"
            />
          </div>
        </el-form-item>
        <el-form-item label="商品视频*" style="flex: 1" prop="productPhotoUrl">
          <div style="display: flex; flex-wrap: wrap">
            <file-upload :limit="1" :isShowTip="false" v-model="from.videos" />
          </div>
        </el-form-item>
        <!-- <el-form-item label="商品描述" prop="productDescription">
  <el-input type="textarea" v-model="from.productDescription" placeholder="请输入商品描述"></el-input>
</el-form-item> -->
        <!-- <div class="BackgroundPicture" v-if="!forbidden">
       <div class="uploadTitle"><p style="color: #F84343;">*</p> <p>会所标签</p></div>
       <div class="upload">
        <el-tag
  :key="tag"
  v-for="tag in ruleForm.clubTagNames"
  closable
  :disable-transitions="false"
  @close="handleCloses(tag)">
  {{tag}}
</el-tag>
       </div>
    </div> -->
        <el-form-item label="商品标签*" style="flex: 1" prop="tag">
          <div style="display: flex; flex-wrap: wrap">
            <el-tag
              :key="tag"
              v-for="tag in from.tag"
              closable
              :disable-transitions="false"
              @close="handleCloses(tag)"
              class="tagTitle"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="" prop="name">
          <div class="label">
            <el-tag
              :key="tag"
              v-for="tag in labelList"
              closable
              :disable-transitions="false"
              @close="handleClose(tag)"
              @click="choice(tag)"
              class="tagTitle"
            >
              {{ tag }}
            </el-tag>
            <el-input
              class="input-new-tag"
              v-if="inputVisible"
              v-model="inputValue"
              placeholder="请输入"
              ref="saveTagInput"
              size="small"
              @keyup.enter.native="handleInputConfirm"
              @blur="handleInputConfirm"
            >
            </el-input>
            <el-button
              v-else
              class="button-new-tag"
              size="small"
              @click="showInput"
              style="height: 26px; line-height: 9px"
              >+ 添加标签</el-button
            >
          </div>
        </el-form-item>
        <!-- <el-form-item label="商品价格" prop="productPrice">
          <el-input
            v-model="from.productPrice"
            type="number"
            placeholder="请输入商品价格"
          ></el-input>
        </el-form-item> -->
        <el-form-item label="套餐简介" prop="productDescription">
          <el-input
            v-model="from.productDescription"
            placeholder="请输入套餐简介"
            type="textarea"
          ></el-input>
        </el-form-item>
        <el-form-item label="商品名称" prop="productName">
          <el-input
            v-model="from.productName"
            placeholder="请输入商品名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="图文详情*" style="flex: 1" prop="productDetails">
          <div style="display: flex; flex-wrap: wrap">
            <image-upload
              :oneAll="3"
              :limit="6"
              :isShowTip="false"
              v-model="from.productDetails"
            />
          </div>
          <div class="imgHint">
            <img
              src="http://cdn.xiaodingdang1.com/info-circle-filled.png"
              alt=""
              style="width: 14px; height: 14px"
            />
            <div>最多可配置六张图片</div>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addShow = false">取 消</el-button>
        <el-button type="primary" @click="submitForm('from')">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="删除" :visible.sync="deleteDialogVisible" width="30%">
      <span>是否删除此月子套餐？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
      </span>
    </el-dialog>
    <!--新增-->
    <el-dialog title="新增" :visible.sync="cuisineDialogVisible" width="30%">
      <!-- <el-form :model="from" :rules="rules" ref="from" label-width="100px" class="demo-ruleForm"> -->
      <el-form
        :model="from"
        ref="from"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="菜品名称" prop="dishName">
          <el-input
            v-model="froms.dishName"
            placeholder="请输入菜品名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="菜品功效" prop="effect">
          <el-input
            v-model="froms.effect"
            placeholder="请输入菜品功效"
          ></el-input>
        </el-form-item>
        <el-form-item label="菜品介绍" prop="dishDescription">
          <el-input
            v-model="froms.dishDescription"
            placeholder="请输入菜品介绍"
          ></el-input>
        </el-form-item>
        <el-form-item label="菜品图片*" style="flex: 1" prop="photos">
          <div style="display: flex; flex-wrap: wrap">
            <image-upload
              :oneAll="2"
              :isShowTip="false"
              v-model="froms.photos"
            />
          </div>
        </el-form-item>
        <el-form-item label="图文详情*" style="flex: 1" prop="photosDetail">
          <div style="display: flex; flex-wrap: wrap">
            <image-upload
              :oneAll="3"
              :limit="6"
              :isShowTip="false"
              v-model="froms.photosDetail"
            />
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button
          type="primary"
          @click="submitForm1('froms')"
          v-if="iftyper == 1"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog title="删除" :visible.sync="deleteDialogVisible1" width="30%">
      <span>是否删除此热门餐饮？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible1 = false">取 消</el-button>
        <el-button type="primary" @click="confirmDelete1"
          >确 定 删 除</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  saves,
  infos,
  mealPage,
  getItemDetail,
  deleteItem,
  saveItem,
  isRec,
  isMealTags,
} from "@/api/platform/monthlyDiet";
import ImageUpload from "@/components/ImageUpload/index";
import { giftList } from "@/api/platform/public";
import FileUpload from "@/components/FileUpload/index";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import {
  page,
  save,
  delTable,
  info,
  update,
} from "@/api/platform/distributionService";
export default {
  name: "app",
  components: { ImageUpload, FileUpload, ElImageViewer },
  data() {
    return {
      isTags: false,
      deleteDialogVisible1: false,
      froms: {
        dishName: "",
        effect: "",
        dishDescription: "",
        photos: [],
        photosDetail: [],
      },
      ruleForms: {
        pageSize: 10,
        pageNum: 1,
      },
      deleteDialogVisible: false,
      inputVisible: false,
      tag: "",
      tabList: [],
      formLabelWidth: "120px",
      addShow: false,
      from: {
        productDetails: [], //图文详情
        productName: "", //商品昵称
        productDescription: "", //商品描述
        productPhotoUrl: [], //商品照片,
        videos: [],
        tag: [],
        productPrice: "",
      },
      cuisineDialogVisible: false,
      tabIndex: 0,
      listTab: ["热门餐饮", "月子套餐"],
      activeName: "first",
      showViewer: false, // 显示查看器
      url: "",
      audit: "",
      auditId: "",
      forbidden: false,
      auditInfoList: [],
      dialogVisibleReject: false,
      rejectionReason: "",
      giftList: [],
      ruleForm: {
        contractGiftId: "",
        mealPhotos: [], //月子餐图片
        videos: [],
        mealDescription: "", //简介
        certificateUrl: [], //资质证书
        mealItems: [
          {
            category: "早餐", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
          {
            category: "早餐", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
          {
            category: "早餐", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
          {
            category: "早餐", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
          {
            category: "午餐", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
          {
            category: "午餐", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
          {
            category: "午餐", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
          {
            category: "午餐", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },

          {
            category: "晚餐", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
          {
            category: "晚餐", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
          {
            category: "晚餐", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
          {
            category: "晚餐", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
          {
            category: "点心", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
          {
            category: "点心", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
          {
            category: "点心", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
          {
            category: "点心", //菜品类别
            dishName: "", //菜品名称
            dishDescription: "", //菜品简介
            photos: [], //菜品信息照片
          },
        ], //菜品信息
      },
      listData: [],
      total: "",
      inputValue: "",
      labelList: [],
      deliveryId: "",
      iftyper: 0,
      itemId: "",
      mealTags: [],
    };
  },
  created() {
    let audit = this.$route.query.audit;
    this.audit = audit;
    this.forbidden = this.$route.query.forbidden ? true : false;
    this.auditId = this.$route.query.auditId;
    if (audit) {
      this.auditInfo(this.auditId);
    }
    this.info();
    this.getList();
    this.getMealPage();
  },
  methods: {
    checkboxChange(e) {
      //是否启用膳食图片
      let id = this.ruleForm.mealId;
      isMealTags(id).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    isTagsClick(id) {
      //是否推荐
      isRec(id).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getMealPage();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    cancel() {
      this.cuisineDialogVisible = !this.cuisineDialogVisible;
      this.itemId = "";
    },
    examine(row) {
      this.cuisineDialogVisible = !this.cuisineDialogVisible;
      this.iftyper = 0;
      this.getItemDetail(row.itemId);
    },
    compile(row) {
      this.cuisineDialogVisible = !this.cuisineDialogVisible;
      this.iftyper = 1;
      this.getItemDetail(row.itemId);
      this.itemId = row.itemId;
      this.froms.itemId = row.itemId;
    },
    handleSizeChange(val) {
      this.ruleForms.pageSize = val;
      this.getLists();
    },
    handleCurrentChange(val) {
      this.ruleForms.pageNum = val;
      this.getLists();
    },
    handleClose(tag) {
      this.labelList.splice(this.labelList.indexOf(tag), 1);
    },
    handleCloses(tag) {
      this.from.tag.splice(this.from.tag.indexOf(tag), 1);
    },
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.labelList.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
    choice(e) {
      this.from.tag.forEach((item) => {
        if (item == e) {
          this.$message("标签已添加");
          black;
        }
        if (this.from.tag.length > 1) {
          this.$message("最多添加两个");
          black;
        }
      });
      this.from.tag.push(e);
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    submitForm1(formName) {
      let from = this.froms;
      if (from.dishName == "") {
        this.$message("请填写菜品名称");
        return;
      }
      if (from.effect == "") {
        this.$message("请填写菜品功效");
        return;
      }
      if (from.dishDescription == "") {
        this.$message("请填写菜品简介");
        return;
      }
      if (from.photos.length == 0) {
        this.$message("请上传菜品图片");
        return;
      }
      console.log(this.itemId);
      this.froms.mealId = this.ruleForm.mealId;
      let hint = this.itemId
        ? "确定修改该热门餐饮, 是否继续?"
        : "确定新增该热门餐饮, 是否继续?";
      this.$confirm(hint, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          saveItem(this.froms).then((res) => {
            if (res.code == 200) {
              this.$message(res.msg);
              this.cuisineDialogVisible = false;
              this.getMealPage();
              this.froms = {
                dishName: "",
                effect: "",
                dishDescription: "",
                photos: [],
                photosDetail: [],
              };
              return;
            } else {
              this.$message(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    submitForm(formName) {
      let from = this.from;
      if (from.productPhotoUrl.length == 0) {
        this.$message("请上传商品照片");
        return;
      }
      if (from.tag.length == 0) {
        this.$message("请添加商品标签");
        return;
      }
      if (from.productName == "") {
        this.$message("请填写商品名称");
        return;
      }
      if (from.productDetails.length == 0) {
        this.$message("请上传图文详情");
        return;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.deliveryId) {
            this.$confirm("确定修改膳食配送吗, 是否继续?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                if (this.from.videos == "") {
                  this.from.videos = [];
                }
                update(this.from).then((res) => {
                  if (res.code == 200) {
                    this.$message(res.msg);
                    this.addShow = false;
                    this.getLists();
                    return;
                  } else {
                    this.$message(res.msg);
                  }
                });
              })
              .catch(() => {
                this.$message({
                  type: "info",
                  message: "已取消修改",
                });
              });
          }

          if (!this.deliveryId) {
            this.$confirm("确定新增该膳食配送吗, 是否继续?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                save(this.from).then((res) => {
                  if (res.code == 200) {
                    this.$message(res.msg);
                    this.addShow = false;
                    this.getLists();
                    this.from = {
                      productDetails: [], //图文详情
                      productName: "", //商品昵称
                      productDescription: "", //商品描述
                      productPhotoUrl: [], //商品照片,
                      videos: [],
                    };
                    return;
                  } else {
                    this.$message(res.msg);
                  }
                });
              })
              .catch(() => {
                this.$message({
                  type: "info",
                  message: "已取消新增",
                });
              });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    addTag(e) {
      let list = this.tabList;
      list.push(this.tag);
      this.tabList = list;
      this.tag = "";
    },
    cuisineAdd(index) {
      if (index == 0) {
        this.cuisineDialogVisible = !this.cuisineDialogVisible;
        this.iftyper = 1;
      } else {
        (this.from = {
          productDetails: [], //图文详情
          productName: "", //商品昵称
          productDescription: "", //商品描述
          productPhotoUrl: [], //商品照片,
          videos: [],
          tag: [],
          productPrice: "",
        }),
          (this.labelList = []);
        this.addShow = !this.addShow;
      }
    },
    tabClick(index) {
      this.listData = [];
      this.tabIndex = index;
      if (index == 0) {
        this.getMealPage();
      } else {
        this.getLists();
      }
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    getList() {
      //列表
      this.loading = true;
      let data = {
        pageSize: 1000,
        pageNum: 1,
      };
      giftList(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.giftList = res.data;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    getLists() {
      //列表
      this.loading = true;
      page(this.ruleForms).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.rows;
          this.total = res.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    getMealPage() {
      //列表
      this.loading = true;
      mealPage().then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.rows[0].mealItems;
          // this.total = res.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    getItemDetail(id) {
      //详情
      this.loading = true;
      getItemDetail(id).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          (this.froms.dishName = res.data.dishName),
            (this.froms.effect = res.data.effect),
            (this.froms.dishDescription = res.data.dishDescription),
            (this.froms.photos = res.data.photos),
            (this.froms.photosDetail = res.data.photosDetail),
            (this.loading = false);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    handleDeletes(row) {
      this.deleteDialogVisible1 = !this.deleteDialogVisible1;
      this.itemId = row.itemId;
    },
    onPreview(e) {
      this.showViewer = true;
      this.url = e;
    },
    // 关闭查看器
    closeViewer() {
      this.showViewer = false;
    },
    rejectConfirm() {
      //驳回确定
      if (this.rejectionReason == "") {
        this.$message("请填写驳回理由");
        return;
      }
      this.dialogVisibleReject = false;
      let data = {
        auditId: this.auditId,
        auditResult: false,
        rejectionReason: this.rejectionReason,
      };
      audit(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.$router.go(-1);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    reject() {
      //驳回
      this.dialogVisibleReject = true;
    },
    pass() {
      //通过
      this.$confirm("是否审核通过?", "审核通过", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // this.$message({
          //   type: 'success',
          //   message: '审核通过!'
          // });
          let data = {
            auditId: this.auditId,
            auditResult: true,
          };
          audit(data).then((res) => {
            if (res.code == 200) {
              this.$message(res.msg);
              this.$router.go(-1);
              return;
            } else {
              this.$message(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消审核",
          });
        });
    },

    auditInfo(auditId) {
      //查询审核信息
      auditInfo(auditId).then((res) => {
        if (res.code == 200) {
          this.auditInfoList = res.data;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    confirm1() {
      if (this.mealTags.length == 0) {
        this.$message("请上传膳食科普照片");
        return;
      }
      let data = {
        mealTags: this.mealTags,
        mealId: this.ruleForm.mealId,
      };
      saves(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    confirm() {
      let data = this.ruleForm;
      if (data.mealPhotos.length == 0) {
        this.$message("请上传基础信息照片");
        return;
      }
      if (data.mealDescription == "") {
        this.$message("请输入月子餐简介");
        return;
      }
      // for (let i = 0; i < data.mealItems.length; i++) {
      //   if (data.mealItems[i].photos.length == 0) {
      //     this.$message("请上传菜品照片");
      //     return;
      //   }
      //   if (data.mealItems[i].dishName == "") {
      //     this.$message("请输入菜品名称");
      //     return;
      //   }
      //   if (data.mealItems[i].dishDescription == "") {
      //     this.$message("请输入菜品介绍");
      //     return;
      //   }
      // }
      // if (data.certificateUrl.length == 0) {
      //   this.$message("请上传资历证书");
      //   return;
      // }
      if (!this.ruleForm.videos) {
        this.ruleForm.videos = [];
      }
      saves(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.info();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    info() {
      //查询
      infos().then((res) => {
        if (res.code == 200) {
          if (res.data) {
            this.ruleForm = res.data;
            this.isTags = res.data.isTags;
            this.mealTags = res.data.mealTags;
            this.$message(res.msg);
          }
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    infos(deliveryId) {
      //查询月子套房信息
      info(deliveryId).then((res) => {
        if (res.code == 200) {
          this.from = res.data;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    addCuisine(category) {
      this.ruleForm.mealItems.push({
        category: category, //菜品类别
        dishName: "", //菜品名称
        dishDescription: "", //菜品简介
        photos: [], //菜品信息照片
      });
    },
    handleUpdate(row) {
      //编辑
      this.deliveryId = row.deliveryId;
      this.addShow = true;
      this.infos(row.deliveryId);
      (this.from = {
        productDetails: [], //图文详情
        productName: "", //商品昵称
        productDescription: "", //商品描述
        productPhotoUrl: [], //商品照片,
        videos: [],
        tag: [],
        productPrice: "",
      }),
        (this.labelList = []);
    },
    handleDelete(row) {
      //删除
      this.deliveryId = row.deliveryId;
      this.deleteDialogVisible = true;
    },
    confirmDelete() {
      //确定删除
      delTable(this.deliveryId).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.deleteDialogVisible = false;
          this.getLists();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    confirmDelete1() {
      //确定删除
      deleteItem(this.itemId).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.deleteDialogVisible1 = false;
          this.getMealPage();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .titles {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.titleName {
  padding: 24px 0 10px 24px;
  font-size: 20px;
  color: #17191a;
}
.rule {
  width: 98%;
  height: 2px;
  background: #ebebeb;
  margin: 0 auto;
}
.app {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
}
.BackgroundPicture {
  display: flex;
  margin-top: 20px;
  .uploadTitle {
    display: flex;
    color: #17191a;
    font-size: 14px;
    width: 100px;
    justify-content: flex-end;
    padding: 0 12px 0 0;
  }
}
.hint {
  display: flex;
  color: #45464a;
  font-size: 14px;
  margin-top: 8px;
  .hints {
    display: flex;
    align-items: center;
    .hints1 {
      color: #ff6c11;
    }
  }
}
.title5 {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #606266;
  box-sizing: border-box;
  font-weight: bold;
  width: 10%;
  .required {
    color: red;
  }
}
.content {
  margin-left: 100px;
}
.content1 {
  display: flex;
  align-items: center;
  margin: 20px 0 10px 0;
  .textarea {
    width: 368px;
  }
}
.content2 {
  display: flex;
  align-items: center;
  margin: 0 0 20px 8px;
  .title6 {
    width: 40%;
    color: #17191a;
    font-size: 14px;
  }
  .title7 {
    margin-left: 8px;
  }
}
.btn {
  margin: 20px auto;
  text-align: center;
}

.auditContent {
  width: 100%;
  padding: 20px 24px;
  background: #ff6c11;
  border-radius: 6px;
  font-size: 14px;
  line-height: 14px;
}
.pass {
  display: flex;
  align-items: center;
  color: #000000;
}
.passCause {
  color: #000000;
  margin-left: 20px;
}
.viewLocations {
  display: flex;
  .viewLocation {
    padding: 5px 5px;
    border-radius: 10px;
    border: 1px solid #dcdcdc;
    margin-left: 10px;
    .viewLocation1 {
      color: #3f85ff;
      font-size: 14px;
      text-align: center;
      margin-top: 8px;
    }
  }
}
.elTabs {
  padding: 20px 20px;
}
.cuisineTab {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .cuisineTab_1 {
    display: flex;
    .cuisineTab_2 {
      width: 100px;
      height: 28px;
      background: #3f85ff;
      font-size: 14px;
      color: #ffff;
      border-radius: 2px;
      text-align: center;
      line-height: 28px;
      margin-left: 10px;
      cursor: pointer;
    }
    .cuisineTab_3 {
      width: 100px;
      height: 28px;
      border: 1px solid #dcdcdc;
      font-size: 14px;
      color: #000000;
      border-radius: 2px;
      text-align: center;
      line-height: 28px;
      margin-left: 10px;
      cursor: pointer;
    }
  }
  .cuisineTab_4 {
    width: 100px;
    height: 28px;
    background: #3f85ff;
    font-size: 14px;
    color: #ffff;
    border-radius: 2px;
    text-align: center;
    line-height: 28px;
    margin-right: 50px;
    cursor: pointer;
  }
}
.cuisineTable {
  margin-top: 14px;
}
.block {
  text-align: right;
  margin-top: 10px;
}
.addTag {
  border: 1px solid #dcdcdc;
  padding: 6px 6px;
  min-height: 40px;
  border-radius: 2px;
  .addTag1 {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    .addTag1_2 {
      margin-right: 5px;
      color: #b5b5b5;
      border: 1px solid #dcdcdc;
      height: 25px;
      line-height: 25px;
      padding: 0 6px;
      margin-bottom: 5px;
    }
    input {
      width: 100px;
      height: 25px;
    }
    .addTag1_1 {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #b5b5b5;
      border: 1px solid #dcdcdc;
      padding: 0px 6px;
      height: 25px;
      margin-left: 5px;
    }
  }
}
.label {
  width: 368px;
  height: 86px;
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  display: flex;
  flex-wrap: wrap;
  padding: 10px 10px;
  .labelName {
    color: #7c7d81;
    font-size: 14px;
    height: 24px;
    border-radius: 3px;
    line-height: 24px;
    background: #ebebeb;
    margin-right: 16px;
    padding: 0 8px;
  }
  .inputLabel {
    width: 156px;
    font-size: 12px;
  }
}
.imgHint {
  display: flex;
  align-items: center;
}
.employ {
  display: flex;
  margin-left: 50px;
  align-items: center;
}
.employ_1 {
  font-size: 15px;
  color: #606266;
  font-weight: bold;
}
.employ_2 {
  margin-left: 30px;
}
</style>
