<template>
  <div class="app">
    <!-- <div class="head">
          <div class="titles">话题管理</div>
          <div class="title1">Pages/宝妈社区/话题管理</div>
      </div> -->
    <div class="content">
      <div class="contentHead">
        <h2 class="title2">线索公海</h2>
        <div class="contentHeads">
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">姓名</p>
              <el-input
                v-model="ruleForm.name"
                placeholder="请输入姓名"
                clearable
              ></el-input>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">电话</p>
              <el-input
                v-model="ruleForm.tel"
                placeholder="请输入电话"
                clearable
              ></el-input>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">线索阶段</p>
              <el-select
                v-model="ruleForm.leadStage"
                placeholder="请选择线索阶段"
                clearable
                style="width: 159px; margin-left: 10px"
              >
                <el-option
                  v-for="item in leadStage"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">跟进状态</p>
              <el-select
                v-model="ruleForm.followUpStatus"
                placeholder="请选择跟进状态"
                clearable
                style="width: 159px; margin-left: 10px"
              >
                <el-option
                  v-for="item in followUpStatus"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">分配状态</p>
              <el-select
                v-model="ruleForm.assignmentStatus"
                placeholder="请选择跟进状态"
                clearable
                style="width: 159px; margin-left: 10px"
              >
                <el-option
                  v-for="item in assignmentStatus"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <!-- <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">通话状态</p>
              <el-select
                v-model="ruleForm.status"
                placeholder="请选择通话状态"
                clearable
                style="width: 159px; margin-left: 10px"
              >
                <el-option
                  v-for="item in status"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </div>
          </div> -->
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">线索来源</p>
              <el-select
                v-model="ruleForm.source"
                placeholder="请选择线索来源"
                clearable
                style="width: 159px; margin-left: 10px"
              >
                <el-option
                  v-for="item in source"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">预产期</p>
              <div style="display: flex; margin-left: 10px">
                <el-date-picker
                  v-model="ruleForm.dueDateStart"
                  type="date"
                  placeholder="开始时间"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <el-date-picker
                  v-model="ruleForm.dueDateEnd"
                  type="date"
                  placeholder="结束时间"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </div>
            </div>
          </div>
          <div class="timeCycle">
            <p
              v-for="(item, index) in timeTap"
              :key="index"
              :class="
                timeCycleIndex == index ? 'timeCycleActive' : 'timeCycles'
              "
              @click="timeCycleClick(item.timeCycle, index)"
            >
              {{ item.dictLabel }}
            </p>
          </div>
          <div class="roomMessage">
            <div class="sel">
              <p class="selTitle">创建时间</p>
              <div style="display: flex; margin-left: 10px">
                <el-date-picker
                  v-model="createTimeStart"
                  type="date"
                  placeholder="创建开始时间"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <el-date-picker
                  v-model="createTimeEnd"
                  type="date"
                  placeholder="创建结束时间"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </div>
            </div>
          </div>
          <el-button
            type="primary"
            style="margin-left: 8px; height: 36px"
            @click="inquire"
            >查询</el-button
          >
        </div>
        <el-button
          type="primary"
          style="height: 36px; margin: 20px 0 28px 0"
          @click="addMaternitySuite"
          >新建线索</el-button
        >
        <el-table v-loading="loading" :data="listData" class="table" border>
          <el-table-column
            label="姓名"
            prop="name"
            :show-overflow-tooltip="true"
            align="center"
            fixed
          />
          <el-table-column
            label="电话"
            prop="tel"
            :show-overflow-tooltip="true"
            align="center"
            fixed
          >
          </el-table-column>
          <el-table-column label="所属人 " prop="followerName" align="center" />
          <el-table-column label="联系状态" prop="status" align="center">
            <template slot-scope="scope">
              <p v-if="scope.row.status == 0">未联系</p>
              <p v-if="scope.row.status == 1">已联系</p>
              <p v-if="scope.row.status == 2">已到店</p>
              <p v-if="scope.row.status == 3">已交易</p>
            </template>
          </el-table-column>
          <el-table-column label="线索标签" prop="tags" align="center">
          </el-table-column>

          <el-table-column label="线索阶段" prop="leadStage" align="center">
            <template slot-scope="scope">
              <p v-if="scope.row.leadStage == 'NEW_LEAD'">新线索</p>
              <p v-if="scope.row.leadStage == 'PENDING_COMMUNICATION'">
                待再次沟通
              </p>
              <p v-if="scope.row.leadStage == 'INTERESTED'">有意向</p>
              <p v-if="scope.row.leadStage == 'DEAL_CLOSED'">已成交</p>
              <p v-if="scope.row.leadStage == 'INVALID_LEAD'">无效线索</p>
              <p v-if="scope.row.leadStage == 'SECONDARY_LEAD'">二次线索</p>
            </template>
          </el-table-column>
          <el-table-column label="跟进状态" prop="leadStatus" align="center">
            <template slot-scope="scope">
              <p v-if="scope.row.leadStatus == 'FOLLOWED_UP'">已跟进</p>
              <p v-if="scope.row.leadStatus == 'NOT_FOLLOWED_UP'">待跟进</p>
            </template>
          </el-table-column>
          <el-table-column
            label="分配状态"
            prop="assignmentStatus"
            align="center"
          >
            <template slot-scope="scope">
              <p v-if="scope.row.assignmentStatus == 'ALLOCATED'">已分配</p>
              <p v-if="scope.row.assignmentStatus == 'NOT_ALLOCATED'">未分配</p>
            </template>
          </el-table-column>
          <el-table-column label="最新跟进记录">
            <template>
              <div :key="index" v-for="(item, index) in listData">
                <div :key="i" v-for="(res, i) in item.followUpLogs">
                  <p>{{ res.nickname }}</p>
                  <p>{{ res.followUpDate }}</p>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="最新修改时间"
            prop="updateTime"
            align="center"
          >
          </el-table-column>
          <el-table-column label="线索来源" prop="source" align="center">
          </el-table-column>
          <el-table-column
            label="线索创建时间"
            prop="createTime"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="线索分配时间"
            prop="assignmentDate"
            align="center"
          >
          </el-table-column>
          <el-table-column label="微信" prop="wechat" align="center">
          </el-table-column>
          <el-table-column label="预产期" prop="dueDate" align="center">
          </el-table-column>
          <el-table-column label="年龄" prop="age" align="center">
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="drawerShow(scope.row)"
                >详情</el-button
              >
              <el-button size="mini" type="text" @click="allocation(scope.row)"
                >分配</el-button
              >
              <el-button size="mini" type="text" @click="followUp(scope.row)"
                >跟进</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="block">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog title="新增线索" :visible.sync="deleteDialogVisible" width="35%">
      <el-form
        :model="form"
        ref="form"
        label-width="70px"
        class="demo-ruleForm"
        :rules="rules"
      >
        <div style="display: flex; justify-content: space-between">
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model.number="form.name"
              autocomplete="off"
              style="width: 200px"
              placeholder="请填写姓名"
            ></el-input>
          </el-form-item>
          <el-form-item label="电话" prop="tel">
            <el-input
              v-model.number="form.tel"
              autocomplete="off"
              style="width: 200px"
              placeholder="请填写电话"
            ></el-input>
          </el-form-item>
        </div>
        <div style="display: flex; justify-content: space-between">
          <el-form-item label="性别">
            <el-select v-model="form.region" placeholder="请选择性别">
              <el-option label="男" value="男"></el-option>
              <el-option label="女" value="女"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="年龄" prop="age">
            <el-input
              v-model.number="form.age"
              autocomplete="off"
              style="width: 200px"
              placeholder="请填写年龄"
            ></el-input>
          </el-form-item>
        </div>
        <div style="display: flex; justify-content: space-between">
          <el-form-item label="客户标签">
            <el-select v-model="form.tags" placeholder="请选择客户标签">
              <el-option label="高意向" value="高意向"></el-option>
              <el-option label="中意向" value="中意向"></el-option>
              <el-option label="低意向" value="低意向"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="线索来源">
            <el-input
              v-model.number="form.source"
              autocomplete="off"
              style="width: 200px"
              placeholder="请填写线索来源"
            ></el-input>
          </el-form-item>
        </div>
        <div style="display: flex; justify-content: space-between">
          <el-form-item label="预产期">
            <el-form-item>
              <el-date-picker
                type="date"
                placeholder="请选择预产期"
                v-model="form.dueDate"
                style="width: 100%"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </el-form-item>
          </el-form-item>
          <el-form-item label="微信">
            <el-input
              v-model.number="form.wechat"
              autocomplete="off"
              style="width: 200px"
              placeholder="请填写微信号"
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm('form')">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="写跟进" :visible.sync="followUpShow" width="50%">
      <el-form
        :model="notesForm"
        ref="notesForm"
        label-width="100px"
        class="demo-dynamic"
      >
        <el-form-item
          prop="notes"
          label="填写跟进"
          :rules="[{ required: true, message: '请输入跟进', trigger: 'blur' }]"
        >
          <textarea
            v-model="notesForm.notes"
            placeholder="请填写跟进"
            style="width: 400px; height: 100px; padding: 10px 10px"
          ></textarea>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="followUpShow = false">取 消</el-button>
        <el-button type="primary" @click="notesSubmitForm('notesForm')"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog title="分配线索" :visible.sync="allocationShow" width="30%">
      <el-form
        :model="allocationFrom"
        ref="allocationFrom"
        label-width="100px"
        class="demo-dynamic"
      >
        <el-form-item
          prop="notes"
          label="选择员工"
          :rules="[{ required: true, message: '请选择员工', trigger: 'blur' }]"
        >
          <el-form-item>
            <el-select v-model="followerId" placeholder="请选择员工">
              <el-option
                :label="item.nickname"
                :value="item.userId"
                v-for="(item, index) in userList"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="allocationShow = false">取 消</el-button>
        <el-button type="primary" @click="allocationSubmitForm()"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-drawer
      :visible.sync="drawer"
      :direction="direction"
      :before-close="handleClose"
      :size="drawerWidth"
    >
      <div class="details">
        <div class="detailsHead">
          <div class="detailsHead_1">{{ detailsData.name }}</div>
          <div class="detailsHead_2">{{ detailsData.leadStatus }}</div>
        </div>
        <div class="detailsComtent">
          <el-descriptions direction="vertical" :column="4" border>
            <el-descriptions-item label="线索所属人">{{
              detailsData.followerName
            }}</el-descriptions-item>
            <el-descriptions-item label="电话">{{
              detailsData.tel
            }}</el-descriptions-item>
            <el-descriptions-item label="预产期" :span="2">{{
              detailsData.dueDate
            }}</el-descriptions-item>
            <el-descriptions-item label="年龄">{{
              detailsData.age
            }}</el-descriptions-item>
            <el-descriptions-item label="线索来源">{{
              detailsData.source
            }}</el-descriptions-item>
            <el-descriptions-item label="线索创建时间" :span="2">{{
              detailsData.createTime
            }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="detailsHead">
          <div class="detailsHead_1">线索阶段</div>
        </div>
        <div class="statusTag">
          <div
            :class="item.check ? 'activestatus' : 'status'"
            v-for="(item, index) in leadStage"
            :key="index"
          >
            {{ item.dictLabel }}
          </div>
        </div>
        <div class="detailsHead">
          <div class="detailsHead_1">补充线索跟进情况</div>
        </div>
        <div class="addTag">
          <div
            :class="checkTagIndex == index ? 'addTag2' : 'addTag1'"
            v-for="(item, index) in tagList"
            :key="index"
            @click="tag(index, item.dictLabel)"
          >
            {{ item.dictLabel }}
          </div>
          <div class="addTag1" @click="changeTag">增加标签</div>
        </div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="跟进记录" name="first">
            <!-- <div>
              <div class="record">
                <img
                  src="http://cdn.xiaodingdang1.com/2024/07/18/2e48fbe2d2ae476e8e741444f35303d4.png"
                  alt=""
                  style="width: 26px; height: 26px"
                />
                <p class="recordUser">用户56565261</p>
              </div>
              <div class="recordContent">
                <div class="fgx"></div>
                <datagrid class="recordContent1">
                  <p class="recordContent2">这个线索已跟进</p>
                  <p class="recordContent3">06月20日 15:16</p>
                </datagrid>
              </div>
            </div> -->

            <el-timeline :reverse="reverse">
              <el-timeline-item
                v-for="(activity, index) in followedLogsList"
                :key="index"
                :timestamp="activity.nickname"
              >
                <div>{{ activity.notes }}</div>
                <div>{{ activity.updateTime }}</div>
              </el-timeline-item>
            </el-timeline>
          </el-tab-pane>
          <el-tab-pane label="线索操作日志" name="second">
            <el-table :data="operationLogsList" style="width: 100%">
              <el-table-column prop="operationType" label="类型" width="180">
                <template slot-scope="scope">
                  <p v-if="scope.row.operationType == 'FOLLOW_UP'">跟进记录</p>
                  <p v-if="scope.row.operationType == 'CHANGE_STAGE'">
                    修改线索阶段
                  </p>
                  <p
                    v-if="scope.row.operationType == 'CHANGE_ASSIGNMENT_STATUS'"
                  >
                    修改分配状态
                  </p>
                  <p v-if="scope.row.operationType == 'CHANGE_TAG'">修改标签</p>
                </template>
              </el-table-column>
              <el-table-column prop="nickname" label="操作人" width="180">
              </el-table-column>
              <el-table-column prop="notes" label="日志内容"> </el-table-column>
              <el-table-column prop="createTime" label="操作时间">
              </el-table-column>
            </el-table>
            <div class="block">
              <el-pagination
                @size-change="handleSizeChanges"
                @current-change="handleCurrentChanges"
                :page-sizes="[3, 20, 30, 40]"
                :page-size="3"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totals"
              >
              </el-pagination>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  page,
  save,
  customer,
  assignLead,
  userPage,
  followedLogs,
  operationLogs,
  changeTag,
} from "@/api/platform/clueHighSeas";
export default {
  name: "app",
  data() {
    var validatePhone = (rule, value, callback) => {
      const reg = /^1[3-9]\d{9}$/;
      if (value === "") {
        callback(new Error("手机号不能为空"));
      } else if (!reg.test(value)) {
        callback(new Error("请输入正确的手机号"));
      } else {
        callback();
      }
    };
    return {
      timeTap: [
        {
          dictLabel: "日",
          timeCycle: "day",
        },
        {
          dictLabel: "周",
          timeCycle: "week",
        },
        {
          dictLabel: "月",
          timeCycle: "month",
        },
      ],
      reverse: true,
      activeName: "first",
      drawerWidth: "40%",
      notesForm: {
        notes: "",
      },
      loading: false,
      tagList: [
        {
          dictLabel: "高意向",
          check: false,
        },
        {
          dictLabel: "中意向",
          check: false,
        },
        {
          dictLabel: "低意向",
          check: false,
        },
      ],
      source: [
        //线索来源
        {
          dictLabel: "微信",
          dictValue: "微信",
        },
        {
          dictLabel: "小程序",
          dictValue: "小程序",
        },
      ],
      status: [
        //客户状态
        {
          dictLabel: "未联系",
          dictValue: "0",
        },
        {
          dictLabel: "已联系",
          dictValue: "1",
        },
        {
          dictLabel: "已到店",
          dictValue: "2",
        },
        {
          dictLabel: "已交易",
          dictValue: "3",
        },
      ],
      assignmentStatus: [
        //分配状态
        {
          dictLabel: "已分配",
          dictValue: "ALLOCATED",
        },
        {
          dictLabel: "未分配",
          dictValue: "NOT_ALLOCATED",
        },
      ],
      followUpStatus: [
        //跟进状态
        {
          dictLabel: "已跟进",
          dictValue: "FOLLOWED_UP",
        },
        {
          dictLabel: "待跟进",
          dictValue: "NOT_FOLLOWED_UP",
        },
      ],
      leadStage: [
        //线索阶段
        {
          dictLabel: "新线索",
          dictValue: "NEW_LEAD",
          check: false,
        },
        {
          dictLabel: "待再次沟通",
          dictValue: "PENDING_COMMUNICATION",
          check: false,
        },
        {
          dictLabel: "有意向",
          dictValue: "INTERESTED",
          check: false,
        },
        {
          dictLabel: "已成交",
          dictValue: "DEAL_CLOSED",
          check: false,
        },
        {
          dictLabel: "无效线索",
          dictValue: "INVALID_LEAD",
          check: false,
        },
        {
          dictLabel: "二次线索",
          dictValue: "SECONDARY_LEAD",
          check: false,
        },
      ],
      topicStatus: [
        //话题状态
        {
          dictLabel: "进行中",
          dictValue: "1",
        },
        {
          dictLabel: "待开始",
          dictValue: "0",
        },
        {
          dictLabel: "已结束",
          dictValue: "2",
        },
      ],
      total: 0,
      topicId: "",
      ruleForm: {
        pageSize: 10,
        pageNum: 1,
        name: "",
        status: "",
        tel: "",
        source: "",
        followUpStatus: "",
        leadStage: "",
        assignmentStatus: "",
        dueDateStart: "",
        dueDateEnd: "",
      },
      timeCycle: "",
      createTimeStart: "",
      createTimeEnd: "",
      form: {
        name: "",
        tel: "",
        region: "",
        age: "",
        tags: "",
        source: "",
        dueDate: "",
        wechat: "",
      },
      rules: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        tel: [{ required: true, trigger: "blur", validator: validatePhone }],
        age: [{ type: "number", message: "年龄必须为数字", trigger: "blur" }],
      },
      deleteDialogVisible: false,
      listData: [],
      followUpShow: false,
      allocationShow: false,
      allocationFrom: {
        nickname: "",
      },
      customerId: "",
      followerId: "",
      userList: [],
      drawer: false,
      direction: "rtl",
      detailsData: "",
      followedLogsList: [],
      pageNums: 1,
      pageSizes: 3,
      totals: 0,
      operationLogsList: [],
      customerIds: "",
      dictLabelTag: "",
      checkTagIndex: -1,
      timeCycleIndex: -1,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    timeCycleClick(timeCycle, index) {
      console.log(index, timeCycle);
      this.timeCycle = timeCycle;
      this.timeCycleIndex = index;
    },
    tag(index, dictLabel) {
      this.checkTagIndex = index;
      this.dictLabelTag = dictLabel;
    },
    changeTag() {
      if (this.dictLabelTag == "") {
        this.$message("请选择标签");
        return;
      }
      let data = {
        tag: this.dictLabelTag,
        customerId: this.customerIds,
      };
      changeTag(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.loading = false;
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    operationLogs(customerId) {
      //分页查询操作记录列表
      let data = {
        pageSize: this.pageSizes,
        pageNum: this.pageNums,
        customerId: customerId,
      };
      operationLogs(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.operationLogsList = res.rows;
          this.totals = res.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    followedLogs(customerId) {
      //分页查询跟进记录列表
      let data = {
        pageSize: 100,
        pageNum: 1,
        customerId: customerId,
      };
      followedLogs(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.followedLogsList = res.rows;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    handleClick(tab, event) {
      if (tab.index == 0) {
        this.followedLogs(this.customerIds);
      } else if (tab.index == 1) {
        this.operationLogs(this.customerIds);
      }
    },
    drawerShow(row) {
      //详情
      this.leadStage.forEach((item) => {
        if (item.dictValue == row.leadStage) {
          item.check = true;
        }
      });
      this.customerIds = row.customerId;
      this.followedLogs(row.customerId);
      this.operationLogs(row.customerId);
      this.detailsData = row;
      this.drawer = true;
    },
    handleClose(done) {
      //详情关闭
      done();
    },
    userPage() {
      //分页查询销售员列表
      this.loading = true;
      let data = {
        pageSize: 200,
        pageNum: 1,
      };
      userPage(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.userList = res.rows;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    assignLead() {
      //分配线索
      this.loading = true;
      let data = {
        customerId: this.customerId,
        followerId: this.followerId,
      };
      assignLead(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.loading = false;
          this.allocationShow = false;
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    allocationSubmitForm() {
      //分配线索确定
      if (this.followerId == "") {
        this.$message("请选择员工");
        return;
      }
      this.assignLead();
    },
    allocation(row) {
      //分配
      this.customerId = row.customerId;
      this.allocationShow = true;
      this.userPage();
    },
    followUp(row) {
      // 获取当前时间精确至[年、月、日、时、分、秒]
      let nTime = new Date();
      let nYear = nTime.getFullYear();
      let nMonth = nTime.getMonth() + 1;
      let nDay = nTime.getDate();
      let nHour = nTime.getHours();
      let nMin = nTime.getMinutes();
      let nSecond = nTime.getSeconds();
      this.notesForm.followUpDate =
        nYear +
        "-" +
        nMonth +
        "-" +
        nDay +
        " " +
        nHour +
        ":" +
        nMin +
        ":" +
        nSecond;
      this.notesForm.customerId = row.customerId;
      //填写跟进
      this.followUpShow = true;
    },
    inquire() {
      //查询
      this.getList();
    },
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val;
      this.getList();
    },
    handleSizeChanges(val) {
      console.log(val);
      this.pageSizes = val;
      this.operationLogs(this.customerIds);
    },
    handleCurrentChanges(val) {
      this.pageNums = val;
      this.operationLogs(this.customerIds);
    },
    addMaternitySuite() {
      //新增
      this.deleteDialogVisible = true;
    },
    notesSubmitForm(formName) {
      //跟进确定
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.customer();
        } else {
          return false;
        }
      });
    },
    customer() {
      //跟进
      this.loading = true;
      customer(this.notesForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.loading = false;
          this.followUpShow = false;
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    submitForm(formName) {
      //创建确定
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.add();
        } else {
          return false;
        }
      });
    },
    add() {
      //新增线索
      this.loading = true;
      save(this.form).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.loading = false;
          this.deleteDialogVisible = false;
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    getList() {
      //列表
      this.loading = true;
      let ruleForm = this.ruleForm;
      console.log(this.createTimeStart);
      if (
        (this.createTimeStart == "" && this.createTimeEnd == "") ||
        (this.createTimeStart == null && this.createTimeEnd == null)
      ) {
        ruleForm.timeCycle = this.timeCycle;
        ruleForm.createTimeStart = "";
        ruleForm.createTimeEnd = "";
      } else {
        ruleForm.createTimeStart = this.createTimeStart;
        ruleForm.createTimeEnd = this.createTimeEnd;
        ruleForm.timeCycle = "";
      }
      page(ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.rows;
          this.total = res.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.app {
  background: #f0f1f5;
  padding-bottom: 30px;
}
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .titles {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  margin: 20px 20px;
  .contentHead {
    background: #ffff;
    padding: 5px 14px;
    border-radius: 10px;
    .title2 {
      color: #17191a;
    }
  }
}
.contentHeads {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.roomMessage {
  display: flex;
}
.sel {
  display: flex;
  align-items: center;
  margin-right: 10px;
  .selTitle {
    width: 30%;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
  }
}
#cars {
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  color: #7c7d81;
  font-size: 14px;
  width: 179px;
  height: 32px;
  margin-left: 6px;
}

.block {
  text-align: right;
  margin-top: 20px;
}
.details {
  padding: 0px 16px;
}
.detailsHead {
  display: flex;
  align-items: center;
  .detailsHead_1 {
    color: #17191a;
    font-size: 16px;
  }
  .detailsHead_2 {
    background: #3f85ff;
    color: #ffff;
    border-radius: 70px;
    padding: 3px 13px;
    margin-left: 16px;
  }
}
.detailsComtent {
  margin: 16px 0;
}
.status {
  border: 1px solid #3f85ff;
  width: 109px;
  height: 38px;
  margin: 16px 0;
  color: #3f85ff;
  font-size: 14px;
  text-align: center;
  line-height: 38px;
}
.activestatus {
  background: #3f85ff;
  width: 109px;
  height: 38px;
  margin: 16px 0;
  color: #ffff;
  font-size: 14px;
  text-align: center;
  line-height: 38px;
}
.statusTag {
  display: flex;
  justify-content: space-between;
}
.addTag {
  width: 100%;
  background: #f8f9f8;
  border-radius: 5px;
  height: 69px;
  margin: 16px 0;
  display: flex;
  align-items: center;
  padding: 0 16px;
  .addTag1 {
    padding: 6px 10px;
    color: #45464a;
    background: #ffffff;
    font-size: 12px;
    margin-right: 10px;
  }
  .addTag2 {
    padding: 6px 10px;
    color: #fff;
    background: #3f85ff;
    font-size: 12px;
    margin-right: 10px;
  }
}
.record {
  display: flex;
  align-items: center;
  .recordUser {
    color: #17191a;
    font-size: 16px;
    margin-left: 12px;
    line-height: 0;
  }
}
.fgx {
  width: 1px;
  height: 41px;
  background: #ebebee;
}
.recordContent1 {
  margin-left: 24px;
  line-height: 0;
}
.recordContent2 {
  color: #17191a;
  font-size: 14px;
}
.recordContent3 {
  color: #7c7d81;
  font-size: 12px;
  padding: 10px 0;
}
.recordContent {
  display: flex;
  align-items: center;
}
.timeCycle {
  display: flex;
  align-items: center;
  .timeCycles {
    border: 1px solid #c8c9cd;
    color: #c8c9cd;
    width: 38px;
    height: 25px;
    text-align: center;
    line-height: 25px;
    font-size: 14px;
    margin: 0 10px;
  }
  .timeCycleActive {
    background: #3f85ff;
    color: #ffffff;
    width: 38px;
    height: 25px;
    text-align: center;
    line-height: 25px;
    font-size: 14px;
    margin: 0 10px;
  }
}
</style>
