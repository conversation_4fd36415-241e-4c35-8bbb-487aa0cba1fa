<template>
    <div class="app" v-loading="loading">
        <!-- <div class="head">
            <div class="title">签到管理</div>
            <div class="title1">Pages/签到管理</div>
        </div> -->
        <div class="content">
             <div class="title2">签约礼品</div>
             <el-button type="primary" @click="addCheckManagement">新增礼品</el-button>
             <el-table :data="listData" class="table">
      <el-table-column label="礼品描述" prop="description" :show-overflow-tooltip="true" align="center" />
      <el-table-column label="价值" prop="price" align="center" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope" v-if="scope.row.roleId !== 1">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >编辑</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            @click="clickOnline(scope.row)"
            v-if="!scope.row.onlineStatus"
          >上线</el-button>
          <el-button
            size="mini"
            type="text"
            @click="showing(scope.row)"
            v-if="scope.row.onlineStatus"
          >下线</el-button> -->
          <!-- <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <div class="block">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="100"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>
  </div>
        </div>
<!--新增-->
<el-dialog
  title="提示"
  :visible.sync="addShow"
  width="30%">
  <el-form :model="from" :rules="rules" ref="from" label-width="100px" class="demo-ruleForm">
  <el-form-item label="礼品价值" prop="price">
    <el-input v-model="from.price" type="number" placeholder="请输入礼品价值"></el-input>
  </el-form-item>
  <el-form-item label="礼品描述" prop="description">
    <el-input type="textarea" v-model="from.description" placeholder="请输入礼品描述"></el-input>
  </el-form-item>
      </el-form>
  <span slot="footer" class="dialog-footer">
    <el-button @click="addShow = false">取 消</el-button>
    <el-button type="primary" @click="submitForm('from')">确 定</el-button>
  </span>
</el-dialog>
<el-dialog :visible.sync="dialogVisible" width="80%">

<img width="100%" :src="dialogImageUrl" alt="" />
<i class="el-icon-arrow-left" @click="leftPhoto" v-if="currentIndex>0"
  style="background:rgba(0, 0, 0, 0.3);width:35px;height:70px;text-align: center;line-height: 70px;color:#fff;font-size:23px;position: absolute;top:48%;left:15px;">
</i>
<i class="el-icon-arrow-right" @click="rightPhoto" v-if="currentIndex<photoList.length-1"
  style="background:rgba(0, 0, 0, 0.3);width:35px;height:70px;text-align: center;line-height: 70px;color:#fff;font-size:23px;position: absolute;top:48%;right:15px;">
</i>
</el-dialog>
    </div>
</template>

<script>
import { page,save,delTable,info,update,online } from "@/api/platform/contractGift";
import ImageUpload from "@/components/ImageUpload/index"
export default {
    name: "app",
    components: {ImageUpload},
  data() {
    return {
      listData:[],
      loading:false,
      nowAddress: [], //临时保存地址
            fixedAddress: 'https://txyoss.oss-cn-shenzhen.aliyuncs.com',
            currentIndex: '',//当前图片的下标
            fileCount: 0,
            picNum: 0,
            maxNum: 5,
            photoList: [], //照片列表
            dialogImageUrl: '',
            dialogVisible: false,
            disabled: false,
            total:0,
      ruleForm: {
        pageSize:10,
        pageNum:1
      },
      from:{
        description:'',
        price:''
      },
      addShow:false,
      deleteDialogVisible:false,
      contractGiftId:'',
      rules: {
        price: [
            {required: true,message: '请填写礼品价值', trigger: 'blur' }
          ],
          description: [
            {required: true,message: '请填写礼品描述', trigger: 'blur' }
          ],
        
      }
    }
},
created(){
  this.getList()
},
methods:{
  handleSizeChange(val) {
      this.ruleForm.pageSize=val
      this.getList()
      },
      handleCurrentChange(val) {
        this.ruleForm.pageNum=val
        this.getList()
      },
      handleUpdate(row){//编辑
        this.contractGiftId=row.contractGiftId
        this.addShow=true
        this.info(row.contractGiftId)
      },
      handleDelete(row){//删除
        this.contractGiftId=row.contractGiftId
        this.deleteDialogVisible=true
      },
    //   confirmDelete(){//确定删除
    //     delTable(this.giftId).then(res=>{
    //       if(res.code==200){
    //                 this.$message(res.msg);
    //                 this.deleteDialogVisible=false
    //                this.getList()
    //             return 
    //             }else{
    //                 this.$message(res.msg); 
    //             }
    //     })
    //   },
      addCheckManagement(){//签到管理
           this.addShow=true
      },
      getList(){//列表
        this.loading=true
        page(this.ruleForm).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.listData=res.rows
                    this.total=res.total
                    this.loading=false
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
      },
      info(contractGiftId){//查询月子套房信息
            info(contractGiftId).then(res => {
                if(res.code==200){
                    this.from=res.data
                    this.$message(res.msg);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
        },
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            if(this.contractGiftId){
              this.$confirm('确定修改该礼品吗, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          update(this.from).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.addShow=false
                    this.getList()
                    this.from={
                     description:'',
                        price:''
                     }
                    // this.$router.go(0)
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消修改'
          });          
        });
            }

            if(!this.contractGiftId){
              this.$confirm('确定新增该礼品吗, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          save(this.from).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.addShow=false
                    this.getList()
                    this.from={
                     description:'',
                        price:''
                     }
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消新增'
          });          
        });
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
}
}
</script>

<style scoped lang="scss">
.head{
    width: 100%;
    height: 80px;
    background: #ffff;
    color: #17191A;
    padding: 15px 20px;
    .title{
        font-size: 22px;
        font-weight: bold;
    }
    .title1{
        font-size: 14px;
    }
}
.content{
    background: #ffff;
    margin: 10px 10px;
    border-radius: 5px;
    padding: 24px 20px;
    .title2{
        font-size: 20px;
        color: #17191A;
        margin-bottom: 24px;
    }
    .table{
        margin-top: 20px;
    }
}
.block{
    text-align: right;
    margin-top: 20px;
}
</style>