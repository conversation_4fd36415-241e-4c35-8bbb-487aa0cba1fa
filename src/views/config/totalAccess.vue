<template>
  <div class="app-container home" id="app">
    <div class="dataStatistics">
      <div class="dataStatistics1">
        <div class="dataStatistics1_1">总访用户量数据分析</div>
        <div class="dataStatistics2">
          <ul class="dataStatistics2_1">
            <li
              :class="
                tabIndex == index ? 'StatisticsActive' : 'dataStatistics2_4'
              "
              v-for="(item, index) in listTab"
              :key="index"
              @click="tab(index)"
            >
              {{ item }}
            </li>
          </ul>
          <el-date-picker
            v-model="startDate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择开始时间"
            class="dataStatistics2_2"
          >
          </el-date-picker>
          <el-date-picker
            v-model="endDate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择结束时间"
            class="dataStatistics2_2"
          >
          </el-date-picker>
          <el-button type="primary" @click="inquire">查询</el-button>
        </div>
      </div>
    </div>
    <div class="distribution">
      <div class="distribution1">
        <p class="distribution1_1">新老用户分布</p>
        <div id="chartLineBox" style="width: 100%; height: 370px"></div>
      </div>
      <div class="distribution2">
        <p class="distribution1_1">性别分布</p>
        <div id="sexDistribution" style="width: 100%; height: 370px"></div>
      </div>
    </div>
    <div class="distribution">
      <div class="distribution1">
        <p class="distribution1_1">用户访问时间</p>
        <div id="accessTime" style="width: 100%; height: 370px"></div>
      </div>
      <div class="distribution2">
        <p class="distribution1_1">年龄分布</p>
        <div id="ageDistribution" style="width: 100%; height: 370px"></div>
      </div>
    </div>
    <div class="table">
      <p class="distribution1_1">用户分析</p>
      <div></div>
      <div class="content">
        <div class="tableInp">
          <div class="ageInp">
            <p>年龄</p>
            <el-input v-model="from.age"></el-input>
          </div>
          <div class="ageInp">
            <p>性别</p>
            <el-select v-model="from.sex" placeholder="请选择">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="ageInp">
            <p>用户类型</p>
            <el-select v-model="from.type" placeholder="请选择">
              <el-option
                v-for="item in optionsType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-button type="primary" @click="query" style="margin-left: 10px"
              >查询</el-button
            >
          </div>
        </div>
        <el-table :data="analysePage" class="table">
          <el-table-column
            label="用户姓名"
            prop="nickname"
            :show-overflow-tooltip="true"
            width="150"
            align="center"
          />
          <el-table-column
            label="电话"
            prop="tel"
            :show-overflow-tooltip="true"
            width="150"
            align="center"
          >
          </el-table-column>
          <el-table-column label="性别" prop="sex" width="200" align="center">
            <template slot-scope="scope">
              <p v-if="scope.row.sex == 0">男</p>
              <p v-if="scope.row.sex == 1">女</p>
            </template>
          </el-table-column>
          <el-table-column label="年龄" prop="age" width="200" align="center" />
          <el-table-column
            label="用户类型"
            prop="type"
            width="200"
            align="center"
          >
            <template slot-scope="scope">
              <p v-if="scope.row.type == 'new'">新用户</p>
              <p v-if="scope.row.type == 'old'">老用户</p>
            </template>
          </el-table-column>
          <el-table-column
            label="页面访问量"
            prop="pageNum"
            width="200"
            align="center"
          />
          <el-table-column
            label="访问次数"
            prop="pv"
            width="200"
            align="center"
          />
          <el-table-column
            label="停留时间"
            prop="viewTime"
            width="200"
            align="center"
          />
          <el-table-column
            label="预产期"
            prop="dueDate"
            width="200"
            align="center"
          />
          <el-table-column
            label="操作"
            width="150"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="details(scope.row)"
                >详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="block">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { userList, analysePage } from "@/api/platform/index";
export default {
  name: "Index",
  data() {
    return {
      sexStatsList: [],
      analysePage: [],
      total: 0,
      eventTime: [],
      uv: [],
      listData: "",
      startDate: null, //开始时间
      endDate: null, //结束时间
      ruleForm: {},
      tabIndex: 0,
      listTab: ["周", "月"],
      from: {
        age: "",
        sex: "",
        type: "",
        pageSize: 10,
        pageNum: 1,
      },
      optionsType: [
        {
          value: "",
          label: "全部",
        },
        {
          value: "new",
          label: "新用户",
        },
        {
          value: "old",
          label: "老用户",
        },
      ],
      options: [
        {
          value: "",
          label: "全部",
        },
        {
          value: 0,
          label: "男",
        },
        {
          value: 1,
          label: "女",
        },
      ],

      // 版本号
      version: "3.8.7",
      value1: "",
    };
  },
  created() {
    this.totalVisit();
    this.analysePages();
  },
  methods: {
    query() {
      this.analysePages();
    },
    handleSizeChange(val) {
      this.from.pageSize = val;
      this.analysePages();
    },
    handleCurrentChange(val) {
      this.from.pageNum = val;
      this.analysePages();
    },
    analysePages() {
      analysePage(this.from).then((res) => {
        if (res.code == 200) {
          this.analysePage = res.rows;
          this.total = res.total;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    tab(index) {
      this.tabIndex = index;
      this.totalVisit();
      this.startDate = null; //开始时间
      this.endDate = null; //结束时间
    },
    inquire() {
      this.totalVisit();
    },
    totalVisit() {
      //查询
      if (this.startDate && this.endDate) {
        this.ruleForm.queryType = "custom";
      } else {
        this.ruleForm.queryType =
          this.tabIndex == 0 ? "week" : this.tabIndex == 1 ? "month" : "";
      }
      this.ruleForm.startDate = this.startDate;
      this.ruleForm.endDate = this.endDate;
      userList(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.listData = res.data;
          this.$message(res.msg);
          let eventTime = [];
          let uv = [];
          res.data.dayViewStatsList.forEach((item) => {
            eventTime.push(item.eventTime);
            uv.push(item.uv);
          });
          this.eventTime = eventTime;
          this.uv = uv;
          let sexStatsList = [];
          res.data.sexStatsList.forEach((item) => {
            let data = {
              product: item.ageRange,
              男: item.totalMale,
              女: item.totalFemale,
            };
            sexStatsList.push(data);
          });
          this.sexStatsList = sexStatsList;
          this.sexDistribution();
          this.chartLineBox();
          this.accessTime();
          this.ageDistribution();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    details(row) {
      //详情
      this.$router.push({
        path: "/dataAnalysisDetails",
        query: {
          type: 1,
          userId: row.userId,
          row: JSON.stringify(row),
        },
      });
    },
    goTarget(href) {
      window.open(href, "_blank");
    },
    ageDistribution() {
      //年龄分布
      this.chartLine = echarts.init(document.getElementById("ageDistribution"));
      // 指定图表的配置项和数据
      var option = {
        legend: {},
        tooltip: {},
        dataset: {
          source: this.sexStatsList,
        },
        xAxis: { type: "category" },
        yAxis: {},
        // Declare several bar series, each will be mapped
        // to a column of dataset.source by default.
        series: [{ type: "bar" }, { type: "bar" }],
        color: ["#0063FF", "#3DD598"],
        barWidth: 30,
      };
      // 使用刚指定的配置项和数据显示图表。
      this.chartLine.setOption(option);
    },
    accessTime() {
      this.chartLine = echarts.init(document.getElementById("accessTime"));
      // 指定图表的配置项和数据
      var option = {
        tooltip: {
          //设置tip提示
          trigger: "axis",
        },
        color: ["#0063FF", "#0063FF"], //设置区分（每条线是什么颜色，和 legend 一一对应）
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.eventTime,
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: this.uv,
            type: "line",
            areaStyle: {},
            itemStyle: {
              normal: {
                //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0.2,
                    color: "#3F85FF", // 0% 处的颜色
                  },
                  {
                    offset: 0.3,
                    color: "#3F85FF", // 100% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#fff", // 100% 处的颜色
                  },
                ]), //背景渐变色
                lineStyle: {
                  // 系列级个性化折线样式
                  width: 2,
                  type: "solid",
                  color: "#3F85FF", //折线的颜色
                },
              },
            },
            // 设置折线弧度，取值：0-1之间
            smooth: 0.5,
          },
        ],
      };

      // 使用刚指定的配置项和数据显示图表。
      this.chartLine.setOption(option);
    },
    sexDistribution() {
      //性别分布
      this.chartLine = echarts.init(document.getElementById("sexDistribution"));
      // 指定图表的配置项和数据
      var option = {
        tooltip: {
          //设置tip提示
          trigger: "axis",
        },
        legend: {
          top: "5%",
          left: "center",
        },

        series: [
          {
            name: "访问来源",
            type: "pie",
            center: ["50%", "60%"], //控制位置
            radius: ["40%", "60%"], //控制圆环的宽度
            avoidLabelOverlap: true, //防止重叠
            label: {
              normal: {
                formatter:
                  "{a|{b}}{abg|}\n{hr|}\n  {b|数量：}{c}  {per|{d}%}  ", //空格也起作用
                backgroundColor: "#eee",
                borderColor: "#aaa",
                borderWidth: 1,
                borderRadius: 2,
                rich: {
                  a: {
                    color: "#45464A",
                    lineHeight: 20,
                    align: "center",
                  },
                  hr: {
                    borderColor: "#aaa",
                    width: "100%",
                    borderWidth: 0.5,
                    height: 0,
                  },
                  b: {
                    fontSize: 12,
                    lineHeight: 20,
                  },
                  // per: {
                  //     color: '#eee',
                  //     backgroundColor: '#334455',
                  //     padding: [2, 4],
                  //     borderRadius: 2
                  // }
                },
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: "14",
                },
              },
            },
            labelLine: {
              normal: {
                show: true,
              },
            },
            data: [
              { value: this.listData.girlUsers, name: "女" },
              { value: this.listData.manUsers, name: "男" },
            ],
            color: ["#3DD598", "#0063FF"], //各个区域颜色
          },
        ],
      };

      // 使用刚指定的配置项和数据显示图表。
      this.chartLine.setOption(option);
    },
    chartLineBox() {
      //新老用户分布
      this.chartLine = echarts.init(document.getElementById("chartLineBox"));
      // 指定图表的配置项和数据
      var option = {
        tooltip: {
          //设置tip提示
          trigger: "axis",
        },
        legend: {
          top: "5%",
          left: "center",
        },

        series: [
          {
            name: "访问来源",
            type: "pie",
            center: ["50%", "60%"], //控制位置
            radius: ["40%", "60%"], //控制圆环的宽度
            avoidLabelOverlap: true, //防止重叠
            label: {
              normal: {
                formatter:
                  "{a|{b}}{abg|}\n{hr|}\n  {b|数量：}{c}  {per|{d}%}  ", //空格也起作用
                backgroundColor: "#eee",
                borderColor: "#aaa",
                borderWidth: 1,
                borderRadius: 2,
                rich: {
                  a: {
                    color: "#45464A",
                    lineHeight: 20,
                    align: "center",
                  },
                  hr: {
                    borderColor: "#aaa",
                    width: "100%",
                    borderWidth: 0.5,
                    height: 0,
                  },
                  b: {
                    fontSize: 12,
                    lineHeight: 20,
                  },
                  // per: {
                  //     color: '#eee',
                  //     backgroundColor: 'red',
                  //     padding: [2, 4],
                  //     borderRadius: 2
                  // }
                },
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: "14",
                },
              },
            },
            labelLine: {
              normal: {
                show: true,
              },
            },
            data: [
              { value: this.listData.newUsers, name: "新用户" },
              { value: this.listData.oldUsers, name: "老用户" },
            ],
            color: ["#3DD598", "#0063FF"], //各个区域颜色
          },
        ],
      };

      // 使用刚指定的配置项和数据显示图表。
      this.chartLine.setOption(option);
    },
  },
  mounted() {
    this.chartLineBox();
    this.sexDistribution();
    this.accessTime();
    this.ageDistribution();
  },
};
</script>

<style scoped lang="scss">
#app {
  background: #f0f1f5;
  padding-bottom: 40px;
  padding: 20px 20px;
}
.dataStatistics {
  background: #fff;
  padding: 20px 20px;
  width: 100%;
  border-radius: 10px;
  .dataStatistics1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 0;
    .dataStatistics1_1 {
      color: #17191a;
      font-size: 20px;
    }
  }
  .dataStatistics2 {
    display: flex;
    align-items: center;
    .dataStatistics2_1 {
      display: flex;
      list-style-type: none;
      .dataStatistics2_4 {
        width: 38px;
        height: 25px;
        border: 1px solid #c8c9cd;
        color: #c8c9cd;
        text-align: center;
        line-height: 25px;
        border-radius: 2px;
        margin-right: 12px;
      }
      .StatisticsActive {
        width: 38px;
        height: 25px;
        background: #3f85ff;
        color: #ffffff;
        text-align: center;
        line-height: 25px;
        border-radius: 2px;
        margin-right: 12px;
      }
    }
    .dataStatistics2_2 {
      margin-right: 12px;
    }
  }
}
.distribution {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  .distribution1 {
    padding: 20px 20px;
    background: #fff;
    border-radius: 10px;
    width: 49.5%;
    position: relative;
    .distribution1_1 {
      color: #17191a;
      font-size: 18px;
    }
  }
  .distribution2 {
    padding: 20px 20px;
    background: #fff;
    width: 49.5%;
    border-radius: 10px;
    .distribution1_1 {
      color: #17191a;
      font-size: 18px;
    }
  }
}
.table {
  margin-top: 20px;
  background: #fff;
  padding: 20px 20px;
  border-radius: 10px;
  width: 100%;
}
.block {
  text-align: right;
  margin-top: 20px;
}
.tableInp {
  display: flex;
  .ageInp {
    display: flex;
    align-items: center;
    margin-left: 10px;
    p {
      width: 30%;
    }
  }
}
</style>
