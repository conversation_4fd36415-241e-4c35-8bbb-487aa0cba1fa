<template>
  <div id="app">
    <!-- <div class="head">
            <div class="titles">月子膳食配置</div>
            <div class="title1">Pages/基础配置/月子膳食配置</div>
        </div> -->

    <div class="app">
      <div class="titleName">标签修改</div>
      <div class="rule"></div>
      <div class="content">
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>标签名称</p>
          </div>
          <div>
            <el-input
              type="text"
              v-model="ruleForm.tagName"
              class="textarea"
              :disabled="forbidden"
            ></el-input>
          </div>
        </div>
      </div>
      <div class="content">
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>描述</p>
          </div>
          <div>
            <el-input type="textarea" v-model="ruleForm.tagDesc"></el-input>
          </div>
        </div>
        <div class="professionalSpeech">专业话术</div>
        <div class="professionalSpeech1">
          <div
            class="professionalSpeech2"
            v-for="(item, index) in ruleForm.professionalList"
            :key="index"
          >
            <div class="professionalSpeech2_1">{{ item.name }}</div>
            <div class="professionalSpeech2_2">
              <el-tooltip
                class="item"
                effect="dark"
                :content="item.desc"
                placement="top-start"
              >
                <div class="professionalSpeech2_8">
                  {{ item.desc }}
                </div>
              </el-tooltip>
            </div>
            <div class="professionalSpeech2_3">
              <p class="professionalSpeech2_4" @click="updateList(item, index)">
                修改
              </p>
              <p class="professionalSpeech2_5" @click="deleteList(index)">
                删除
              </p>
            </div>
          </div>
        </div>
        <el-button
          type="primary"
          @click="addProfessiona"
          style="margin-top: 10px"
          >添加话术</el-button
        >
      </div>
      <div class="titleName">系统方案</div>
      <div class="rule"></div>
      <div class="content">
        <div class="content1">
          <div class="title5">
            <p class="required"></p>
            <p>方案描述</p>
          </div>
          <div>
            <el-input
              type="text"
              v-model="ruleForm.solution.desc"
              class="textarea"
              :disabled="forbidden"
            ></el-input>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p style="color: #f84343">*</p>
            <p>方案配图</p>
          </div>
          <div>
            <div class="viewLocations">
              <!-- <image-upload
                :width="375"
                :width1="2250"
                :height="250"
                :height1="1500"
                :limit="1"
                :isShowTip="false"
                v-model="ruleForm.images"
              /> -->
              <image-upload
                :limit="1"
                :isShowTip="false"
                v-model="ruleForm.solution.images"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="titleName">专家咨询</div>
      <div class="rule"></div>
      <div class="content">
        <div class="content1">
          <div class="title5">
            <p style="color: #f84343">*</p>
            <p>二维码配图</p>
          </div>
          <div v-for="(item, index) in ruleForm.expertList" :key="index">
            <!-- <image-upload
                :width="375"
                :width1="2250"
                :height="250"
                :height1="1500"
                :limit="1"
                :isShowTip="false"
                v-model="ruleForm.expertImg"
              /> -->
            <image-upload
              :limit="1"
              :isShowTip="false"
              v-model="item.expertImg"
              style="height: 150px"
            />
            <el-input
              type="text"
              v-model="item.expertName"
              class="textarea"
              style="width: 150px; margin-top: 5px"
            ></el-input>
          </div>
        </div>
      </div>
      <div class="btn">
        <el-button type="primary" @click="confirm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </div>
    <el-image-viewer
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="[url]"
    />
    <!--添加话术-->
    <el-dialog title="添加话术" :visible.sync="addProfessionaShow" width="30%">
      <el-form
        :model="addRuleForm"
        :rules="addRules"
        ref="addRuleForm"
        label-width="80px"
      >
        <el-form-item label="话术名称" prop="name">
          <el-input
            placeholder="请输入话术名称"
            v-model="addRuleForm.name"
          ></el-input>
        </el-form-item>
        <el-form-item label="话术描述">
          <el-input
            placeholder="请输入话术描述"
            type="textarea"
            v-model="addRuleForm.desc"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addProfessionaShow = false">取 消</el-button>
        <el-button type="primary" @click="submitFormAdd('addRuleForm')"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { save, info, update } from "@/api/platform/labelManagement";
import ImageUpload from "@/components/ImageUpload/index";
import { giftList } from "@/api/platform/public";
import FileUpload from "@/components/FileUpload/index";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
export default {
  name: "app",
  components: { ImageUpload, FileUpload, ElImageViewer },
  data() {
    return {
      addRuleForm: {
        name: "",
        desc: "",
      },
      addRules: {
        name: [{ required: true, message: "请输入话术名称", trigger: "blur" }],
      },
      addProfessionaShow: false,
      showViewer: false, // 显示查看器
      url: "",
      audit: "",
      auditId: "",
      forbidden: false,
      auditInfoList: [],
      dialogVisibleReject: false,
      rejectionReason: "",
      giftList: [],
      id: "",
      ruleForm: {
        expertList: [
          {
            expertName: "",
            expertImg: "",
          },
          {
            expertName: "",
            expertImg: "",
          },
        ],
      },
      updateData: "",
      updateIndex: "",
    };
  },
  created() {
    let id = this.$route.query.id;
    this.id = id;
    this.forbidden = this.$route.query.forbidden ? true : false;
    this.auditId = this.$route.query.auditId;
    this.info();
    this.getList();
  },
  methods: {
    updateList(e, index) {
      this.addRuleForm = e;
      this.addProfessionaShow = true;
      this.updateData = 1;
      this.updateIndex = index;
    },
    deleteList(index) {
      this.ruleForm.professionalList.splice(index, 1);
    },
    submitFormAdd(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let professionalList = this.ruleForm.professionalList;
          if (this.updateData == 1) {
            professionalList[this.updateIndex] = this.addRuleForm;

            this.addProfessionaShow = false;
            return;
          }
          professionalList.push(this.addRuleForm);
          this.addProfessionaShow = false;
        } else {
          return false;
        }
      });
    },
    addProfessiona() {
      this.updateData = "";
      this.addRuleForm = {
        name: "",
        desc: "",
      };
      this.addProfessionaShow = !this.addProfessionaShow;
    },
    cancel() {
      this.$router.go(-1);
    },
    getList() {
      //列表
      this.loading = true;
      let data = {
        pageSize: 1000,
        pageNum: 1,
      };
      giftList(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.giftList = res.data;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    onPreview(e) {
      this.showViewer = true;
      this.url = e;
    },
    confirm() {
      let data = this.ruleForm;
      console.log(data);
      if (data.tagName == "") {
        this.$message("请填写标签名称");
        return;
      }
      if (data.tagDesc == "") {
        this.$message("请填写标签描述");
        return;
      }
      if (data.professionalList.length == 0) {
        this.$message("请添加专业话术");
        return;
      }
      if (data.solution.desc == "") {
        this.$message("请填写系统方案描述");
        return;
      }
      if (data.solution.images == "") {
        this.$message("请上传系统方案配图");
        return;
      }
      for (let i = 0; i < data.expertList.length; i++) {
        if (data.expertList[i].expertImg == "") {
          this.$message("请上传专家图片");
          return;
        }
        if (data.expertList[i].expertName == "") {
          this.$message("请填写专家名称");
          return;
        }
      }
      data.expertList.forEach((item) => {
        item.expertImg = item.expertImg[0];
      });
      data.solution.images = data.solution.images[0];
      update(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.info();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    info() {
      //查询
      info(this.id).then((res) => {
        if (res.code == 200) {
          if (res.data) {
            this.ruleForm = res.data;
            if (res.data.expertList.length == 0) {
              this.ruleForm.expertList = [
                {
                  expertName: "",
                  expertImg: "",
                },
                {
                  expertName: "",
                  expertImg: "",
                },
              ];
            }
            res.data.expertList.forEach((item) => {
              let expertImg = [];
              expertImg.push(item.expertImg);
              item.expertImg = expertImg;
            });
            let images = [];
            images.push(
              res.data.solution.images ? res.data.solution.images : ""
            );
            res.data.solution.images = images;
            this.$message(res.msg);
          }
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    addCuisine(category) {
      this.ruleForm.mealItems.push({
        category: category, //菜品类别
        dishName: "", //菜品名称
        dishDescription: "", //菜品简介
        photos: [], //菜品信息照片
      });
    },
  },
};
</script>

<style scoped lang="scss">
.professionalSpeech {
  padding: 8px 16px;
  background: #dae7ff;
  color: #45464a;
  font-size: 14px;
  font-weight: bold;
  margin: 0 300px 0 0px;
}
.professionalSpeech1 {
  width: 80%;
  .professionalSpeech2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    padding: 5px;
    .professionalSpeech2_1 {
      width: 20%;
      color: #3f6fff;
      text-align: center;
    }
    .professionalSpeech2_2 {
      width: 50%;
      color: #333333;
      .professionalSpeech2_8 {
        width: 100%;
        overflow: hidden;
      }
    }
    .professionalSpeech2_3 {
      width: 20%;
      display: flex;
      .professionalSpeech2_4 {
        color: #3f6fff;
        cursor: pointer;
      }
      .professionalSpeech2_5 {
        color: #f84343;
        margin-left: 30px;
        cursor: pointer;
      }
    }
  }
}

.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .titles {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.titleName {
  padding: 24px 0 10px 24px;
  font-size: 20px;
  color: #17191a;
}
.rule {
  width: 98%;
  height: 2px;
  background: #ebebeb;
  margin: 0 auto;
}
.app {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
}
.BackgroundPicture {
  display: flex;
  margin-top: 20px;
  .uploadTitle {
    display: flex;
    color: #17191a;
    font-size: 14px;
    width: 100px;
    justify-content: flex-end;
    padding: 0 12px 0 0;
  }
}
.hint {
  display: flex;
  color: #45464a;
  font-size: 14px;
  margin-top: 8px;
  .hints {
    display: flex;
    align-items: center;
    .hints1 {
      color: #ff6c11;
    }
  }
}
.title5 {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #606266;
  box-sizing: border-box;
  font-weight: bold;
  width: 10%;
  .required {
    color: red;
  }
}
.content {
  margin-left: 100px;
}
.content1 {
  display: flex;
  align-items: center;
  margin: 20px 0 10px 0;
  .textarea {
    width: 368px;
  }
}
.content2 {
  display: flex;
  align-items: center;
  margin: 0 0 20px 8px;
  .title6 {
    width: 40%;
    color: #17191a;
    font-size: 14px;
  }
  .title7 {
    margin-left: 8px;
  }
}
.btn {
  margin: 20px auto;
  text-align: center;
}

.auditContent {
  width: 100%;
  padding: 20px 24px;
  background: #ff6c11;
  border-radius: 6px;
  font-size: 14px;
  line-height: 14px;
}
.pass {
  display: flex;
  align-items: center;
  color: #000000;
}
.passCause {
  color: #000000;
  margin-left: 20px;
}
.viewLocations {
  .viewLocation {
    padding: 5px 5px;
    border-radius: 10px;
    border: 1px solid #dcdcdc;
    margin-left: 10px;
    .viewLocation1 {
      color: #3f85ff;
      font-size: 14px;
      text-align: center;
      margin-top: 8px;
    }
  }
}
</style>
