<template>
    <div class="app-container home" id="app">
        <div class="dataStatistics">
            <div class="dataStatistics1">
            <div class="dataStatistics1_1">浏览总时间数据分析</div>
            <div class="dataStatistics2">
                <ul class="dataStatistics2_1">
                <li :class="tabIndex==index?'StatisticsActive':'dataStatistics2_4'" v-for="item,index in listTab" :key="index" @click="tab(index)">{{ item }}</li>
              </ul>
              <el-date-picker
      v-model="startDate"
      type="date"
      value-format="yyyy-MM-dd HH:mm:ss"
      placeholder="选择开始时间" class="dataStatistics2_2">
    </el-date-picker>
    <el-date-picker
    v-model="endDate"
      type="date"
      value-format="yyyy-MM-dd HH:mm:ss"
      placeholder="选择结束时间" class="dataStatistics2_2">
    </el-date-picker>
    <el-button type="primary" @click="inquire">查询</el-button>
            </div>
          </div>
        </div>
        <div class="pageView">
            <div class="pageView1">
                <p class="distribution1_1">页面访问时间</p>
                <div id="accessTime" style=" width:100%;
            height: 370px;"> </div>
            </div>
            <div class="pageView2">
                <p class="distribution1_1">页面时间占比</p>
                <!-- <div class="parameters">
                    <div class="parameter">
                        <p class="dot"></p>
                        <p class="name">房间</p>
                        <p class="time">2小时15分钟</p>
                    </div>
                    <div class="parameter">
                        <p class="dot"></p>
                        <p class="name">膳食</p>
                        <p class="time">2小时10分钟</p>
                    </div>
                </div>
                <div class="parameters">
                    <div class="parameter">
                        <p class="dot"></p>
                        <p class="name">护理团队</p>
                        <p class="time">2小时5分钟</p>
                    </div>
                    <div class="parameter">
                        <p class="dot"></p>
                        <p class="name">产康</p>
                        <p class="time">2小时</p>
                    </div>
                </div>
                <div class="parameters">
                    <div class="parameter">
                        <p class="dot"></p>
                        <p class="name">社区</p>
                        <p class="time">2小时15分钟</p>
                    </div>
                    <div class="parameter">
                        <p class="dot"></p>
                        <p class="name">评价</p>
                        <p class="time">2小时15分钟</p>
                    </div>
                </div> -->
                    <div id="chartLineBox" style=" width:760px;
            height: 370px;"> </div>
            </div>
        </div>
        <div class="table">
     <p class="distribution1_1">模块分析</p>
     <div>
     </div>
     <div class="content">
           <el-table :data="statsModule" class="table">
    <el-table-column label="模块分布" prop="moduleName" :show-overflow-tooltip="true" width="150" align="center" />
    <el-table-column label="访问次数" prop="viewNum" :show-overflow-tooltip="true" width="150" align="center" >
  </el-table-column>
    <el-table-column label="访问人数" prop="userNum" width="200" align="center" />
    <el-table-column label="分享次数" prop="shareNum" width="200" align="center" />
    <el-table-column label="分享人数" prop="shareUserNum" width="200" align="center" />
    <el-table-column label="次均时长" prop="viewTimeStr" width="200" align="center" />
    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
      <template slot-scope="scope">
        <el-button
          size="mini"
          type="text"
          @click="details(scope.row)"
        >详情</el-button>
      </template>
    </el-table-column>
  </el-table>
      </div>
  </div>
    </div>
  </template>
  
  <script>
  import { detailVisit,statsModule} from "@/api/platform/index";
  import * as echarts from 'echarts'
  export default {
    name: "Index",
    data() {
      return {
        LineBoxs:[],
        LineBox:[],
        moduleName:[],
        viewTime:[],
        statsModule:[],
        startDate:null,//开始时间
      endDate:null,//结束时间
      ruleForm:{
      },
        tabIndex:0,
        listTab:['周','月'],
        listData:[
            {
                productName:'1'
            }
        ],
        // 版本号
        version: "3.8.7",
        value1:''
      };
    },
    created(){
      // this.detailVisit()
      this.statsModules()
    },
    methods: {
      inquire(){
        this.statsModules()
      },
      statsModules(){
        let from={
        }
        if(this.startDate){
          from.startDate=this.startDate
        }
        if(this.endDate){
          from.endDate=this.endDate
        }
        if(this.startDate&&this.endDate){
        from.queryType='custom'
      }else{
        from.queryType=this.tabIndex==0?'week':this.tabIndex==1?'month':''
      }
        statsModule(from).then(res => {
          if(res.code==200){
                    this.statsModule=res.data
                    let viewTime=[]
                    let moduleName=[]
                    let LineBox=[]
                    let LineBoxs=[]
                    res.data.forEach(item=>{
                      viewTime.push(item.viewTime)
                      moduleName.push(item.moduleName)
                      let data={
                        name:item.moduleName,
                        value:item.viewTime,
                      }
                      let datas={
                        name:item.moduleName,
                        value:item.viewTime,
                      }
                      LineBox.push(data)
                      LineBoxs.push(datas)
                    })
                    this.viewTime=viewTime
                    this.moduleName=moduleName
                    this.LineBox=LineBox
                    this.LineBoxs=LineBoxs
                    this.accessTime()
                    this.chartLineBox()
                    this.$message(res.msg);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
      },
      detailVisit(){//查询
      if(this.startDate&&this.endDate){
        this.ruleForm.queryType='custom'
      }else{
        this.ruleForm.queryType=this.tabIndex==0?'week':this.tabIndex==1?'month':''
      }
      this.ruleForm.startDate=this.startDate
      this.ruleForm.endDate=this.endDate
      detailVisit(this.ruleForm).then(res => {
                if(res.code==200){
                    this.listData=res.data
                    this.$message(res.msg);
                    let eventTime=[]
                    let uv=[]
                    res.data.dayViewStatsList.forEach(item => {
                      eventTime.push(item.eventTime)
                      uv.push(item.uv)
                    });
                    this.eventTime=eventTime
                    this.uv=uv
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
        },
      tab(index){
     this.tabIndex=index
      this.startDate=null//开始时间
     this.endDate=null//结束时间
     this.statsModules()
      },
        details(row){//详情
            this.$router.push({
                        path: "/totalBrowsingTimeDetails", query: {
                          module:row.module
                        },
                    });
        },
      goTarget(href) {
        window.open(href, "_blank");
      },
      accessTime(){
        this.chartLine = echarts.init(document.getElementById('accessTime'));
          // 指定图表的配置项和数据
          var option = {
            xAxis: {
    type: 'category',
    data: this.moduleName
  },
  yAxis: {
    type: 'value',
    name:'单位:分钟'
  },
  series: [
    {
      data: this.viewTime,
      type: 'bar',
      barWidth:30,
      itemStyle: {
            normal: {
　　　　　　　　//这里是重点
                color:'#0063FF',
                barBorderRadius: [50, 50, 0, 0]
            }
        }
    }
  ]
          };
   
          // 使用刚指定的配置项和数据显示图表。
          this.chartLine.setOption(option);
      },
      chartLineBox(){
        this.chartLine = echarts.init(document.getElementById('chartLineBox'));
          // 指定图表的配置项和数据
          var option = {
            title:{
            text:'',
            top:'bottom',
            left:'center',
            textStyle:{
                fontSize: 14,
                fontWeight: '',
                color: '#333'
            },
        },//标题
        tooltip: {
            trigger: 'item',
            formatter: "{a} <br/>{b}: {c} ({d}%)"
            /*formatter:function(val){   //让series 中的文字进行换行
                 console.log(val);//查看val属性，可根据里边属性自定义内容
                 var content = var['name'];
                 return content;//返回可以含有html中标签
             },*/ //自定义鼠标悬浮交互信息提示，鼠标放在饼状图上时触发事件
        },//提示框，鼠标悬浮交互时的信息提示
        legend: {
            show: true,
            orient: 'vertical',
            x: 'center',
            left:110,
            bottom:0,
            data: this.LineBoxs
        },//图例属性，以饼状图为例，用来说明饼状图每个扇区，data与下边series中data相匹配
        graphic:{
            type:'text',
            left:'center',
            top:'center',
            style:{
                text:'', //使用“+”可以使每行文字居中
                textAlign:'center',
                font:'italic bolder 16px cursive',
                fill:'#000',
                width:30,
                height:30
            }
        },//此例饼状图为圆环中心文字显示属性，这是一个原生图形元素组件，功能很多
        series: [
            {
                name:'',//tooltip提示框中显示内容
                type: 'pie',//图形类型，如饼状图，柱状图等
                radius: ['35%', '65%'],//饼图的半径，数组的第一项是内半径，第二项是外半径。支持百分比，本例设置成环形图。具体可以看文档或改变其值试一试
                //roseType:'area',是否显示成南丁格尔图，默认false
                itemStyle: {
                    normal:{
                        label:{
                            show:true,
                            textStyle:{color:'#3c4858',fontSize:"10"},
                            formatter:function(val){   //让series 中的文字进行换行
                                return val.name.split("-").join("\n");}
                        },//饼图图形上的文本标签，可用于说明图形的一些数据信息，比如值，名称等。可以与itemStyle属性同级，具体看文档
                        labelLine:{
                            show:true,
                            lineStyle:{color:'#3c4858'}
                        }//线条颜色
                    },//基本样式
                    emphasis: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)',//鼠标放在区域边框颜色
                        textColor:'#000'
                    }//鼠标放在各个区域的样式
                },
                data: this.LineBox,//数据，数据中其他属性，查阅文档
                color: ['#3F85FF','#2C57FF','#722FD1','#2AD98A','#9FDB1D','#FFC439'],//各个区域颜色
            },//数组中一个{}元素，一个图，以此可以做出环形图
        ],//系列列表
    };
   
          // 使用刚指定的配置项和数据显示图表。
          this.chartLine.setOption(option);
      }
    },
    mounted(){
     this.accessTime()
     this.chartLineBox()
    }
  };
  </script>
  
  <style scoped lang="scss">
  #app{
    background: #F0F1F5;
      padding-bottom: 40px;
      padding: 20px 20px;
  }
  .dataStatistics{
    background: #fff;
    padding: 20px 20px;
    width: 100%;
    border-radius: 10px;
    .dataStatistics1{
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 0;
      .dataStatistics1_1{
        color: #17191A;
        font-size: 20px;
      }
    }
    .dataStatistics2{
      display: flex;
      align-items: center;
      .dataStatistics2_1{
        display: flex;
        list-style-type: none;
        .dataStatistics2_4{
          width: 38px;
          height: 25px;
          border: 1px solid #C8C9CD;
          color: #C8C9CD;
          text-align: center;
          line-height: 25px;
          border-radius: 2px;
          margin-right: 12px;
        }
        .StatisticsActive{
          width: 38px;
          height: 25px;
          background: #3F85FF;
          color: #FFFFFF;
          text-align: center;
          line-height: 25px;
          border-radius: 2px;
          margin-right: 12px;
        }
      }
      .dataStatistics2_2{
        margin-right: 12px;
      }
    }
}
.pageView{
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    .pageView1{
        background: #fff;
        border-radius: 12px;
        padding: 20px 20px;
        width: 64%;
    }
    .pageView2{
        position: relative;
        background: #fff;
        border-radius: 12px;
        padding: 20px 20px;
        width: 35%;
    }
}

.table{
    margin-top: 20px;
    background: #fff;
      padding: 20px 20px;
      border-radius: 10px;
      width: 100%;
  }
  .block{
  text-align: right;
  margin-top: 20px;
}
#chartLineBox{
   position:absolute;
   left: -80px;
   top: 40px;
}
.parameters{
    display: flex;
    justify-content: center;
    width: 100%;
.parameter{
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 0;
    width: 100%;
    .dot{
      width: 7px;
      height: 7px;
      border-radius: 50px;
      background: #3F85FF;
    }
    .name{
     color: #45464A;
     margin-left: 6px;
    }
    .time{
      color: #000000;
      margin-left: 15px;
    }
}
}

  </style>
  
   