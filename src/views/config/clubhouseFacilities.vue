<template>
  <div id="app">
    <!-- <div class="head">
             <div class="title">会所设施配置</div>
             <div class="title1">Pages/会所设施配置</div>
         </div> -->
    <div class="content">
      <div class="auditContent" v-if="auditInfoList.auditStatus == 2">
        <div class="pass">
          <img
            src="http://cdn.xiaodingdang1.com/icon%402x.png"
            alt=""
            style="width: 20px; height: 20px"
          />
          <p>驳回原因</p>
        </div>
        <p class="passCause">{{ auditInfoList.rejectionReason }}</p>
      </div>

      <div v-for="(item, index) in listData" :key="index">
        <div class="content1" v-if="index == 0">
          <div class="title5">
            <p class="required">*</p>
            <p>背景图片</p>
          </div>
          <div class="upload">
            <div class="viewLocations">
              <image-upload
                :limit="8"
                :isShowTip="false"
                v-model="item.backgroundPhotos"
                v-if="!forbidden"
              />
              <!-- <div
                class="viewLocation"
                @click="
                  onPreview(
                    'http://cdn.xiaodingdang1.com/2024/05/11/a20be99b76fb448e9763b08e846e7725png'
                  )
                "
              >
                <img
                  src="http://cdn.xiaodingdang1.com/2024/05/11/a20be99b76fb448e9763b08e846e7725png"
                  alt=""
                  style="width: 180px; height: 110px"
                />
                <div class="viewLocation1">查看位置</div>
              </div> -->
            </div>
            <div v-if="forbidden">
              <img
                :src="item"
                alt=""
                v-for="(item, index) in item.facilityPhotos"
                :key="index"
                style="
                  width: 146px;
                  height: 146px;
                  margin-right: 10px;
                  border: 1px solid #c0ccda;
                  border-radius: 6px;
                "
              />
            </div>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>设施图片</p>
          </div>
          <div class="upload">
            <div class="viewLocations">
              <image-upload
                :width="375"
                :width1="2250"
                :height="200"
                :height1="1200"
                :limit="8"
                :isShowTip="false"
                v-model="item.facilityPhotos"
                v-if="!forbidden"
              />
              <div
                class="viewLocation"
                @click="
                  onPreview(
                    'http://cdn.xiaodingdang1.com/2024/05/11/a20be99b76fb448e9763b08e846e7725png'
                  )
                "
              >
                <img
                  src="http://cdn.xiaodingdang1.com/2024/05/11/a20be99b76fb448e9763b08e846e7725png"
                  alt=""
                  style="width: 180px; height: 110px"
                />
                <div class="viewLocation1">查看位置</div>
              </div>
            </div>
            <div v-if="forbidden">
              <img
                :src="item"
                alt=""
                v-for="(item, index) in item.facilityPhotos"
                :key="index"
                style="
                  width: 146px;
                  height: 146px;
                  margin-right: 10px;
                  border: 1px solid #c0ccda;
                  border-radius: 6px;
                "
              />
            </div>
          </div>
        </div>
        <div class="content1">
          <div class="title5"><p>设施视频</p></div>
          <div class="upload">
            <file-upload
              :limit="1"
              :isShowTip="false"
              v-model="item.videos"
              v-if="!forbidden"
            />
            <div v-if="forbidden">
              <video
                controls="controls"
                :src="item"
                alt=""
                v-for="(item, index) in item.videos"
                :key="index"
                style="
                  width: 146px;
                  height: 146px;
                  margin-right: 10px;
                  border: 1px solid #c0ccda;
                  border-radius: 6px;
                "
              ></video>
            </div>
          </div>
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>设施名称</p>
          </div>
          <input
            type="text"
            placeholder="请输入设施名称"
            class="input"
            v-model="item.facilityName"
            :disabled="forbidden"
          />
        </div>
        <div class="content1">
          <div class="title5">
            <p class="required">*</p>
            <p>会所介绍</p>
          </div>
          <el-input
            type="textarea"
            class="textarea"
            placeholder="请输入会所介绍信息"
            v-model="item.facilityDescription"
            :disabled="forbidden"
          ></el-input>
          <div class="del" @click="remove(index)" v-if="index > 0">删除</div>
        </div>
      </div>
      <div class="content1">
        <div class="title5"></div>
        <div>
          <el-button @click="add" :disabled="forbidden"
            >添加设施<i class="el-icon-plus"></i
          ></el-button>
        </div>
      </div>
      <div class="btn" v-if="!audit">
        <el-button type="primary" @click="confirm">保存</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
      <div class="btn" v-if="auditInfoList.auditStatus == 0">
        <el-button type="primary" @click="pass">通过</el-button>
        <el-button @click="reject">驳回</el-button>
      </div>
      <div class="btn">
        <el-button type="success" v-if="auditInfoList.auditStatus == 1"
          >已通过</el-button
        >
        <el-button type="warning" v-if="auditInfoList.auditStatus == 2"
          >已驳回</el-button
        >
      </div>
    </div>
    <!--驳回-->
    <el-dialog title="驳回原因" :visible.sync="dialogVisibleReject" width="30%">
      <el-input
        type="textarea"
        v-model="rejectionReason"
        placeholder="请输入驳回原因"
      ></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleReject = false">取 消</el-button>
        <el-button type="primary" @click="rejectConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-image-viewer
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="[url]"
    />
  </div>
</template>

<script>
import ImageUpload from "@/components/ImageUpload/index";
import { save, info } from "@/api/platform/clubhouseFacilities";

import FileUpload from "@/components/FileUpload/index";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
export default {
  components: { ImageUpload, FileUpload, ElImageViewer },
  name: "app",
  data() {
    return {
      showViewer: false, // 显示查看器
      url: "",
      audit: "",
      auditId: "",
      forbidden: false,
      auditInfoList: [],
      dialogVisibleReject: false,
      rejectionReason: "",
      listData: [
        {
          facilityName: "",
          facilityPhotos: [],
          facilityDescription: "",
          videos: [],
        },
      ],
      ruleForm: {
        clubActivityAreaPhotos: [], //会所照片
        clubDescription: "", //会所介绍
        clubName: "", //会所名称
        clubBackgroundPhotos: [], //头部照片
      },
    };
  },
  created() {
    let audit = this.$route.query.audit;
    this.audit = audit;
    this.forbidden = this.$route.query.forbidden ? true : false;
    this.auditId = this.$route.query.auditId;
    if (audit) {
      this.auditInfo(this.auditId);
    }
    this.info();
  },
  methods: {
    onPreview(e) {
      this.showViewer = true;
      this.url = e;
    },
    // 关闭查看器
    closeViewer() {
      this.showViewer = false;
    },
    remove(index) {
      this.listData.splice(index, 1);
    },
    cancel() {
      this.$router.go(-1);
    },
    rejectConfirm() {
      //驳回确定
      if (this.rejectionReason == "") {
        this.$message("请填写驳回理由");
        return;
      }
      this.dialogVisibleReject = false;
      let data = {
        auditId: this.auditId,
        auditResult: false,
        rejectionReason: this.rejectionReason,
      };
      audit(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.$router.go(-1);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    reject() {
      //驳回
      this.dialogVisibleReject = true;
    },
    pass() {
      //通过
      this.$confirm("是否审核通过?", "审核通过", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // this.$message({
          //   type: 'success',
          //   message: '审核通过!'
          // });
          let data = {
            auditId: this.auditId,
            auditResult: true,
          };
          audit(data).then((res) => {
            if (res.code == 200) {
              this.$message(res.msg);
              this.$router.go(-1);
              return;
            } else {
              this.$message(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消审核",
          });
        });
    },

    auditInfo(auditId) {
      //查询审核信息
      auditInfo(auditId).then((res) => {
        if (res.code == 200) {
          this.auditInfoList = res.data;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },

    add() {
      this.listData.push({
        facilityName: "",
        facilityPhotos: [],
        facilityDescription: "",
        videos: [],
      });
    },
    confirm() {
      let data = this.listData;
      for (let i = 0; i < data.length; i++) {
        if (data[i].facilityPhotos.length == 0) {
          this.$message("请上传设施图片");
          return;
        }
        if (data[i].facilityName == "") {
          this.$message("请输入设施名称");
          return;
        }
        if (data[i].facilityDescription == "") {
          this.$message("请输入设施介绍");
          return;
        }
        if (!data[i].videos) {
          data[i].videos = [];
        }
      }

      save(this.listData).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    info() {
      //查询
      info().then((res) => {
        if (res.code == 200) {
          this.listData = res.data.length > 0 ? res.data : this.listData;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .title {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
  padding: 24px 50px;
  .title2 {
    font-size: 20px;
    color: #17191a;
    margin-bottom: 24px;
  }
  .table {
    margin-top: 20px;
  }
  .title5 {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
    width: 10%;
    .required {
      color: red;
    }
  }
  .content1 {
    display: flex;
    align-items: center;
    margin: 20px 0 10px 0;
    .input {
      width: 240px;
      height: 32px;
      border: 1px solid #dcdcdc;
      border-radius: 3px;
    }
    .upload {
      width: 90%;
    }
  }
}
.btn {
  margin: 20px auto;
  text-align: center;
}
#cars {
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  color: #7c7d81;
  font-size: 14px;
  width: 129px;
  height: 32px;
  margin-left: 6px;
}
.postpartum {
  display: flex;
  align-items: center;
}
.inputs {
  width: 100px;
  height: 32px;
  border: 1px solid #dcdcdc;
  border-radius: 3px;
}
.units {
  width: 46px;
  height: 32px;
  background: #dcdcdc;
  border: 1px solid #dcdcdc;

  text-align: center;
  line-height: 32px;
  font-size: 14px;
}
.textarea {
  width: 368px;
}
.auditContent {
  width: 100%;
  padding: 20px 24px;
  background: #ff6c11;
  border-radius: 6px;
  font-size: 14px;
  line-height: 14px;
}
.pass {
  display: flex;
  align-items: center;
  color: #000000;
}
.passCause {
  color: #000000;
  margin-left: 20px;
}
.del {
  margin-left: 24px;
  font-size: 14px;
  color: #f84343;
  cursor: pointer;
}
.viewLocations {
  display: flex;
  .viewLocation {
    padding: 5px 5px;
    border-radius: 10px;
    border: 1px solid #dcdcdc;
    margin-left: 10px;
    height: 150px;
    .viewLocation1 {
      color: #3f85ff;
      font-size: 14px;
      text-align: center;
      margin-top: 8px;
    }
  }
}
</style>
