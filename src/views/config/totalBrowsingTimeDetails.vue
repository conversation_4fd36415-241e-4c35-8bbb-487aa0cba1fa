<template>
  <div class="app-container home" id="app">
    <div class="dataStatistics">
      <div class="dataStatistics1">
        <div class="dataStatistics1_1">房间模块页面</div>
        <div class="dataStatistics2">
          <ul class="dataStatistics2_1">
            <li
              v-for="(item, index) in tabList"
              :key="index"
              :class="tabIndex == index ? 'tabActive' : 'tab'"
              @click="tab(index)"
            >
              {{ item }}
            </li>
          </ul>
          <el-date-picker
            v-model="startDate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择开始时间"
            class="dataStatistics2_2"
          >
          </el-date-picker>
          <el-date-picker
            v-model="endDate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择结束时间"
            class="dataStatistics2_2"
          >
          </el-date-picker>
          <el-button type="primary" @click="inquire">查询</el-button>
          <!-- <p class="dataStatistics2_3" style="margin-left:10px">查看更多</p> -->
        </div>
      </div>
      <div class="roomModule">
        <div class="roomModule1">
          <p class="name">月子套房</p>
          <p>
            <span class="num">{{ moduleOne.viewNum }}</span
            ><span class="unit">次</span>
          </p>
        </div>
        <div class="roomModule2">
          <p class="name">套房详情</p>
          <p>
            <span class="num">暂无后端没字段</span><span class="unit">次</span>
          </p>
        </div>
      </div>
    </div>

    <div class="roomModuleAnalysis">
      <p class="distribution1_1">房间模块数据分析</p>
      <div class="roomModuleAnalysis1">
        <div class="roomModuleAnalysis1_1">
          <p class="roomModuleAnalysis1_2">访问人数</p>
          <p class="roomModuleAnalysis1_5">
            <span class="roomModuleAnalysis1_3">{{ moduleOne.userNum }}</span>
            <span class="roomModuleAnalysis1_4">人</span>
          </p>
        </div>
        <div class="fgx"></div>
        <div class="roomModuleAnalysis1_1">
          <p class="roomModuleAnalysis1_2">分享次数</p>
          <p class="roomModuleAnalysis1_5">
            <span class="roomModuleAnalysis1_3" style="color: #f5bb35">{{
              moduleOne.shareNum
            }}</span>
            <span class="roomModuleAnalysis1_4">人</span>
          </p>
        </div>
        <div class="fgx"></div>
        <div class="roomModuleAnalysis1_1">
          <p class="roomModuleAnalysis1_2">分享人数</p>
          <p class="roomModuleAnalysis1_5">
            <span class="roomModuleAnalysis1_3" style="color: #866ffa">{{
              moduleOne.shareUserNum
            }}</span>
            <span class="roomModuleAnalysis1_4">人</span>
          </p>
        </div>
        <div class="fgx"></div>
        <div class="roomModuleAnalysis1_1">
          <p class="roomModuleAnalysis1_2">次均时长</p>
          <p class="roomModuleAnalysis1_5">
            <span class="roomModuleAnalysis1_3" style="color: #2ad98a">{{
              moduleOne.viewTime
            }}</span>
            <span class="roomModuleAnalysis1_4">人</span>
          </p>
        </div>
      </div>
    </div>

    <div class="roomModuleAnalysis">
      <p class="distribution1_1">访问时间</p>
      <div id="chartLineBox" style="width: 100%; height: 349px"></div>
    </div>
    <div class="roomModuleAnalysis">
      <p class="distribution1_1">访问次数</p>
      <div id="accessTime" style="width: 100%; height: 349px"></div>
    </div>
    <div class="table">
      <p class="distribution1_1">模块分析</p>
      <div></div>
      <div class="content">
        <div class="tableInp">
          <div class="ageInp">
            <p>年龄</p>
            <el-input v-model="from.age"></el-input>
          </div>
          <div class="ageInp">
            <p>性别</p>
            <el-select v-model="from.sex" placeholder="请选择">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="ageInp">
            <p>用户类型</p>
            <el-select v-model="from.type" placeholder="请选择">
              <el-option
                v-for="item in optionsType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-button type="primary" @click="query" style="margin-left: 10px"
              >查询</el-button
            >
          </div>
        </div>
        <el-table :data="analysePage" class="table">
          <el-table-column
            label="用户姓名"
            prop="nickname"
            :show-overflow-tooltip="true"
            width="150"
            align="center"
          />
          <el-table-column
            label="电话"
            prop="tel"
            :show-overflow-tooltip="true"
            width="150"
            align="center"
          >
          </el-table-column>
          <el-table-column label="性别" prop="sex" width="200" align="center">
            <template slot-scope="scope">
              <p v-if="scope.row.sex == 0">男</p>
              <p v-if="scope.row.sex == 1">女</p>
            </template>
          </el-table-column>
          <el-table-column label="年龄" prop="age" width="200" align="center" />
          <el-table-column
            label="用户类型"
            prop="type"
            width="200"
            align="center"
          >
            <template slot-scope="scope">
              <p v-if="scope.row.type == 'new'">新用户</p>
              <p v-if="scope.row.type == 'old'">老用户</p>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="details(scope.row)"
                >详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="block">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { moduleOne, analysePage, moduleOneDay } from "@/api/platform/index";
import * as echarts from "echarts";
export default {
  name: "Index",
  data() {
    return {
      eventTime: [],
      viewTime: [],
      pv: [],
      optionsType: [
        {
          value: "new",
          label: "新用户",
        },
        {
          value: "old",
          label: "老用户",
        },
      ],
      options: [
        {
          value: 0,
          label: "男",
        },
        {
          value: 1,
          label: "女",
        },
      ],
      from: {
        age: "",
        sex: "",
        type: "",
        pageSize: 10,
        pageNum: 1,
      },
      analysePage: [],
      total: 0,
      moduleOne: "",
      startDate: null, //开始时间
      endDate: null, //结束时间
      tabIndex: 0,
      ruleForm: {},
      tabList: ["日", "周", "月"],
      module: "",
      age: "",
      options: [],

      listData: [
        {
          productName: "1",
        },
      ],
      // 版本号
      version: "3.8.7",
      value1: "",
    };
  },

  methods: {
    moduleOneDays() {
      let from = {};
      if (this.startDate) {
        from.startDate = this.startDate;
      }
      if (this.endDate) {
        from.endDate = this.endDate;
      }
      if (this.startDate && this.endDate) {
        from.queryType = "custom";
      } else {
        from.queryType =
          this.tabIndex == 0
            ? "day"
            : this.tabIndex == 1
            ? "week"
            : this.tabIndex == 2
            ? "month"
            : "";
      }
      from.module = this.module;
      moduleOneDay(from).then((res) => {
        if (res.code == 200) {
          let eventTime = [];
          let viewTime = [];
          let pv = [];
          res.data.forEach((item) => {
            eventTime.push(item.eventTime);
            viewTime.push(item.viewTime);
            pv.push(item.pv);
          });
          this.eventTime = eventTime;
          this.viewTime = viewTime;
          this.pv = pv;
          this.$message(res.msg);
          this.accessTime();
          this.chartLineBox();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    query() {
      this.analysePages();
    },
    handleSizeChange(val) {
      this.from.pageSize = val;
      this.analysePages();
    },
    handleCurrentChange(val) {
      this.from.pageNum = val;
      this.analysePages();
    },
    analysePages() {
      analysePage(this.from).then((res) => {
        if (res.code == 200) {
          this.analysePage = res.rows;
          this.total = res.total;
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    inquire() {
      this.moduleOnes();
      this.moduleOneDays();
    },
    tab(index) {
      this.tabIndex = index;
      this.moduleOnes();
      this.moduleOneDays();
      this.startDate = null; //开始时间
      this.endDate = null; //结束时间
    },
    moduleOnes() {
      let from = {};
      if (this.startDate) {
        from.startDate = this.startDate;
      }
      if (this.endDate) {
        from.endDate = this.endDate;
      }
      if (this.startDate && this.endDate) {
        from.queryType = "custom";
      } else {
        from.queryType =
          this.tabIndex == 0
            ? "day"
            : this.tabIndex == 1
            ? "week"
            : this.tabIndex == 2
            ? "month"
            : "";
      }
      from.module = this.module;
      moduleOne(from).then((res) => {
        if (res.code == 200) {
          this.moduleOne = res.data;
          let viewTime = [];
          let moduleName = [];
          let LineBox = [];
          res.data.forEach((item) => {
            viewTime.push(item.viewTime);
            moduleName.push(item.module);
            let data = {
              name: item.moduleName + item.viewTimeStr,
              value: item.viewTime,
            };
            LineBox.push(data);
          });
          this.viewTime = viewTime;
          this.moduleName = moduleName;
          this.LineBox = LineBox;
          this.accessTime();
          this.chartLineBox();
          this.$message(res.msg);
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    details(row) {
      //详情
      this.$router.push({
        path: "/dataAnalysisDetails",
        query: {
          type: 1,
          userId: row.userId,
          row: JSON.stringify(row),
        },
      });
    },
    goTarget(href) {
      window.open(href, "_blank");
    },
    chartLineBox() {
      this.chartLine = echarts.init(document.getElementById("chartLineBox"));
      // 指定图表的配置项和数据
      var option = {
        tooltip: {
          //设置tip提示
          trigger: "axis",
        },
        color: ["#3058FF", "#3F85FF"], //设置区分（每条线是什么颜色，和 legend 一一对应）
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.eventTime,
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: this.viewTime,
            type: "line",
            areaStyle: {},
            itemStyle: {
              normal: {
                //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0.2,
                    color: "#3F85FF", // 0% 处的颜色
                  },
                  {
                    offset: 0.3,
                    color: "#3F85FF", // 100% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#fff", // 100% 处的颜色
                  },
                ]), //背景渐变色
                lineStyle: {
                  // 系列级个性化折线样式
                  width: 2,
                  type: "solid",
                  color: "#3F85FF", //折线的颜色
                },
              },
            },
            // 设置折线弧度，取值：0-1之间
            smooth: 0.5,
          },
        ],
      };

      // 使用刚指定的配置项和数据显示图表。
      this.chartLine.setOption(option);
    },
    accessTime() {
      this.chartLine = echarts.init(document.getElementById("accessTime"));
      // 指定图表的配置项和数据
      var option = {
        tooltip: {
          //设置tip提示
          trigger: "axis",
        },
        color: ["#3058FF", "#3F85FF"], //设置区分（每条线是什么颜色，和 legend 一一对应）
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.eventTime,
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: this.pv,
            type: "line",
            areaStyle: {},
            itemStyle: {
              normal: {
                //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0.2,
                    color: "#3F85FF", // 0% 处的颜色
                  },
                  {
                    offset: 0.3,
                    color: "#3F85FF", // 100% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#fff", // 100% 处的颜色
                  },
                ]), //背景渐变色
                lineStyle: {
                  // 系列级个性化折线样式
                  width: 2,
                  type: "solid",
                  color: "#3F85FF", //折线的颜色
                },
              },
            },
            // 设置折线弧度，取值：0-1之间
            smooth: 0.5,
          },
        ],
      };

      // 使用刚指定的配置项和数据显示图表。
      this.chartLine.setOption(option);
    },
  },
  created() {
    this.module = this.$route.query.module;
    this.moduleOnes();
    this.analysePages();
    this.moduleOneDays();
  },
  mounted() {},
};
</script>

<style scoped lang="scss">
#app {
  background: #f0f1f5;
  padding-bottom: 40px;
  padding: 20px 20px;
}
.dataStatistics {
  background: #fff;
  padding: 20px 20px;
  width: 100%;
  border-radius: 10px;
  .dataStatistics1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 0;
    .dataStatistics1_1 {
      color: #17191a;
      font-size: 20px;
    }
  }
  .dataStatistics2 {
    display: flex;
    align-items: center;
    .dataStatistics2_1 {
      display: flex;
      list-style-type: none;
      .tab {
        width: 38px;
        height: 25px;
        border: 1px solid #c8c9cd;
        color: #c8c9cd;
        text-align: center;
        line-height: 25px;
        border-radius: 2px;
        margin-right: 12px;
      }
      .tabActive {
        width: 38px;
        height: 25px;
        color: #fff;
        text-align: center;
        line-height: 25px;
        border-radius: 2px;
        margin-right: 12px;
        background: #3f85ff;
      }
    }
    .dataStatistics2_2 {
      margin-right: 12px;
    }
  }
}
.table {
  margin-top: 20px;
  background: #fff;
  padding: 20px 20px;
  border-radius: 10px;
  width: 100%;
}
.block {
  text-align: right;
  margin-top: 20px;
}

.roomModule {
  display: flex;
  .roomModule1 {
    width: 49.5%;
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    padding: 16px 12px 28px 12px;
    .name {
      color: #1d2129;
      font-size: 16px;
    }
    .num {
      color: #17191a;
      font-size: 36px;
      font-weight: bold;
    }
    .unit {
      color: #86909c;
      font-size: 12px;
      margin-left: 10px;
    }
  }
  .roomModule2 {
    width: 49.5%;
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    padding: 16px 12px 28px 12px;
    margin-left: 1%;
    .name {
      color: #1d2129;
      font-size: 16px;
    }
    .num {
      color: #17191a;
      font-size: 36px;
      font-weight: bold;
    }
    .unit {
      color: #86909c;
      font-size: 12px;
      margin-left: 10px;
    }
  }
}
.roomModuleAnalysis {
  background: #fff;
  border-radius: 10px;
  padding: 20px 20px 60px 20px;
  margin-top: 20px;
  line-height: 0;
  .roomModuleAnalysis1 {
    margin-top: 48px;
    display: flex;
    justify-content: space-around;
    .roomModuleAnalysis1_2 {
      color: #1d2129;
      font-size: 16px;
    }
    .roomModuleAnalysis1_5 {
      margin-top: 30px;
      .roomModuleAnalysis1_3 {
        color: #3f85ff;
        font-size: 36px;
        font-weight: bold;
      }
      .roomModuleAnalysis1_4 {
        color: #86909c;
        font-size: 12px;
      }
    }
    .fgx {
      width: 1px;
      background: #d9d9d9;
    }
  }
}
.distribution1_1 {
  font-size: 18px;
  color: #17191a;
}
.tableInp {
  display: flex;
  .ageInp {
    display: flex;
    align-items: center;
    margin-left: 10px;
    p {
      width: 30%;
    }
  }
}
</style>
