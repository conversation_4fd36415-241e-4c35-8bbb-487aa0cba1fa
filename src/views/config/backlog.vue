<template>
    <div id="app" v-loading="loading">
   
     <!-- <div class="head">
             <div class="title">待办事项</div>
             <div class="title1">Pages/房间工单管理/待办事项</div>
         </div> -->
         <div class="content">
             <div class="contentTitle">待办事项</div>
             <div class="fgx"></div>
             <div class="blackklogs" v-for="item,index in listData" :key="index">
                <p>投诉时间:2024年12月24日 22:00:00</p>
                <div class="blackklog">
                    <div class="blackklog1">
                        <p>入住客户:</p>
                        <p class="blackklog2">{{item.customerName}}</p>
                    </div>
                    <div class="blackklog1">
                        <p>入住时间:</p>
                        <p class="blackklog2">{{item.checkinDate}}</p>
                    </div>
                    <div class="blackklog1">
                        <p>房间服务人员:</p>
                        <p class="blackklog2" v-for="res,indexs in item.staffList" :key="indexs">{{res.staffName}}</p>
                    </div>
                    <div class="blackklog1">
                        <p>客户满意度:</p>
                        <p class="dissatisfaction">{{item.satisfaction}}</p>
                    </div>
                </div>
                <p>{{item.content}}</p>
                <div>
                    <textarea v-model="replyContent" name="" id="" cols="30" rows="10" placeholder="商家回复:" style="width: 100%;background: #FAFAFD;border: none;padding: 20px 20px;"></textarea>
                </div>
                <div style="margin-top: 20px;">
                    <el-button type="primary" @click="send(item.feedbackId)" v-if="item.status==0">发送</el-button>
                   <el-button type="warning" v-if="item.status==1">处理中</el-button>
                </div>
       

             </div>
         
         </div>
    </div>
 </template>
 
 <script>
 import { todo,reply } from "@/api/platform/roomManagement";
 export default {
    name:'app',
    data(){
     return{
        loading:false,
        feedbackId:'',//评价id
        replyContent:'',//回复内容
        listData:[],
     }
    },
    created(){
    let item=this.$route.query.item
    this.item=JSON.parse(item)
    this.todo(this.item.roomId)
     },
    methods:{
        send(feedbackId){//发送
      this.reply(feedbackId)
        },
        todo(roomId){//查询
            console.log(roomId);
            this.loading=true
            todo(roomId).then(res=>{
                if(res.code==200){
                    this.$message(res.msg);
                    this.listData=res.data
                    this.loading = false;
                return 
                }else{
                    this.$message(res.msg); 
                }
            })
        },
        reply(feedbackId){
            let data={
                feedbackId:feedbackId,//评价id
                replyContent:this.replyContent,//回复内容
            }
            if(this.replyContent==''){
                this.$message('请填写回复内容');
                return
            }
            reply(data).then(res=>{
                if(res.code==200){
                    this.$message(res.msg);
                    this.todo(this.item.roomId)
                return 
                }else{
                    this.$message(res.msg); 
                }
            })
        }
    }
 }
 </script>
 
 <style scoped lang="scss">
 .head{
     width: 100%;
     height: 80px;
     background: #ffff;
     color: #17191A;
     padding: 15px 20px;
     .title{
         font-size: 22px;
         font-weight: bold;
     }
     .title1{
         font-size: 14px;
     }
 }
 .content{
     background: #ffff;
     margin: 10px 10px;
     border-radius: 5px;
     padding: 20px 20px 24px 20px;
     .contentTitle{
         color: #17191A;
         font-size: 20px;
         margin-top: 30px;
     }
     .fgx{
         width: 100%;
         height: 2px;
         background: #EBEBEB;
         margin: 20px 0;
     }
    .input{
     width: 20%;
    }
    .content1{
     margin-left: 50px;
    }
    .unit{
     margin-left: 6px;
    }
    .title5{
     display: flex;
     align-items: center;
     font-size: 14px;
     color: #606266;
     box-sizing: border-box;
     font-weight: bold;
     .required{
         color: red;
     }
    }
 }
 .relevance{
     margin-top: 10px;
     margin-left: 150px;
 }
 .postpartum{
     display: flex;
     align-items: center;
 }
 .inputs{
     width: 80px;
     height: 32px;
     border: 1px solid #DCDCDC;
     border-radius: 3px;
     margin-left: 16px;
 }
 .units{
     width: 30px;
     height: 32px;
     background: #DCDCDC;
     border: 1px solid #DCDCDC;
     text-align: center;
     line-height: 32px;
     font-size: 14px;
 }
 .jia{
     font-size:28px;
     margin: 0 8px;
 }
 .BackgroundPicture{
     display: flex;
     margin-top: 20px;
     .uploadTitle{
         display: flex;
         color: #17191A;
         font-size: 14px;
         width: 100px;
         justify-content: flex-end;
         padding: 0 12px 0 0;
     }
     .el-upload--picture-card{
         width: 50px;
         height: 50px;
     }
 }
 .btn{
     margin: 20px auto;
     text-align: center;
 }
 .dialogTitle{
     display: flex;
     align-items: center;
 }
 .tables{
     margin-bottom: 10px;
 }
 .auditContent{
   width: 100%;
   padding: 20px 24px;
   background: #FF6C11;
   border-radius: 6px;
   font-size: 14px;
   line-height: 14px;
 }
 .pass{
   display: flex;
   align-items: center;
   color: #000000;
 }
 .passCause{
   color:#000000;
   margin-left:20px
 }
 .blackklogs{
    line-height: 0;
    font-size: 14px;
    color:#17191A ;
    margin: 20px 50px;
 }
 .blackklog{
    display: flex;
 }
 .blackklog1{
    display: flex;
    margin-right: 30px;
    align-items: center;
 }
 .dissatisfaction{
    width: 52px;
    height: 22px;
    line-height: 22px;
    text-align: center;
    border-radius: 3px;
    background: #F84343;
    color: #ffff;
    margin-left: 5px;
 }
 .blackklog2{
    margin-left: 5px;
 }
 </style>