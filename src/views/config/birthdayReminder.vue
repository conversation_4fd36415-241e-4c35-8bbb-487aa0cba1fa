<template>
    <div class="app" v-loading="loading">
        <!-- <div class="head">
            <div class="titles">生日提醒</div>
            <div class="title1">Pages/客资中心/生日提醒</div>
        </div> -->
        <div class="content">
        <div class="contentHead">
              <h2 class="title2">生日提醒</h2>
              <el-tabs v-model="type" @tab-click="handleClick">
    <el-tab-pane label="妈妈生日" name="mom"></el-tab-pane>
    <el-tab-pane label="宝宝生日" name="baby"></el-tab-pane>
  </el-tabs>
    </div>
    <div  class="reminders">
        <div class="reminder" v-for="item,index in listData" :key="index">
            <div class="reminder1">
               <div class="reminderHeader">
                <div>
                    <el-progress type="circle" :percentage="item.momBirthdayNum" :width="44" :format="format" :height="44" v-if="type=='mom'" :color="colors">
                    </el-progress>
                    <el-progress type="circle" :percentage="item.babyBirthdayNum" :width="44" :format="format" :height="44" v-if="type=='baby'" :color="colors"></el-progress>
                </div>
            <div class="reminderTitle">
                <p class="reminderTitle1" v-if="type=='mom'">妈妈生日</p>
                <p class="reminderTitle1" v-if="type=='baby'">宝宝生日</p>
                <p :class="item.momBirthdayNum==9?'reminderTitle2':item.momBirthdayNum==6?'reminderTitle3':item.momBirthdayNum==3?'reminderTitle4':'reminderTitle5'" v-if="type=='mom'">{{ item.momBirthday }}</p>
                <p class="reminderTitle2" v-if="type=='baby'">{{ item.babyBirthday }}</p>
             </div>
               </div>
               <img src="../../../image/蛋糕 <EMAIL>" alt="" style="width: 50px;height: 50px;">
            </div>
            <div class="fgx"></div>
            <div class="reminderContent">
                <div class="reminderContent1">
                    <p class="reminderContent2">妈妈姓名</p>
                    <p class="reminderContent3">{{ item.name }}</p>
                </div>
                <div class="reminderContent4">
                    <p class="reminderContent2">年龄</p>
                    <p class="reminderContent3">{{ item.momAge }}</p>
                </div>
            </div>
            <div class="reminderContent">
                <div class="reminderContent1">
                    <p class="reminderContent2">宝宝姓名</p>
                    <p class="reminderContent3">{{ item.babyName }}</p>
                </div>
                <div class="reminderContent4">
                    <p class="reminderContent2">宝宝性别</p>
                    <p class="reminderContent3">{{ item.babyGender }}</p>
                </div>
            </div>
            <div>
                <div class="reminderContent4">
                    <p class="reminderContent2">手机号码</p>
                    <p class="reminderContent3">{{ item.tel }}</p>
                </div>
            </div>
        </div>
     </div>
    </div>
</div>
</template>

<script>
import { list } from "@/api/platform/birthdayReminder";
export default {
    name: "app",
  data() {
    return {
        loading:false,
        type: 'mom',
      ruleForm:{
      },
      listData:[],
      colors: [
          {color: '#F84343', percentage: 3},
          {color: '#FF6C11', percentage: 6},
          {color: '#0BBD71', percentage: 9},
        ]
    }
},
created(){
  this.getList()
},
methods:{
    handleClick(){//查询
        this.getList()
    },
      getList(){//列表
        this.loading=true
        list(this.type).then(res => {
                if(res.code==200){
                    this.$message(res.msg);
                    this.listData=res.data
                    this.total=res.total
                    this.loading=false
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
      },
      format(percentage){
                return percentage+'天'
      }
}
}
</script>

<style scoped lang="scss">
.app{
    background:#F0F1F5;
    padding-bottom: 30px;
}
.head{
    width: 100%;
    height: 80px;
    background: #ffff;
    color: #17191A;
    padding: 15px 20px;
    .titles{
        font-size: 22px;
        font-weight: bold;
    }
    .title1{
        font-size: 14px;
    }
}
.content{
    margin: 20px 20px;
    .contentHead{
    background: #ffff;
    padding: 5px 14px;
    border-radius: 10px;
    .title2{
        color: #17191A;
    }
}
}
.contentHeads{
    display: flex;
}
.roomMessage{
    display: flex;
}
.sel{
    display: flex;
    align-items: center;
    margin-right: 10px;
    .selTitle{
        width: 30%;
    font-size: 14px;
    color: #606266;
    box-sizing: border-box;
    font-weight: bold;
}
}
#cars{
    border: 1px solid #DCDCDC;
    border-radius: 3px;
    color: #7C7D81;
    font-size: 14px;
    width: 179px;
    height: 32px;
    margin-left: 6px;
}
.block{
    text-align: right;
    margin-top: 20px;
}
.reminders{
    display: flex;
    flex-flow: wrap;
}
.reminder1{
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.reminder{
    padding: 15px 15px;
    background: #ffff;
    border-radius: 10px;
    width: 280px;
    margin-top: 20px;
    margin-right: 20px;
  
}
.reminderHeader{
    display: flex;
    font-size: 14px;
    line-height: 0;

}
.reminderTitle{
    margin-left: 14px;
}
.reminderTitle1{
    color: #7C7D81;
}
.reminderTitle2{
    color: #0BBD71;
    padding-top: 10px;

}
.reminderTitle3{
    color: #FF6C11;
    padding-top: 10px;
}
.reminderTitle5{
    padding-top: 10px;
}
.reminderTitle4{
    color: #F84343;
    padding-top: 10px;
}
.fgx{
    width: 100%;
    height: 1px;
    background: #EBEBEB;
    margin: 14px 0 20px 0;
}
.reminderContent{
    display: flex;
    line-height: 0;
    font-size: 14px;
}
.reminderContent1{
    display: flex;
  align-items: center;
  width: 65%;

}
.reminderContent2{
    color: #7C7D81;
}
.reminderContent3{
    margin-left: 5px;
    color: #17191A;
}
.reminderContent4{
    line-height: 0;
    display: flex;
    align-items: center;
    font-size: 14px;
}
.showProgressColor{
}
</style>