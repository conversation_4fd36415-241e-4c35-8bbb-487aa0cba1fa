<template>
  <div class="app" v-loading="loading">
    <!-- <div class="head">
            <div class="title">护理人员配置</div>
            <div class="title1">Pages/护理人员配置</div>
        </div> -->
    <div class="content">
      <div class="title2">护理人员</div>
      <el-button type="primary" @click="addMaternitySuite">新增人员</el-button>
      <el-table :data="listData" class="table">
        <el-table-column
          label="人员照片"
          prop="name"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            <img
              :src="scope.row.staffPhotos[0]"
              min-width="70"
              height="70"
              style="width: 100px; height: 100px; border-radius: 5px"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="电话号码"
          prop="staffTel"
          width="120"
          align="center"
        />
        <el-table-column
          label="人员ID"
          prop="staffId"
          :show-overflow-tooltip="true"
          width="150"
          align="center"
        />
        <el-table-column
          label="姓名"
          prop="staffName"
          :show-overflow-tooltip="true"
          width="150"
          align="center"
        >
        </el-table-column>
        <el-table-column
          label="职位"
          prop="staffPost"
          width="200"
          align="center"
        />
        <el-table-column
          label="从业开始年份"
          prop="practiceTime"
          width="200"
          align="center"
        />
        <el-table-column
          label="排序"
          prop="sortKey"
          width="120"
          align="center"
        />
        <el-table-column label="主页显示" align="center" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.isShow"
              :active-value="true"
              :inactive-value="false"
              @change="homepage(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="examine(scope.row)"
              >查看</el-button
            >
            <el-button size="mini" type="text" @click="compile(scope.row)"
              >编辑</el-button
            >
            <el-button
              size="mini"
              type="text"
              @click="bindEmployee(scope.row)"
              v-loading.fullscreen.lock="fullscreenLoading"
              >绑定员工</el-button
            >
            <el-button size="mini" type="text" @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog title="删除" :visible.sync="deleteDialogVisible" width="30%">
      <span>是否删除此护理人员配置？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
      </span>
    </el-dialog>
    <!--新增-->
    <el-dialog title="请选择新增人员的类型" :visible.sync="addShow" width="30%">
      <div class="staffType">
        <p
          :class="rentId == index ? 'active' : 'type'"
          @click="addRent(index, item)"
          v-for="(item, index) in addRentList"
          :key="index"
        >
          {{ item.name }}
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addShow = false">取 消</el-button>
        <el-button type="primary" @click="addRentConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="绑定员工" :visible.sync="bindEmployeeVisible">
      <el-select
        v-model="bindData.userId"
        placeholder="请选择员工"
        style="width: 100%"
      >
        <el-option
          :label="item.name"
          :value="item.userId"
          v-for="(item, index) in employeeList"
          :key="index"
        ></el-option>
      </el-select>
      <span slot="footer" class="dialog-footer">
        <el-button @click="bindEmployeeVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="bind"
          v-loading.fullscreen.lock="fullscreenLoading"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { page, delTable, online, homepage } from "@/api/platform/nursingStaff";
import { list as employeeList, bindEmployee } from "@/api/platform/employee";
export default {
  name: "app",
  data() {
    return {
      loading: false,
      staffPost: "",
      staffPostId: "",
      rentId: -1,
      addShow: false,
      total: 0,
      staffId: "",
      ruleForm: {
        pageSize: 10,
        pageNum: 1,
      },
      deleteDialogVisible: false,
      listData: [],
      addRentList: [
        {
          id: 1,
          name: "孕产厨师",
        },
        {
          id: 2,
          name: "孕产医师",
        },
        {
          id: 3,
          name: "孕产护士",
        },
        {
          id: 4,
          name: "孕产月嫂",
        },
        {
          id: 5,
          name: "孕产育婴员",
        },
        {
          id: 6,
          name: "孕产心理咨询师",
        },
        {
          id: 7,
          name: "孕产产后康复师",
        },
        {
          id: 8,
          name: "孕产健康管理师",
        },
        {
          id: 9,
          name: "孕产母婴护理师",
        },
        {
          id: 10,
          name: "母婴顾问",
        },
        {
          id: 11,
          name: "店助",
        },
        {
          id: 12,
          name: "顾问主管",
        },
        {
          id: 13,
          name: "店长",
        },
        {
          id: 14,
          name: "前台客服",
        },
        {
          id: 15,
          name: "产康主管",
        },
        {
          id: 16,
          name: "护士长",
        },
        {
          id: 17,
          name: "护士组长",
        },
        {
          id: 18,
          name: "育婴师组长",
        },
        {
          id: 19,
          name: "产康顾问",
        },
        {
          id: 20,
          name: "药浴师",
        },
        {
          id: 21,
          name: "厨师长",
        },
        {
          id: 22,
          name: "护理主任",
        },
        {
          id: 23,
          name: "月子管家",
        },
        {
          id: 24,
          name: "营养师",
        },
        {
          id: 25,
          name: "妇科医生",
        },
        {
          id: 26,
          name: "儿科医生",
        },
      ],
      employeeList: [],
      bindEmployeeVisible: false,
      bindData: {
        staffId: undefined,
        userId: undefined,
      },
      fullscreenLoading: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val;
      this.getList();
    },
    handleDelete(row) {
      this.staffId = row.staffId;
      this.deleteDialogVisible = true;
    },
    homepage(e) {
      //改变护理人员主页显示状态
      console.log(e);
      let data = {
        staffId: e.staffId,
        status: e.isShow,
      };
      homepage(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    confirmDelete() {
      //确定删除
      delTable(this.staffId).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.deleteDialogVisible = false;
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    addMaternitySuite() {
      //新增房型
      this.addShow = true;
    },
    getList() {
      //列表
      this.loading = true;
      page(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.rows;
          this.total = res.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    examine(row) {
      //查看
      this.$router.push({
        path: "/addnursingStaff",
        query: {
          staffId: row.staffId,
          forbidden: true,
        },
      });
    },
    compile(row) {
      //编辑
      this.$router.push({
        path: "/addnursingStaff",
        query: {
          staffId: row.staffId,
        },
      });
    },
    addRent(index, item) {
      this.rentId = index;
      this.staffPost = item.name;
      this.staffPostId = item.id;
    },
    addRentConfirm() {
      this.$router.push({
        path: "/addnursingStaff",
        query: {
          staffPost: this.staffPost,
          staffPostId: this.staffPostId,
        },
      });
    },
    bindEmployee(row) {
      this.fullscreenLoading = true;
      this.bindData = {
        staffId: undefined,
        userId: undefined,
      };
      employeeList().then((res) => {
        this.employeeList = res.data;
        this.bindEmployeeVisible = true;
        this.bindData.staffId = row.staffId;
        this.fullscreenLoading = false;
      });
    },
    bind() {
      this.fullscreenLoading = true;
      bindEmployee(this.bindData)
        .then((res) => {
          this.$modal.msgSuccess("绑定成功");
        })
        .finally(() => {
          this.bindEmployeeVisible = false;
          this.fullscreenLoading = false;
        });
    },
  },
};
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;

  .title {
    font-size: 22px;
    font-weight: bold;
  }

  .title1 {
    font-size: 14px;
  }
}

.content {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
  padding: 24px 20px;

  .title2 {
    font-size: 20px;
    color: #17191a;
    margin-bottom: 24px;
  }

  .table {
    margin-top: 20px;
  }
}

.block {
  text-align: right;
  margin-top: 20px;
}

.staffType {
  display: flex;
  flex-wrap: wrap;

  .type {
    border-radius: 34px;
    border: 1px solid #ebebeb;
    color: #17191a;
    font-size: 14px;
    padding: 5px 10px;
    margin-right: 8px;
  }
}

.active {
  border-radius: 34px;
  color: #ffff;
  font-size: 14px;
  padding: 5px 10px;
  margin-right: 8px;
  background: #3f85ff;
}
</style>
