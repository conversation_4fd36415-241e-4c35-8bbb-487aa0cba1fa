<template>
    <div class="app-container home" id="app">
        <div class="dataStatistics">
            <div class="dataStatistics1">
            <div class="dataStatistics1_1">浏览总时间数据分析</div>
            <div class="dataStatistics2">
            </div>
          </div>
        </div>
          <div class="expectedDate">
             <div class="expectedDate1">
                <p class="distribution1_1">预产期时间占比</p>
                <div class="expectedDate1_1">
                    <div :class="tabIndex==index?'active':'expectedDate1_2'" v-for="item,index in list" :key="index" @click="tab(index)">
                        <p class="expectedDate1_3">{{item.monthRange}}</p>
                        <p>
                            <span class="expectedDate1_4">{{item.userCount}}</span><span class="expectedDate1_5">人</span>
                        </p>
                    </div>
                </div>
             </div>
             <div class="expectedDate2">
                <p class="distribution1_1">预产期时间占比</p>
                <!-- <div class="parameters">
                    <div class="parameter">
                        <p class="dot"></p>
                        <p class="name">房间</p>
                        <p class="time">2小时15分钟</p>
                    </div>
                    <div class="parameter">
                        <p class="dot"></p>
                        <p class="name">膳食</p>
                        <p class="time">2小时10分钟</p>
                    </div>
                </div>
                <div class="parameters">
                    <div class="parameter">
                        <p class="dot"></p>
                        <p class="name">护理团队</p>
                        <p class="time">2小时5分钟</p>
                    </div>
                    <div class="parameter">
                        <p class="dot"></p>
                        <p class="name">产康</p>
                        <p class="time">2小时</p>
                    </div>
                </div>
                <div class="parameters">
                    <div class="parameter">
                        <p class="dot"></p>
                        <p class="name">社区</p>
                        <p class="time">2小时15分钟</p>
                    </div>
                    <div class="parameter">
                        <p class="dot"></p>
                        <p class="name">评价</p>
                        <p class="time">2小时15分钟</p>
                    </div>
                </div> -->
                    <div id="chartLineBox" style=" width:660px;
            height: 370px;"> </div>
             </div>
          </div>
        <div class="roomModuleAnalysis">
            <p class="distribution1_1">预产期人数</p>
            <div id="accessTime" style="width:100%;height:349px"> </div>
        </div>
        <div class="table">
     <p class="distribution1_1">预产期用户列表</p>
     <div>
     </div>
     <div class="content">
        <!-- <div class="tableInp">
            <div class="ageInp">
                <p>用户姓名</p>
                <el-input v-model="age"></el-input>
            </div>
            <div class="ageInp">
                <p>电话号码</p>
                <el-input v-model="age"></el-input>
            </div>
            <div class="ageInp">
                <p>状态</p>
                <el-select v-model="value" placeholder="请选择">
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value">
    </el-option>
  </el-select>
            </div>
        </div> -->
           <el-table :data="analysePage" class="table">
    <el-table-column label="用户姓名" prop="nickname" :show-overflow-tooltip="true" width="150" align="center" />
    <el-table-column label="电话号码" prop="tel" :show-overflow-tooltip="true" width="150" align="center" >
  </el-table-column>
    <el-table-column label="状态" prop="productDescription" width="200" align="center" >
      <template slot-scope="scope">
        <p v-if="scope.row.type=='new'"> 新用户</p>
        <p v-if="scope.row.type=='old'"> 老用户</p>
      </template>
    </el-table-column>
    <el-table-column label="预产期" prop="dueDate" width="200" align="center" />
    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
      <template slot-scope="scope">
        <el-button
          size="mini"
          type="text"
          @click="details(scope.row)"
        >详情</el-button>
      </template>
    </el-table-column>
  </el-table>
  <div class="block">
  <el-pagination
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
    :page-sizes="[10, 20, 30, 40]"
    :page-size="100"
    layout="total, sizes, prev, pager, next, jumper"
    :total="total">
  </el-pagination>
</div>
      </div>
  </div>
    </div>
  </template>
  
  <script>
    import {dueList,analysePage} from "@/api/platform/index";
  import * as echarts from 'echarts'
  export default {
    name: "Index",
    data() {
      return {
        optionsType:[
        {
                value:'new',
                label:'新用户'
            },
            {
                value:'old',
                label:'老用户'
            }
        ],
        options:[
            {
                value:0,
                label:'男'
            },
            {
                value:1,
                label:'女'
            }
        ],
        from:{
        pageSize:10,
        pageNum:1,
        },
        analysePage:[],
        total:0,
        listData:'',
        tabIndex:-1,
        list:[
          {
            time:'1~3',
            number:'2000'
          },
          {
            time:'4~6',
            number:'3000'
          },
          {
            time:'7~10',
            number:'5000'
          },
          {
            time:'备孕中',
            number:'2000'
          },
        ],
        age:'',
        options:[],

        listData:[
            {
                productName:'1'
            }
        ],
        // 版本号
        version: "3.8.7",
        value1:'',
        userCount:[],
        monthRange:[],
        LineBox:[
        ]
      };
    },
    created(){
     this.statsDues()
     this.analysePages()
    },
    methods: {
      analysePages(){
        analysePage(this.from).then(res => {
                if(res.code==200){
                    this.analysePage=res.rows
                    this.total=res.total
                    this.$message(res.msg);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
      },
      handleSizeChange(val) {
      this.from.pageSize=val
      this.analysePages()
      },
      handleCurrentChange(val) {
        this.from.pageNum=val
        this.analysePages()
      },
      statsDues(){//查询
        dueList().then(res => {
                if(res.code==200){
                    this.list=res.data
                    let monthRange=[]
                    let userCount=[]
                    let LineBox=[]
                    res.data.forEach(item => {
                      monthRange.push(item.monthRange)
                      userCount.push(item.userCount)
                      let data={
                        value:item.userCount,
                        name:item.monthRange
                      }
                      LineBox.push(data)
                    });
                    this.LineBox=LineBox
                    this.monthRange=monthRange
                    this.userCount=userCount
                    this.accessTime()
                    this.chartLineBox()
                    this.$message(res.msg);
                return 
                }else{
                    this.$message(res.msg); 
                }
            });
        },
      tab(index){
          this.tabIndex=index
      },
        details(row){//详情
            this.$router.push({
                        path: "/perinatalDataDetails", query: {
                          row:JSON.stringify(row)
                        },
                    });
        },
      goTarget(href) {
        window.open(href, "_blank");
      },
      chartLineBox(){
        this.chartLine = echarts.init(document.getElementById('chartLineBox'));
          // 指定图表的配置项和数据
          var option = {
            title:{
            text:'',
            top:'bottom',
            left:'center',
            textStyle:{
                fontSize: 14,
                fontWeight: '',
                color: '#333'
            },
        },//标题
        tooltip: {
            trigger: 'item',
            formatter: "{a} <br/>{b}: {c} ({d}%)"
            /*formatter:function(val){   //让series 中的文字进行换行
                 console.log(val);//查看val属性，可根据里边属性自定义内容
                 var content = var['name'];
                 return content;//返回可以含有html中标签
             },*/ //自定义鼠标悬浮交互信息提示，鼠标放在饼状图上时触发事件
        },//提示框，鼠标悬浮交互时的信息提示
        legend: {
            show: true,
            orient: 'vertical',
            x: 'center',
            left:160,
            top:140,
            data: this.LineBox
        },//图例属性，以饼状图为例，用来说明饼状图每个扇区，data与下边series中data相匹配
        graphic:{
            type:'text',
            left:'center',
            top:'center',
            style:{
                text:'', //使用“+”可以使每行文字居中
                textAlign:'center',
                font:'italic bolder 16px cursive',
                fill:'#000',
                width:30,
                height:30
            }
        },//此例饼状图为圆环中心文字显示属性，这是一个原生图形元素组件，功能很多
        series: [
            {
                name:'',//tooltip提示框中显示内容
                type: 'pie',//图形类型，如饼状图，柱状图等
                radius: ['15%', '35%'],//饼图的半径，数组的第一项是内半径，第二项是外半径。支持百分比，本例设置成环形图。具体可以看文档或改变其值试一试
                //roseType:'area',是否显示成南丁格尔图，默认false
                itemStyle: {
                    normal:{
                        label:{
                            show:true,
                            textStyle:{color:'#3c4858',fontSize:"10"},
                            formatter:function(val){   //让series 中的文字进行换行
                                return val.name.split("-").join("\n");}
                        },//饼图图形上的文本标签，可用于说明图形的一些数据信息，比如值，名称等。可以与itemStyle属性同级，具体看文档
                        labelLine:{
                            show:true,
                            lineStyle:{color:'#3c4858'}
                        }//线条颜色
                    },//基本样式
                    emphasis: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)',//鼠标放在区域边框颜色
                        textColor:'#000'
                    }//鼠标放在各个区域的样式
                },
                data: this.LineBox,//数据，数据中其他属性，查阅文档
                color: ['#3F85FF','#2C57FF','#722FD1','#2AD98A','#9FDB1D','#FFC439'],//各个区域颜色
            },//数组中一个{}元素，一个图，以此可以做出环形图
        ],//系列列表
    };
   
          // 使用刚指定的配置项和数据显示图表。
          this.chartLine.setOption(option);
      },
      accessTime(){
        this.chartLine = echarts.init(document.getElementById('accessTime'));
          // 指定图表的配置项和数据
          var option = {
              tooltip: {              //设置tip提示
                  trigger: 'axis'
              },
              color: ['#3058FF', '#3F85FF'],       //设置区分（每条线是什么颜色，和 legend 一一对应）
              xAxis: {
      type: 'category',
      boundaryGap: false,
      data: this.monthRange
    },
    yAxis: {
      type: 'value'
    },
              series: [
              {
        data: this.userCount,
        type: 'line',
        areaStyle: {},
        itemStyle: {  
                normal: { //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1,[{
                            offset: 0.2, color: '#3F85FF' // 0% 处的颜色
                        }, {
                            offset: 0.3, color: '#3F85FF' // 100% 处的颜色
                        }, {
                            offset: 1, color: '#fff' // 100% 处的颜色
                        }]
                    ), //背景渐变色    
                    lineStyle: {        // 系列级个性化折线样式  
                        width: 2,  
                        type: 'solid',  
                        color: "#3F85FF" //折线的颜色
                    }  
                },  
    },
            // 设置折线弧度，取值：0-1之间
            smooth: 0.5,
      }
            ]
          };
   
          // 使用刚指定的配置项和数据显示图表。
          this.chartLine.setOption(option);
      }
    },
    mounted(){
     this.accessTime()
     this.chartLineBox()
    }
  };
  </script>
  
  <style scoped lang="scss">
  #app{
    background: #F0F1F5;
      padding-bottom: 40px;
      padding: 20px 20px;
  }
  .dataStatistics{
    background: #fff;
    padding: 20px 20px;
    width: 100%;
    border-radius: 10px;
    .dataStatistics1{
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 0;
      .dataStatistics1_1{
        color: #17191A;
        font-size: 20px;
      }
    }
    .dataStatistics2{
      display: flex;
      align-items: center;
      .dataStatistics2_1{
        display: flex;
        list-style-type: none;
        li{
          width: 38px;
          height: 25px;
          border: 1px solid #C8C9CD;
          color: #C8C9CD;
          text-align: center;
          line-height: 25px;
          border-radius: 2px;
          margin-right: 12px;
        }
      }
      .dataStatistics2_2{
        margin-right: 12px;
      }
    }
}
.table{
    margin-top: 20px;
    background: #fff;
      padding: 20px 20px;
      border-radius: 10px;
      width: 100%;
  }
  .block{
  text-align: right;
  margin-top: 20px;
}

.roomModule{
    display: flex;
    .roomModule1{
        width: 49.5%;
        border: 1px solid #D9D9D9;
        border-radius: 5px;
        padding: 16px 12px 28px 12px;
        .name{
            color: #1D2129;
            font-size: 16px;
        }
        .num{
           color: #17191A;
           font-size: 36px; 
           font-weight: bold;
        }
        .unit{
            color: #86909C;
            font-size: 12px;
            margin-left: 10px;
        }
    }
    .roomModule2{
        width: 49.5%;
        border: 1px solid #D9D9D9;
        border-radius: 5px;
        padding: 16px 12px 28px 12px;
        margin-left: 1%;
        .name{
            color: #1D2129;
            font-size: 16px;
        }
        .num{
           color: #17191A;
           font-size: 36px; 
           font-weight: bold;
        }
        .unit{
            color: #86909C;
            font-size: 12px;
            margin-left: 10px;
        }
    }
}
.roomModuleAnalysis{
    background: #fff;
    border-radius: 10px;
    padding: 20px 20px 60px 20px;
    margin-top: 20px;
    line-height: 0;
}
.distribution1_1{
  line-height: 0;
    font-size: 18px;
    color: #17191A;
}
.tableInp{
    display: flex;
  .ageInp{
    display: flex;
    align-items: center;
    margin-left: 10px;
    p{
        width: 30%;
    }
  }
}
.expectedDate{
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    .expectedDate1{
        width: 74%;
        background: #fff;
        padding: 20px 20px;
        border-radius: 12px;
        .expectedDate1_1{
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            .active{
              border: 1px solid #3F85FF;
                border-radius: 5px;
                width: 24%;
                padding: 0 20px;
                background: #E3EDFF;
                .expectedDate1_3{
                    color: #1D2129;
                    font-size: 16px;
                }
                .expectedDate1_4{
                    color: #17191A;
                    font-size: 36px;
                }
                .expectedDate1_5{
                    color: #86909C;
                    font-size: 12px;
                }
            }
            .expectedDate1_2{
                border: 1px solid #D9D9D9;
                border-radius: 5px;
                width: 24%;
                padding: 0 20px;
                height: 123px;
                .expectedDate1_3{
                    color: #1D2129;
                    font-size: 16px;
                }
                .expectedDate1_4{
                    color: #17191A;
                    font-size: 36px;
                }
                .expectedDate1_5{
                    color: #86909C;
                    font-size: 12px;
                }
            }
            
        }
    }
    .expectedDate2{
        width: 25%;
        background: #fff;
        padding: 20px 20px;
        border-radius: 12px;
        position: relative;
        #chartLineBox{
   position:absolute;
   left: -30%;
   top: -80px;
}
    }
}

.parameters{
    display: flex;
    justify-content: center;
    width: 100%;
.parameter{
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 0;
    width: 100%;
    .dot{
      width: 7px;
      height: 7px;
      border-radius: 50px;
      background: #3F85FF;
    }
    .name{
     color: #45464A;
     margin-left: 6px;
    }
    .time{
      color: #000000;
      margin-left: 15px;
    }
}
}
  </style>
  
   