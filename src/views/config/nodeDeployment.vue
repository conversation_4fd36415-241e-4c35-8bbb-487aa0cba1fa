<template>
  <div class="app" v-loading="loading">
    <!-- <div class="head">
            <div class="title">签到管理</div>
            <div class="title1">Pages/签到管理</div>
        </div> -->
    <div class="content">
      <div class="title2">标签配置</div>
      <el-button type="primary" @click="addCheckManagement">新增标签</el-button>
      <el-table :data="listData" class="table">
        <el-table-column
          label="序号"
          prop="description"
          :show-overflow-tooltip="true"
          align="center"
        >
          <template scope="scope">
            <span v-text="scope.$index + 1"></span>
          </template>
        </el-table-column>
        <el-table-column label="角色" prop="roleName" align="center" />
        <el-table-column label="类型" prop="typeName" align="center" />
        <el-table-column label="标签名称" prop="nodeName" align="center" />
        <el-table-column label="是否精选节点" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.isFeatured"
              :active-value="true"
              :inactive-value="false"
              @change="homepage(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope" v-if="scope.row.roleId !== 1">
            <el-button size="mini" type="text" @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <!-- <el-button
            size="mini"
            type="text"
            @click="clickOnline(scope.row)"
            v-if="!scope.row.onlineStatus"
          >上线</el-button>
          <el-button
            size="mini"
            type="text"
            @click="showing(scope.row)"
            v-if="scope.row.onlineStatus"
          >下线</el-button> -->
            <el-button size="mini" type="text" @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <!--新增-->
    <el-dialog title="新增" :visible.sync="addShow" width="30%">
      <el-form
        :model="from"
        :rules="rules"
        ref="from"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item
          label="角色"
          :label-width="formLabelWidth"
          prop="nurseRole"
        >
          <el-select v-model="from.nurseRole" placeholder="请选择角色">
            <el-option
              :label="item.dictLabel"
              :value="item.dictValue"
              v-for="(item, index) in topicStatus"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="类型"
          :label-width="formLabelWidth"
          prop="nurseType"
        >
          <el-select v-model="from.nurseType" placeholder="请选择类型">
            <el-option
              :label="item.dictLabel"
              :value="item.dictValue"
              v-for="(item, index) in topicStatus1"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标签名称" prop="nodeName">
          <el-input
            v-model="from.nodeName"
            placeholder="请输入标签名称"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="礼品描述" prop="description">
    <el-input type="textarea" v-model="from.description" placeholder="请输入礼品描述"></el-input>
  </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addShow = false">取 消</el-button>
        <el-button type="primary" @click="submitForm('from')">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="删除" :visible.sync="deleteDialogVisible" width="30%">
      <span>是否删除此节点配置？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDelete">确 定 删 除</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  save,
  delTable,
  changeFeatured,
  update,
} from "@/api/platform/nodeDeployment";
import ImageUpload from "@/components/ImageUpload/index";
export default {
  name: "app",
  components: { ImageUpload },
  data() {
    return {
      typeIf: "",
      formLabelWidth: "100px",
      topicStatus1: [
        //话题状态
        {
          dictLabel: "产后康复",
          dictValue: "CHKF",
        },
        {
          dictLabel: "月子膳食",
          dictValue: "YZSS",
        },
        {
          dictLabel: "护理服务",
          dictValue: "HLFW",
        },
        {
          dictLabel: "其他服务",
          dictValue: "QTFW",
        },
      ],
      topicStatus: [
        //话题状态
        {
          dictLabel: "护理",
          dictValue: "NURSE",
        },
        {
          dictLabel: "产康",
          dictValue: "POSTPARTUM",
        },
        {
          dictLabel: "厨师",
          dictValue: "CHEF",
        },
        {
          dictLabel: "月嫂",
          dictValue: "MATERNITY_NANNY",
        },
        {
          dictLabel: "销售",
          dictValue: "SALES",
        },
      ],
      listData: [],
      loading: false,
      nowAddress: [], //临时保存地址
      fixedAddress: "https://txyoss.oss-cn-shenzhen.aliyuncs.com",
      currentIndex: "", //当前图片的下标
      fileCount: 0,
      picNum: 0,
      maxNum: 5,
      photoList: [], //照片列表
      dialogImageUrl: "",
      dialogVisible: false,
      disabled: false,
      total: 0,
      ruleForm: {
        pageSize: 10,
        pageNum: 1,
      },
      from: {
        nodeName: "",
        nurseRole: "",
        nurseType: "",
      },
      addShow: false,
      deleteDialogVisible: false,
      taskNodeId: "",
      rules: {
        nodeName: [
          { required: true, message: "请填写标签名称", trigger: "blur" },
        ],
        nurseRole: [{ required: true, message: "请选择角色", trigger: "blur" }],
        nurseType: [{ required: true, message: "请选择类型", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val;
      this.getList();
    },
    handleDelete(row) {
      //删除
      this.taskNodeId = row.taskNodeId;
      this.deleteDialogVisible = true;
    },
    confirmDelete() {
      //确定删除
      delTable(this.taskNodeId).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.deleteDialogVisible = false;
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    addCheckManagement() {
      //增加标签
      this.typeIf = 1;
      this.addShow = !this.addShow;
    },
    handleUpdate(row) {
      //修改标签
      this.typeIf = 2;
      this.from = {
        nodeName: row.nodeName,
        nurseRole: row.nurseRole,
        nurseType: row.nurseType,
        taskNodeId: row.taskNodeId,
      };
      this.addShow = !this.addShow;
    },
    getList() {
      //列表
      this.loading = true;
      page(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.listData = res.rows;
          this.total = res.total;
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    submitForm(formName) {
      if (this.typeIf == 1) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.$confirm("确定新增该标签吗, 是否继续?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                save(this.from).then((res) => {
                  if (res.code == 200) {
                    this.$message(res.msg);
                    this.addShow = false;
                    this.getList();
                    this.from = {
                      nodeName: "",
                      nurseRole: "",
                      nurseType: "",
                    };
                    return;
                  } else {
                    this.$message(res.msg);
                  }
                });
              })
              .catch(() => {
                this.$message({
                  type: "info",
                  message: "已取消新增",
                });
              });
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      }
      if (this.typeIf == 2) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.$confirm("确定修改该标签吗, 是否继续?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                update(this.from).then((res) => {
                  if (res.code == 200) {
                    this.$message(res.msg);
                    this.addShow = false;
                    this.getList();
                    this.from = {
                      nodeName: "",
                      nurseRole: "",
                      nurseType: "",
                      taskNodeId: "",
                    };
                    return;
                  } else {
                    this.$message(res.msg);
                  }
                });
              })
              .catch(() => {
                this.$message({
                  type: "info",
                  message: "已取消修改",
                });
              });
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      }
    },
    homepage(row) {
      let data = {
        taskNodeId: row.taskNodeId,
        isFeatured: row.isFeatured,
      };
      changeFeatured(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getList();
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.head {
  width: 100%;
  height: 80px;
  background: #ffff;
  color: #17191a;
  padding: 15px 20px;
  .title {
    font-size: 22px;
    font-weight: bold;
  }
  .title1 {
    font-size: 14px;
  }
}
.content {
  background: #ffff;
  margin: 10px 10px;
  border-radius: 5px;
  padding: 24px 20px;
  .title2 {
    font-size: 20px;
    color: #17191a;
    margin-bottom: 24px;
  }
  .table {
    margin-top: 20px;
  }
}
.block {
  text-align: right;
  margin-top: 20px;
}
</style>
