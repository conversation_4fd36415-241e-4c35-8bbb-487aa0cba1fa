<template>
    <div class="app-container home" id="app">
        <div  class="userDetails">
            <p class="userDetailsTitle">用户详情</p>
            <p class="fgx"></p>
            <div class="userDetails2">
                <div class="userDetails1">
                    <p class="userDetails1_1">用户姓名</p>
                    <p class="userDetails1_2">{{row.nickname}}</p>
                </div>
                <div class="userDetails1">
                    <p class="userDetails1_1">电话号码</p>
                    <p class="userDetails1_2">{{row.tel}}</p>
                </div>
            </div>
            <div class="userDetails2">
                <div class="userDetails1">
                    <p class="userDetails1_1">状态</p>
                    <p class="userDetails1_2">{{row.type=='new'?'新用户':'老用户'}}</p>
                </div>
                <div class="userDetails1">
                    <p class="userDetails1_1">预产期</p>
                    <p class="userDetails1_2">{{row.dueDate}}</p>
                </div>
            </div>
        </div>
    </div>
  </template>
  
  <script>
  import * as echarts from 'echarts'
  export default {
    name: "Index",
    data() {
      return {
        row:'',
        activeName:'1',
        aa:2,
        // 版本号
        version: "3.8.7",
        type:this.$route.query.type
      };
    },
    created(){
      this.row=JSON.parse(this.$route.query.row) 
    },
    methods: {
      goTarget(href) {
        window.open(href, "_blank");
      }
    },
    mounted(){
    }
  };
  </script>
  
  <style scoped lang="scss">
  #app{
    background: #F0F1F5;
      padding-bottom: 40px;
      padding: 20px 20px;
  }
  .userDetails{
    background: #fff;
    border-radius: 10px;
    padding: 20px 20px;
    .userDetailsTitle{
        font-size: 20px;
        color: #17191A;
    }
    .fgx{
        height: 2px;
        width: 100%;
        background: #EBEBEB;
    }
    .userDetails2{
        display: flex;
        justify-content: space-between;
        width: 100%;
        .userDetails1{
            width: 50%;
        display: flex;
        font-size: 14px;
        .userDetails1_1{
            width: 50%;
            color: #A9AAAE;
        }
        .userDetails1_2{
            width: 50%;
            color: #17191A;
        }
    }
    }
  
  }
  
  </style>
  
  