<template>
  <div class="app-container home" id="app">
    <div class="head">
      <div class="headTitle">{{ clubInfo.clubName }}</div>
      <div class="headInp">
        <el-input
          v-model="input"
          placeholder="请输入券码进行验券"
          clear="Inp"
        ></el-input>
        <el-button type="primary" class="autopsy">验券</el-button>
      </div>
    </div>
    <div class="function">
      <div class="commonlyUsed">
        <p class="commonlyUsed1">常用功能</p>
        <div class="commonlyUsed3">
          <div @click="next(1)">
            <img
              src="http://cdn.xiaodingdang1.com/2024/04/15/713a780b695c46e1b7c3a9abe2473c7dpng"
              alt=""
              style="width: 50px; height: 50px"
            />
            <p class="commonlyUsed2">店铺装修</p>
          </div>
          <div @click="next(2)">
            <img
              src="http://cdn.xiaodingdang1.com/2024/04/26/66b0c490dff6410aa9602d47ffab3e9bpng"
              alt=""
              style="width: 50px; height: 50px"
            />
            <p class="commonlyUsed2">签到管理</p>
          </div>
          <div @click="next(3)">
            <img
              src="http://cdn.xiaodingdang1.com/2024/04/15/24c78ffcc1304f41a97318f894b8827fpng"
              alt=""
              style="width: 50px; height: 50px"
            />
            <p class="commonlyUsed2">客资中心</p>
          </div>
          <div @click="next(4)">
            <img
              src="http://cdn.xiaodingdang1.com/2024/04/26/433e6ef73b5e4e23a03f6ae7411f048bpng"
              alt=""
              style="width: 50px; height: 50px"
            />
            <p class="commonlyUsed2">膳食配送</p>
          </div>
          <div @click="next(5)">
            <img
              src="http://cdn.xiaodingdang1.com/2024/04/26/7e05e3b3d7954a6dbecadeae5e9f2325png"
              alt=""
              style="width: 50px; height: 50px"
            />
            <p class="commonlyUsed2">移动月嫂</p>
          </div>
        </div>
      </div>
      <div class="evaluationManagement">
        <div class="evaluationManagement1">
          <p class="evaluationManagement1_1">评价管理</p>
          <p class="evaluationManagement1_2" @click="viewMore(1)">查看更多</p>
        </div>
        <p class="evaluationManagement2">最近7天</p>
        <div class="evaluationManagement3">
          <img
            src="http://cdn.xiaodingdang1.com/2024/04/15/a514b7d6f0014d77bb3b28e8467148a4png"
            alt=""
            style="width: 60px; height: 60px"
          />
          <div class="evaluationManagement3_1">
            <p class="evaluationManagement3_2">新增评论数</p>
            <p class="evaluationManagement3_3">{{ newStats }}个</p>
          </div>
        </div>
      </div>
    </div>
    <div class="dataStatistics">
      <div class="dataStatistics1">
        <div class="dataStatistics1_1">数据统计</div>
        <div class="dataStatistics2">
          <ul class="dataStatistics2_1">
            <li
              v-for="(item, index) in tabList"
              :key="index"
              :class="tabIndex == index ? 'tabActive' : 'tab'"
              @click="tab(index)"
            >
              {{ item }}
            </li>
          </ul>
          <el-date-picker
            v-model="startDate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择开始时间"
            class="dataStatistics2_2"
          >
          </el-date-picker>
          <el-date-picker
            v-model="endDate"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择结束时间"
            class="dataStatistics2_2"
          >
          </el-date-picker>
          <el-button type="primary" @click="inquire">查询</el-button>
          <p class="dataStatistics2_3" @click="viewMore(2)">查看更多</p>
        </div>
      </div>

      <div class="dataStatistics3">
        <div class="dataStatistics3_1">
          <div class="dataStatistics3_1_1">
            <div class="dataStatistics3_1_2">访客数</div>
            <el-tooltip
              class="item"
              effect="dark"
              content="一天之内到底有多少不同的用户访问了你的小程序。"
              placement="top"
            >
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/fe3baf5baa76452d92c457eeda2c8686png"
                alt=""
                style="width: 16px; height: 16px; margin-left: 3px"
              />
            </el-tooltip>
          </div>
          <p class="dataStatistics3_2">{{ listData.uv }}</p>
          <div class="dataStatistics3_3">
            <p class="dataStatistics3_3_1">较前一日</p>
            <div class="dataStatistics3_3_2">
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png"
                alt=""
                style="width: 8px; height: 16px"
                v-if="listData.pvDayBeforeType == 0"
              />
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png"
                alt=""
                style="width: 8px; height: 16px"
                v-if="listData.pvDayBeforeType == 1"
              />
              <p
                class="dataStatistics3_3_3"
                v-if="listData.pvDayBeforeType == 0"
              >
                {{ listData.pvDayBeforeRate }}人
              </p>
              <p id="dataStatistics3_3_3" v-if="listData.pvDayBeforeType == 1">
                {{ listData.pvDayBeforeRate }}人
              </p>
            </div>
          </div>
        </div>
        <div class="dataStatistics3_1">
          <div class="dataStatistics3_1_1">
            <div class="dataStatistics3_1_2">浏览量</div>
            <el-tooltip
              class="item"
              effect="dark"
              content="小程序页面的浏览次数"
              placement="top"
            >
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/fe3baf5baa76452d92c457eeda2c8686png"
                alt=""
                style="width: 16px; height: 16px; margin-left: 3px"
              />
            </el-tooltip>
          </div>
          <p class="dataStatistics3_2">{{ listData.pv }}</p>
          <div class="dataStatistics3_3">
            <p class="dataStatistics3_3_1">较前一日</p>
            <div class="dataStatistics3_3_2">
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png"
                alt=""
                style="width: 8px; height: 16px"
                v-if="listData.pvDayBeforeType == 0"
              />
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png"
                alt=""
                style="width: 8px; height: 16px"
                v-if="listData.pvDayBeforeType == 1"
              />
              <p
                class="dataStatistics3_3_3"
                v-if="listData.pvDayBeforeType == 0"
              >
                {{ listData.avgUvDayBeforeRate }}人
              </p>
              <p id="dataStatistics3_3_3" v-if="listData.pvDayBeforeType == 1">
                {{ listData.avgUvDayBeforeRate }}人
              </p>
            </div>
          </div>
        </div>
        <div class="dataStatistics3_1">
          <div class="dataStatistics3_1_1">
            <div class="dataStatistics3_1_2">人均浏览量</div>
            <el-tooltip
              class="item"
              effect="dark"
              content="统计时间内浏览量与访客数的比率"
              placement="top"
            >
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/fe3baf5baa76452d92c457eeda2c8686png"
                alt=""
                style="width: 16px; height: 16px; margin-left: 3px"
              />
            </el-tooltip>
          </div>
          <p class="dataStatistics3_2">{{ listData.avgUv }}</p>
          <div class="dataStatistics3_3">
            <p class="dataStatistics3_3_1">较前一日</p>
            <div class="dataStatistics3_3_2">
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png"
                alt=""
                style="width: 8px; height: 16px"
                v-if="listData.avgUvDayBeforeType == 0"
              />
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png"
                alt=""
                style="width: 8px; height: 16px"
                v-if="listData.avgUvDayBeforeType == 1"
              />
              <p
                class="dataStatistics3_3_3"
                v-if="listData.avgDurationChangeType == 0"
              >
                {{ listData.avgUvDayBeforeRate }}人
              </p>
              <p
                id="dataStatistics3_3_3"
                v-if="listData.avgUvDayBeforeType == 1"
              >
                {{ listData.avgUvDayBeforeRate }}人
              </p>
            </div>
          </div>
        </div>
        <div class="dataStatistics3_1">
          <div class="dataStatistics3_1_1">
            <div class="dataStatistics3_1_2">跳失率</div>
            <el-tooltip
              class="item"
              effect="dark"
              content="跳失率 = 只浏览了一个页面的访客数 / 总访客数"
              placement="top"
            >
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/fe3baf5baa76452d92c457eeda2c8686png"
                alt=""
                style="width: 16px; height: 16px; margin-left: 3px"
              />
            </el-tooltip>
          </div>
          <p class="dataStatistics3_2">{{ listData.lossRate }}</p>
          <div class="dataStatistics3_3">
            <p class="dataStatistics3_3_1">较前一日</p>
            <div class="dataStatistics3_3_2">
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png"
                alt=""
                style="width: 8px; height: 16px"
                v-if="listData.lossDayBeforeType == 0"
              />
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png"
                alt=""
                style="width: 8px; height: 16px"
                v-if="listData.lossDayBeforeType == 1"
              />
              <p
                class="dataStatistics3_3_3"
                v-if="listData.lossDayBeforeType == 0"
              >
                {{ listData.lossDayBeforeRate }}人
              </p>
              <p
                id="dataStatistics3_3_3"
                v-if="listData.lossDayBeforeType == 1"
              >
                {{ listData.lossDayBeforeRate }}人
              </p>
            </div>
          </div>
        </div>
        <div class="dataStatistics3_1">
          <div class="dataStatistics3_1_1">
            <div class="dataStatistics3_1_2">平均停留时长</div>
            <el-tooltip
              class="item"
              effect="dark"
              content="访客浏览某一页面时所花费的平均时长"
              placement="top"
            >
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/fe3baf5baa76452d92c457eeda2c8686png"
                alt=""
                style="width: 16px; height: 16px; margin-left: 3px"
              />
            </el-tooltip>
          </div>
          <p class="dataStatistics3_2">{{ listData.avgDurationStr }}</p>
          <div class="dataStatistics3_3">
            <p class="dataStatistics3_3_1">较前一日</p>
            <div class="dataStatistics3_3_2">
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/6d4a2d4323c747f3a98fe7fa418803d6png"
                alt=""
                style="width: 8px; height: 16px"
                v-if="listData.avgDurationChangeType == 0"
              />
              <img
                src="http://cdn.xiaodingdang1.com/2024/04/15/588526959a80412a838ae98d13b9c4f1png"
                alt=""
                style="width: 8px; height: 16px"
                v-if="listData.avgDurationChangeType == 1"
              />
              <p
                class="dataStatistics3_3_3"
                v-if="listData.avgDurationChangeType == 0"
              >
                {{ listData.avgDurationSecondsRate }}人
              </p>
              <p
                id="dataStatistics3_3_3"
                v-if="listData.avgDurationChangeType == 1"
              >
                {{ listData.avgDurationSecondsRate }}人
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="information">
      <div class="numberVisitors">
        <div class="numberVisitors1">
          <p class="numberVisitors1_1">访客数</p>
          <p class="numberVisitors1_2">单位:{{ this.eventTime.length }}人</p>
        </div>
        <div id="chartLineBox" style="width: 100%; height: 237px"></div>
      </div>
      <div class="remind">
        <div class="remind1">
          <p class="remind1_1">
            <span>生日提醒：</span>
            <span class="remind1_3">{{ birthdayList.length }}</span>
          </p>
          <p class="remind1_2" @click="viewMore(3)">查看更多</p>
        </div>
        <div class="remind2" v-for="(item, index) in birthdayList" :key="index">
          <div class="remind2_1">
            <p class="remind2_2" v-if="item.birthdayType == 'mom'">妈妈</p>
            <p class="remind2_2" v-if="item.birthdayType == 'baby'">宝宝</p>
            <p class="remind2_3" v-if="item.birthdayType == 'mom'">
              距离{{ item.name }}生日还剩
              <span class="remind2_4">{{ item.momBirthdayNum }}</span
              >天
            </p>
            <p class="remind2_3" v-if="item.birthdayType == 'baby'">
              距离{{ item.babyName }}生日还剩
              <span class="remind2_4">{{ item.babyBirthdayNum }}</span
              >天
            </p>
          </div>
          <img
            src="http://cdn.xiaodingdang1.com/2024/04/15/8c9a56676e184330b57ca405291efda0png"
            alt=""
            style="width: 7px; height: 10px"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { totalVisit, birthdayList, newStats } from "@/api/platform/index";
import { queryClubInfo } from "@/api/platform/club";
export default {
  name: "Index",
  data() {
    return {
      newStats: "",
      birthdayList: [],
      uv: [],
      eventTime: [],
      listData: "",

      // 版本号
      version: "3.8.7",
      input: "",
      value1: "",
      startDate: null, //开始时间
      endDate: null, //结束时间
      tabIndex: 0,
      ruleForm: {},
      tabList: ["日", "周", "月"],
      clubInfo: {},
    };
  },
  created() {
    this.totalVisit();
    this.birthdayLists();
    this.newStats1();
    queryClubInfo().then((res) => {
      this.clubInfo = res.data;
    });
  },
  methods: {
    next(index) {
      if (index == 1) {
        this.$router.push({
          path: "/club/info",
        });
      } else if (index == 2) {
        this.$router.push({
          path: "signIn/checkManagement",
        });
      } else if (index == 3) {
        this.$router.push({
          path: "/informationCenter",
        });
      } else if (index == 4) {
        this.$router.push({
          path: "/distribution/distributionService",
        });
      } else if (index == 5) {
        this.$router.push({
          path: "/distribution/mobileMatron",
        });
      }
    },
    viewMore(index) {
      if (index == 1) {
        this.$router.push({
          path: "/commentManagement",
        });
      } else if (index == 2) {
        this.$router.push({
          path: "/dataAnalysis",
        });
      } else if (index == 3) {
        this.$router.push({
          path: "/customerManagement/birthdayReminder",
        });
      }
    },
    inquire() {
      this.totalVisit();
    },
    tab(index) {
      this.tabIndex = index;
      this.totalVisit();
      this.startDate = null; //开始时间
      this.endDate = null; //结束时间
    },
    goTarget(href) {
      window.open(href, "_blank");
    },
    getEcharts() {
      this.chartLine = echarts.init(document.getElementById("chartLineBox"));
      // 指定图表的配置项和数据
      var option = {
        tooltip: {
          //设置tip提示
          trigger: "axis",
        },
        color: ["#3058FF", "#3F85FF"], //设置区分（每条线是什么颜色，和 legend 一一对应）
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.eventTime,
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: this.uv,
            type: "line",
            areaStyle: {},
            itemStyle: {
              normal: {
                //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0.2,
                    color: "#3F85FF", // 0% 处的颜色
                  },
                  {
                    offset: 0.3,
                    color: "#3F85FF", // 100% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#fff", // 100% 处的颜色
                  },
                ]), //背景渐变色
                lineStyle: {
                  // 系列级个性化折线样式
                  width: 2,
                  type: "solid",
                  color: "#3F85FF", //折线的颜色
                },
              },
            },
            // 设置折线弧度，取值：0-1之间
            smooth: 0.5,
          },
        ],
      };

      // 使用刚指定的配置项和数据显示图表。
      this.chartLine.setOption(option);
    },
    totalVisit() {
      //查询
      if (this.startDate && this.endDate) {
        this.ruleForm.queryType = "custom";
      } else {
        this.ruleForm.queryType =
          this.tabIndex == 0
            ? "day"
            : this.tabIndex == 1
            ? "week"
            : this.tabIndex == 2
            ? "month"
            : "";
      }
      this.ruleForm.startDate = this.startDate;
      this.ruleForm.endDate = this.endDate;
      totalVisit(this.ruleForm).then((res) => {
        if (res.code == 200) {
          this.listData = res.data;
          let eventTime = [];
          let uv = [];
          res.data.userAccessAnalysisStats.forEach((item) => {
            eventTime.push(item.eventTime);
            uv.push(item.uv);
          });
          this.eventTime = eventTime;
          this.uv = uv;
          console.log(eventTime, uv);
          this.getEcharts();
          return;
        }
      });
    },
    birthdayLists() {
      let left = this;
      birthdayList({ limit: 1000 }).then((res) => {
        left.birthdayList = res.data;
      });
    },
    newStats1() {
      let left = this;
      newStats().then((res) => {
        left.newStats = res.data;
      });
    },
  },
  mounted() {},
};
</script>

<style scoped lang="scss">
#app {
  background: #f0f1f5;
  padding-bottom: 40px;
  padding: 20px 20px;
}
.head {
  border-radius: 10px;
  background: #fff;
  width: 100%;
  padding: 20px 20px;
  .headTitle {
    font-size: 32px;
    color: #17191a;
    font-weight: bold;
  }
  .headInp {
    display: flex;
    margin-top: 20px;
    .el-input {
      width: 95%;
    }
    .autopsy {
      margin-left: 20px;
    }
  }
}
.function {
  margin-top: 20px;
  width: 100%;
  display: flex;
  .commonlyUsed {
    width: 79%;
    background: #fff;
    border-radius: 10px;
    padding: 20px 20px;
    .commonlyUsed1 {
      font-size: 18px;
      color: #17191a;
    }
    .commonlyUsed3 {
      display: flex;
      justify-content: space-around;
      .commonlyUsed2 {
        font-size: 16px;
        color: #000000;
        text-align: center;
      }
      div {
        text-align: center;
      }
    }
  }
  .evaluationManagement {
    width: 20%;
    background: #fff;
    border-radius: 10px;
    padding: 20px 20px;
    margin-left: 1%;
    .evaluationManagement1 {
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 0px;
      .evaluationManagement1_1 {
        font-size: 20px;
        color: #17191a;
        font-weight: bold;
      }
      .evaluationManagement1_2 {
        font-size: 14px;
        color: #3f85ff;
      }
    }
    .evaluationManagement2 {
      font-size: 14px;
      color: #a9aaae;
    }
    .evaluationManagement3 {
      display: flex;
      align-items: center;
      .evaluationManagement3_1 {
        margin-left: 20px;
        .evaluationManagement3_2 {
          font-size: 14px;
          color: #17191a;
        }
        .evaluationManagement3_3 {
          line-height: 0;
          font-size: 30px;
          color: #17191a;
          font-weight: bold;
          padding-top: 4px;
        }
      }
    }
  }
}
.dataStatistics {
  background: #fff;
  padding: 20px 20px;
  width: 100%;
  margin-top: 20px;
  border-radius: 10px;
  .dataStatistics1 {
    display: flex;
    justify-content: space-between;
    .dataStatistics1_1 {
      color: #17191a;
      font-size: 20px;
    }
  }
  .dataStatistics2 {
    display: flex;
    align-items: center;
    .dataStatistics2_1 {
      display: flex;
      .tab {
        width: 38px;
        height: 25px;
        border: 1px solid #c8c9cd;
        color: #c8c9cd;
        text-align: center;
        line-height: 25px;
        border-radius: 2px;
        margin-right: 12px;
      }
      .tabActive {
        width: 38px;
        height: 25px;
        color: #fff;
        text-align: center;
        line-height: 25px;
        border-radius: 2px;
        margin-right: 12px;
        background: #3f85ff;
      }
    }
    .dataStatistics2_2 {
      margin-right: 12px;
    }
    .dataStatistics2_3 {
      color: #3f85ff;
      font-size: 14px;
      margin-left: 10px;
    }
  }
  .dataStatistics3 {
    display: flex;
    margin-top: 20px;
    .dataStatistics3_1 {
      width: 19%;
      background: #fff;
      padding: 12px 16px;
      border: 1px solid #d9d9d9;
      border-radius: 5px;
      margin-left: 1%;
      .dataStatistics3_1_1 {
        display: flex;
        align-items: center;
        line-height: 0;
        .dataStatistics3_1_2 {
          color: #17191a;
          font-size: 14px;
        }
      }
    }
    .dataStatistics3_2 {
      color: #17191a;
      font-size: 30px;
      font-weight: bold;
      line-height: 0;
      margin-top: 26px;
    }
    .dataStatistics3_3 {
      display: flex;
      line-height: 0;
      align-items: center;
      .dataStatistics3_3_1 {
        color: #7c7d81;
        font-size: 14px;
      }
      .dataStatistics3_3_2 {
        display: flex;
        margin-left: 22px;
        align-items: center;
        .dataStatistics3_3_3 {
          color: #f84343;
          font-size: 14px;
          margin-left: 8px;
        }
        #dataStatistics3_3_3 {
          color: #0bbd71;
          font-size: 14px;
          margin-left: 8px;
        }
      }
    }
  }
}
.information {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 20px;
  .numberVisitors {
    width: 70%;
    background: #fff;
    padding: 20px 20px;
    .numberVisitors1 {
      display: flex;
      justify-content: space-between;
      .numberVisitors1_1 {
        color: #17191a;
        font-size: 18px;
      }
      .numberVisitors1_2 {
        color: #86909c;
        font-size: 12px;
      }
    }
  }
  .remind {
    width: 29%;
    background: #fff;
    padding: 20px 20px;
    height: 328px;
    overflow-y: auto;
    .remind1 {
      display: flex;
      justify-content: space-between;
      .remind1_1 {
        color: #17191a;
        font-size: 18px;
        .remind1_3 {
          color: #f84343;
        }
      }
      .remind1_2 {
        color: #3f85ff;
        font-size: 14px;
      }
    }
    .remind2 {
      display: flex;
      justify-content: space-between;
      .remind2_1 {
        display: flex;
        .remind2_2 {
          width: 34px;
          height: 19px;
          text-align: center;
          line-height: 19px;
          border-radius: 1px;
          background: #f3f5f4;
          color: #0bbd71;
        }
        .remind2_3 {
          margin-left: 16px;
          color: #45464a;
          font-size: 14px;
          .remind2_4 {
            color: #ff6c11;
          }
        }
      }
    }
  }
}

.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}
</style>
