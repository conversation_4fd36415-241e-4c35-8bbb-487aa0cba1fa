<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>AI模型计费策略管理</h2>
      <p>管理AI模型的Token计费策略，支持不同模型和提供商的价格配置</p>
    </div>

    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
        <el-form-item label="模型名称" prop="modelName">
          <el-input
            v-model="queryParams.modelName"
            placeholder="请输入模型名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="模型提供商" prop="modelProvider">
          <el-input
            v-model="queryParams.modelProvider"
            placeholder="请输入模型提供商"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="货币单位" prop="currency">
          <el-select v-model="queryParams.currency" placeholder="请选择货币单位" clearable>
            <el-option
              v-for="item in currencyOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="生效时间">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['ai:pricing:add']"
          >新增</el-button>
        </el-col>

        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="pricingList"
      border
      stripe
    >
      <el-table-column label="序号" type="index" width="60" align="center" />
      
      <el-table-column label="模型名称" align="center" prop="modelName" min-width="120">
        <template slot-scope="scope">
          <span class="model-name">{{ scope.row.modelName }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="模型提供商" align="center" prop="modelProvider" min-width="100">
        <template slot-scope="scope">
          <el-tag size="small" type="info">{{ getProviderText(scope.row.modelProvider) }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="输入Token单价" align="center" prop="inputTokenPrice" min-width="130">
        <template slot-scope="scope">
          <span class="price-text">{{ formatPrice(scope.row.inputTokenPrice, scope.row.currency) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="输出Token单价" align="center" prop="outputTokenPrice" min-width="130">
        <template slot-scope="scope">
          <span class="price-text">{{ formatPrice(scope.row.outputTokenPrice, scope.row.currency) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="货币单位" align="center" prop="currency" width="80">
        <template slot-scope="scope">
          <span>{{ getCurrencyText(scope.row.currency) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-tag 
            :type="getStatusType(scope.row.status)" 
            size="small"
          >
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="生效时间" align="center" prop="effectiveDate" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.effectiveDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="失效时间" align="center" prop="expireDate" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.expireDate ? parseTime(scope.row.expireDate, '{y}-{m}-{d} {h}:{i}:{s}') : '永久有效' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['ai:pricing:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['ai:pricing:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['ai:pricing:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 表单组件 -->
    <pricing-form ref="pricingForm" @success="getList" />

    <!-- 详情对话框 -->
    <el-dialog title="计费策略详情" :visible.sync="detailVisible" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="模型名称">{{ detailData.modelName }}</el-descriptions-item>
        <el-descriptions-item label="模型提供商">{{ getProviderText(detailData.modelProvider) }}</el-descriptions-item>
        <el-descriptions-item label="输入Token单价">
          {{ formatPrice(detailData.inputTokenPrice, detailData.currency) }}
        </el-descriptions-item>
        <el-descriptions-item label="输出Token单价">
          {{ formatPrice(detailData.outputTokenPrice, detailData.currency) }}
        </el-descriptions-item>
        <el-descriptions-item label="货币单位">{{ getCurrencyText(detailData.currency) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(detailData.status)" size="small">
            {{ getStatusText(detailData.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="生效时间">
          {{ parseTime(detailData.effectiveDate, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="失效时间">
          {{ detailData.expireDate ? parseTime(detailData.expireDate, '{y}-{m}-{d} {h}:{i}:{s}') : '永久有效' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">
          {{ parseTime(detailData.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="备注说明" :span="2">
          {{ detailData.remark || '无' }}
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关 闭</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import {
  getModelPricingList,
  deleteModelPricing,
  formatPrice,
  getStatusType,
  getStatusText,
  CURRENCY_OPTIONS,
  STATUS_OPTIONS
} from '@/api/ai/modelPricing'
import PricingForm from './components/PricingForm'

export default {
  name: 'ModelPricing',
  components: {
    PricingForm
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计费策略表格数据
      pricingList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        modelName: null,
        modelProvider: null,
        status: null,
        currency: null,
        effectiveDateStart: null,
        effectiveDateEnd: null
      },
      // 日期范围
      dateRange: [],
      // 货币单位选项
      currencyOptions: CURRENCY_OPTIONS,
      // 状态选项
      statusOptions: STATUS_OPTIONS,
      // 提供商选项列表（用于显示中文名称）
      providerOptions: [
        { label: 'OpenAI', value: 'openai' },
        { label: 'Anthropic', value: 'anthropic' },
        { label: 'Google', value: 'google' },
        { label: 'Microsoft', value: 'microsoft' },
        { label: '阿里云百炼', value: 'dashscope' },
        { label: '百度千帆', value: 'qianfan' },
        { label: '腾讯混元', value: 'hunyuan' },
        { label: '字节豆包', value: 'doubao' },
        { label: '智谱AI', value: 'zhipu' },
        { label: '月之暗面', value: 'moonshot' },
        { label: '深度求索', value: 'deepseek' },
        { label: '零一万物', value: 'yi' },
        { label: 'Mistral AI', value: 'mistral' },
        { label: 'Cohere', value: 'cohere' },
        { label: '其他', value: 'other' }
      ],
      // 详情对话框显示状态
      detailVisible: false,
      // 详情数据
      detailData: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询计费策略列表 */
    getList() {
      this.loading = true
      getModelPricingList(this.queryParams).then(response => {
        this.pricingList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.queryParams.effectiveDateStart = null
      this.queryParams.effectiveDateEnd = null
      this.resetForm('queryForm')
      this.handleQuery()
    },
    
    /** 日期范围变化处理 */
    handleDateRangeChange(value) {
      if (value && value.length === 2) {
        this.queryParams.effectiveDateStart = value[0]
        this.queryParams.effectiveDateEnd = value[1]
      } else {
        this.queryParams.effectiveDateStart = null
        this.queryParams.effectiveDateEnd = null
      }
    },
    

    
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.pricingForm.showAdd()
    },
    
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$refs.pricingForm.showEdit(row)
    },
    
    /** 详情按钮操作 */
    handleDetail(row) {
      this.detailData = { ...row }
      this.detailVisible = true
    },
    
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除该计费策略？').then(function() {
        return deleteModelPricing(row.id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    

    
    // 格式化价格显示
    formatPrice,
    
    // 获取状态标签类型
    getStatusType,
    
    // 获取状态文本
    getStatusText,
    
    // 获取货币单位文本
    getCurrencyText(currency) {
      const currencyInfo = this.currencyOptions.find(item => item.value === currency)
      return currencyInfo ? currencyInfo.label : currency
    },

    // 获取提供商文本
    getProviderText(provider) {
      const providerInfo = this.providerOptions.find(item => item.value === provider)
      return providerInfo ? providerInfo.label : provider
    }
  }
}
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e6e6e6;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 500;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.search-area {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.toolbar {
  margin-bottom: 15px;
}

.model-name {
  font-weight: 500;
  color: #409eff;
}

.price-text {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  color: #e6a23c;
}
</style>
