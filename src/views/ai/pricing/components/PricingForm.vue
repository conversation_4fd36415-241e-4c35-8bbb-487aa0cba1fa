<template>
  <div>
    <!-- 添加或修改计费策略对话框 -->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模型名称" prop="modelName">
              <el-input
                v-model="form.modelName"
                placeholder="请输入模型名称"
                maxlength="100"
                show-word-limit
                clearable
              />
              <div class="form-tip">例如：gpt-4, claude-3-opus, gemini-pro等</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模型提供商" prop="modelProvider">
              <el-select
                v-model="form.modelProvider"
                placeholder="请选择模型提供商"
                filterable
              >
                <el-option
                  v-for="item in providerOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="输入Token单价" prop="inputTokenPrice">
              <el-input-number
                v-model="form.inputTokenPrice"
                :precision="6"
                :step="0.000001"
                :min="0"
                :max="999999"
                controls-position="right"
                placeholder="请输入输入Token单价"
                style="width: 100%"
              />
              <div class="form-tip">单位：元/千Token</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="输出Token单价" prop="outputTokenPrice">
              <el-input-number
                v-model="form.outputTokenPrice"
                :precision="6"
                :step="0.000001"
                :min="0"
                :max="999999"
                controls-position="right"
                placeholder="请输入输出Token单价"
                style="width: 100%"
              />
              <div class="form-tip">单位：元/千Token</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="货币单位" prop="currency">
              <el-radio-group v-model="form.currency">
                <el-radio
                  v-for="item in currencyOptions"
                  :key="item.value"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效时间" prop="effectiveDate">
              <el-date-picker
                v-model="form.effectiveDate"
                type="datetime"
                placeholder="请选择生效时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="失效时间" prop="expireDate">
              <el-date-picker
                v-model="form.expireDate"
                type="datetime"
                placeholder="请选择失效时间（可选）"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注说明" prop="remark">
          <el-input 
            v-model="form.remark" 
            type="textarea" 
            placeholder="请输入备注说明"
            maxlength="500"
            show-word-limit
            :rows="3"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createModelPricing,
  updateModelPricing,
  checkPricingDuplicate,
  CURRENCY_OPTIONS,
  STATUS_OPTIONS
} from '@/api/ai/modelPricing'

export default {
  name: 'PricingForm',
  data() {
    return {
      // 对话框显示状态
      dialogVisible: false,
      // 对话框标题
      title: '',
      // 提交加载状态
      submitLoading: false,
      // 提供商选项列表（静态数据）
      providerOptions: [
        { label: 'OpenAI', value: 'openai' },
        { label: 'Anthropic', value: 'anthropic' },
        { label: 'Google', value: 'google' },
        { label: 'Microsoft', value: 'microsoft' },
        { label: '阿里云百炼', value: 'dashscope' },
        { label: '百度千帆', value: 'qianfan' },
        { label: '腾讯混元', value: 'hunyuan' },
        { label: '字节豆包', value: 'doubao' },
        { label: '智谱AI', value: 'zhipu' },
        { label: '月之暗面', value: 'moonshot' },
        { label: '深度求索', value: 'deepseek' },
        { label: '零一万物', value: 'yi' },
        { label: 'Mistral AI', value: 'mistral' },
        { label: 'Cohere', value: 'cohere' },
        { label: '其他', value: 'other' }
      ],
      // 货币单位选项
      currencyOptions: CURRENCY_OPTIONS,
      // 状态选项
      statusOptions: STATUS_OPTIONS,
      // 表单数据
      form: {
        id: null,
        modelName: '',
        modelProvider: '',
        inputTokenPrice: null,
        outputTokenPrice: null,
        currency: 'CNY',
        status: '0',
        effectiveDate: '',
        expireDate: '',
        remark: ''
      },
      // 原始生效时间（用于编辑时的验证逻辑）
      originalEffectiveDate: null,
      // 表单验证规则
      rules: {
        modelName: [
          { required: true, message: '请输入模型名称', trigger: 'blur' },
          { min: 2, max: 100, message: '模型名称长度在2到100个字符', trigger: 'blur' }
        ],
        modelProvider: [
          { required: true, message: '请选择模型提供商', trigger: 'change' }
        ],
        inputTokenPrice: [
          { required: true, message: '请输入输入Token单价', trigger: 'blur' },
          { type: 'number', min: 0, message: '单价不能小于0', trigger: 'blur' }
        ],
        outputTokenPrice: [
          { required: true, message: '请输入输出Token单价', trigger: 'blur' },
          { type: 'number', min: 0, message: '单价不能小于0', trigger: 'blur' }
        ],
        currency: [
          { required: true, message: '请选择货币单位', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        effectiveDate: [
          { required: true, message: '请选择生效时间', trigger: 'change' },
          { validator: this.validateEffectiveDate, trigger: 'change' }
        ],
        expireDate: [
          { validator: this.validateExpireDate, trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    // 显示新增对话框
    showAdd() {
      this.reset()
      this.title = '新增计费策略'
      this.originalEffectiveDate = null // 新增模式清空原始时间
      this.dialogVisible = true
    },

    // 显示编辑对话框
    showEdit(row) {
      this.reset()
      this.title = '修改计费策略'
      this.form = { ...row }
      this.originalEffectiveDate = row.effectiveDate // 保存原始生效时间
      this.dialogVisible = true
    },
    
    // 重置表单
    reset() {
      this.form = {
        id: null,
        modelName: '',
        modelProvider: '',
        inputTokenPrice: null,
        outputTokenPrice: null,
        currency: 'CNY',
        status: '0',
        effectiveDate: '',
        expireDate: '',
        remark: ''
      }
      this.originalEffectiveDate = null // 重置原始生效时间
      this.resetForm('form')
    },
    
    // 取消操作
    cancel() {
      this.dialogVisible = false
      this.reset()
    },
    
    // 提交表单
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.submitLoading = true
          
          // 检查重复
          this.checkDuplicate().then(() => {
            const submitMethod = this.form.id ? updateModelPricing : createModelPricing
            
            submitMethod(this.form).then(() => {
              this.$modal.msgSuccess(this.form.id ? '修改成功' : '新增成功')
              this.dialogVisible = false
              this.$emit('success')
            }).finally(() => {
              this.submitLoading = false
            })
          }).catch(() => {
            this.submitLoading = false
          })
        }
      })
    },
    
    // 检查重复
    checkDuplicate() {
      return new Promise((resolve, reject) => {
        const checkData = {
          id: this.form.id,
          modelName: this.form.modelName,
          modelProvider: this.form.modelProvider,
          effectiveDate: this.form.effectiveDate,
          expireDate: this.form.expireDate
        }

        checkPricingDuplicate(checkData).then(isDuplicate => {
          if (isDuplicate) {
            this.$modal.msgError('该模型在指定时间段内已存在计费策略')
            reject()
          } else {
            resolve()
          }
        }).catch(() => {
          // 检查失败时继续提交
          resolve()
        })
      })
    },
    
    // 验证生效时间
    validateEffectiveDate(rule, value, callback) {
      if (!value) {
        callback()
        return
      }

      // 如果是编辑模式且有原始数据，需要特殊处理
      if (this.form.id && this.originalEffectiveDate) {
        // 如果生效时间没有改变，则不进行验证（允许保持原有的历史时间）
        if (value === this.originalEffectiveDate) {
          callback()
          return
        }
      }

      // 获取当前时间，精确到分钟
      const now = new Date()
      now.setSeconds(0, 0) // 将秒和毫秒设为0，只比较到分钟

      // 解析生效时间
      const effectiveDate = new Date(value)

      // 只有在新增模式或者修改了生效时间的情况下，才验证不能早于当前时间
      if (effectiveDate.getTime() < now.getTime()) {
        callback(new Error('生效时间不能早于当前时间'))
      } else {
        callback()
      }
    },

    // 验证失效时间
    validateExpireDate(rule, value, callback) {
      if (!value) {
        callback()
        return
      }

      if (!this.form.effectiveDate) {
        callback()
        return
      }

      const effectiveDate = new Date(this.form.effectiveDate)
      const expireDate = new Date(value)

      if (expireDate.getTime() <= effectiveDate.getTime()) {
        callback(new Error('失效时间必须晚于生效时间'))
      } else {
        callback()
      }
    }
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
