<template>
  <div class="document-manager">
    <!-- 文档操作工具栏 -->
    <div class="document-toolbar">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-button 
            type="primary" 
            icon="el-icon-upload" 
            size="small"
            @click="showUploadDialog = true"
            v-hasPermi="['ai:document:upload']"
          >
            上传文档
          </el-button>
          <el-button 
            type="danger" 
            icon="el-icon-delete" 
            size="small"
            :disabled="selectedDocuments.length === 0"
            @click="handleBatchDelete"
            v-hasPermi="['ai:document:remove']"
          >
            批量删除
          </el-button>
          <el-button 
            type="warning" 
            icon="el-icon-refresh" 
            size="small"
            @click="handleCleanupFailed"
            v-hasPermi="['ai:document:edit']"
          >
            清理失败文档
          </el-button>
        </el-col>
        <el-col :span="12">
          <div style="float: right;">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索文档名称或摘要"
              size="small"
              style="width: 200px;"
              @keyup.enter.native="handleSearch"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
            </el-input>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 文档筛选器 -->
    <div class="document-filters">
      <el-form :inline="true" size="small">
        <el-form-item label="文件类型">
          <el-select v-model="filterParams.fileType" placeholder="全部类型" clearable @change="handleFilter">
            <el-option label="PDF" value="pdf" />
            <el-option label="Word文档" value="doc" />
            <el-option label="Word文档(新)" value="docx" />
            <el-option label="文本文件" value="txt" />
            <el-option label="Markdown" value="md" />
            <el-option label="HTML" value="html" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理状态">
          <el-select v-model="filterParams.processStatus" placeholder="全部状态" clearable @change="handleFilter">
            <el-option label="待处理" value="PENDING" />
            <el-option label="处理中" value="PROCESSING" />
            <el-option label="处理成功" value="SUCCESS" />
            <el-option label="处理失败" value="FAILED" />
          </el-select>
        </el-form-item>
        <el-form-item label="向量化状态">
          <el-select v-model="filterParams.vectorStatus" placeholder="全部状态" clearable @change="handleFilter">
            <el-option label="待向量化" value="PENDING" />
            <el-option label="向量化中" value="PROCESSING" />
            <el-option label="向量化成功" value="SUCCESS" />
            <el-option label="向量化失败" value="FAILED" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- 文档列表 -->
    <el-table 
      v-loading="loading" 
      :data="documentList" 
      @selection-change="handleSelectionChange"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文档名称" prop="documentName" min-width="200">
        <template slot-scope="scope">
          <div class="document-name">
            <i :class="getFileIcon(scope.row.fileType)"></i>
            <el-link type="primary" @click="handlePreview(scope.row)" :underline="false">
              {{ scope.row.documentName }}
            </el-link>
          </div>
          <div class="document-info">
            <span class="file-size">{{ formatFileSize(scope.row.fileSize) }}</span>
            <span class="file-type">{{ scope.row.fileType.toUpperCase() }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="摘要" prop="summary" min-width="200" :show-overflow-tooltip="true" />
      <el-table-column label="处理状态" align="center" width="120">
        <template slot-scope="scope">
          <el-tag 
            :type="getProcessStatusType(scope.row.processStatus)"
            size="small"
          >
            {{ getProcessStatusText(scope.row.processStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="向量化状态" align="center" width="120">
        <template slot-scope="scope">
          <el-tag 
            :type="getVectorStatusType(scope.row.vectorStatus)"
            size="small"
          >
            {{ getVectorStatusText(scope.row.vectorStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="分块/向量" align="center" width="100">
        <template slot-scope="scope">
          <div>{{ scope.row.chunkCount || 0 }} / {{ scope.row.vectorCount || 0 }}</div>
        </template>
      </el-table-column>
      <el-table-column label="上传时间" align="center" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button 
            size="mini" 
            type="text" 
            icon="el-icon-view"
            @click="handlePreview(scope.row)"
          >
            预览
          </el-button>
          <el-button 
            size="mini" 
            type="text" 
            icon="el-icon-refresh"
            @click="handleReprocess(scope.row)"
            v-if="scope.row.processStatus === 'FAILED' || scope.row.vectorStatus === 'FAILED'"
            v-hasPermi="['ai:document:edit']"
          >
            重新处理
          </el-button>
          <el-button 
            size="mini" 
            type="text" 
            icon="el-icon-edit"
            @click="handleEdit(scope.row)"
            v-hasPermi="['ai:document:edit']"
          >
            编辑
          </el-button>
          <el-button 
            size="mini" 
            type="text" 
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['ai:document:remove']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getDocumentList"
      style="margin-top: 20px;"
    />

    <!-- 文件上传对话框 -->
    <file-upload
      ref="fileUpload"
      :visible.sync="showUploadDialog"
      :knowledge-id="knowledgeId"
      @success="handleUploadSuccess"
    />

    <!-- 文档预览对话框 -->
    <el-dialog title="文档预览" :visible.sync="showPreviewDialog" width="60%" append-to-body>
      <div v-loading="previewLoading" style="min-height: 200px;">
        <div v-if="previewContent" class="preview-content">
          <pre>{{ previewContent }}</pre>
        </div>
        <div v-else class="no-preview">
          <i class="el-icon-document"></i>
          <p>暂无预览内容</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showPreviewDialog = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 文档编辑对话框 -->
    <el-dialog title="编辑文档" :visible.sync="showEditDialog" width="500px" append-to-body>
      <el-form ref="editForm" :model="editForm" :rules="editRules" label-width="100px">
        <el-form-item label="文档名称" prop="documentName">
          <el-input v-model="editForm.documentName" placeholder="请输入文档名称" />
        </el-form-item>
        <el-form-item label="摘要" prop="summary">
          <el-input 
            v-model="editForm.summary" 
            type="textarea" 
            placeholder="请输入文档摘要"
            :rows="4"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input 
            v-model="editForm.remark" 
            type="textarea" 
            placeholder="请输入备注"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEdit" :loading="editLoading">确 定</el-button>
        <el-button @click="showEditDialog = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDocumentList,
  getDocumentsByKnowledgeId,
  getDocumentPreview,
  updateDocument,
  deleteDocuments,
  reprocessDocument,
  buildDocumentQuery,
  PROCESS_STATUS_TEXT_MAP,
  VECTOR_STATUS_TEXT_MAP
} from "@/api/ai/document"
import { cleanupFailedDocuments } from "@/api/ai/knowledgeService"
import FileUpload from "./FileUpload"

export default {
  name: "DocumentManager",
  components: {
    FileUpload
  },
  props: {
    knowledgeId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      documentList: [],
      selectedDocuments: [],
      total: 0,
      searchKeyword: '',
      filterParams: {
        fileType: '',
        processStatus: '',
        vectorStatus: ''
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 上传对话框
      showUploadDialog: false,
      // 预览对话框
      showPreviewDialog: false,
      previewLoading: false,
      previewContent: '',
      // 编辑对话框
      showEditDialog: false,
      editLoading: false,
      editForm: {},
      editRules: {
        documentName: [
          { required: true, message: "文档名称不能为空", trigger: "blur" }
        ]
      },
      // 状态轮询相关
      statusPollingTimer: null,
      pollingInterval: 4000 // 4秒轮询间隔
    }
  },
  mounted() {
    // 移除这里的调用，由watch处理
  },
  watch: {
    knowledgeId: {
      handler() {
        this.getDocumentList()
      },
      immediate: true
    }
  },
  beforeDestroy() {
    // 清理轮询定时器
    this.stopStatusPolling()
  },
  methods: {
    /** 获取文档列表 */
    getDocumentList() {
      if (!this.knowledgeId) return

      this.loading = true
      const query = buildDocumentQuery({
        ...this.queryParams,
        knowledgeId: this.knowledgeId,
        keyword: this.searchKeyword,
        ...this.filterParams
      })

      getDocumentList(query).then(response => {
        // API层已经处理了数据结构适配
        this.documentList = response.rows || []
        this.total = response.total || 0
        this.loading = false

        // 检查是否需要启动状态轮询
        this.checkAndStartStatusPolling()
      }).catch(error => {
        console.error('获取文档列表失败:', error)
        this.documentList = []
        this.total = 0
        this.loading = false
      })
    },
    /** 搜索 */
    handleSearch() {
      this.queryParams.pageNum = 1
      this.getDocumentList()
    },
    /** 筛选 */
    handleFilter() {
      this.queryParams.pageNum = 1
      this.getDocumentList()
    },
    /** 选择变化 */
    handleSelectionChange(selection) {
      this.selectedDocuments = selection
    },
    /** 上传成功回调 */
    handleUploadSuccess() {
      this.getDocumentList()
      this.$emit('upload-success')
      // 上传成功后立即启动状态轮询
      setTimeout(() => {
        this.startStatusPolling()
      }, 1000)
    },
    /** 预览文档 */
    handlePreview(row) {
      this.showPreviewDialog = true
      this.previewLoading = true
      this.previewContent = ''
      
      getDocumentPreview(row.documentId, 2000).then(response => {
        this.previewContent = response
        this.previewLoading = false
      }).catch(() => {
        this.previewLoading = false
      })
    },
    /** 编辑文档 */
    handleEdit(row) {
      this.editForm = {
        documentId: row.documentId,
        knowledgeId: row.knowledgeId,
        documentName: row.documentName,
        summary: row.summary,
        remark: row.remark
      }
      this.showEditDialog = true
    },
    /** 提交编辑 */
    submitEdit() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          this.editLoading = true
          updateDocument(this.editForm).then(() => {
            this.$modal.msgSuccess("修改成功")
            this.showEditDialog = false
            this.getDocumentList()
          }).finally(() => {
            this.editLoading = false
          })
        }
      })
    },
    /** 重新处理 */
    handleReprocess(row) {
      this.$modal.confirm(`确认要重新处理文档"${row.documentName}"吗？`).then(() => {
        return reprocessDocument(row.documentId)
      }).then(() => {
        this.$modal.msgSuccess("重新处理任务已提交")
        this.getDocumentList()
      })
    },
    /** 删除文档 */
    handleDelete(row) {
      this.$modal.confirm(`确认要删除文档"${row.documentName}"吗？`).then(() => {
        return deleteDocuments([row.documentId])
      }).then(() => {
        this.$modal.msgSuccess("删除成功")
        this.getDocumentList()
        this.$emit('document-deleted')
      })
    },
    /** 批量删除 */
    handleBatchDelete() {
      if (this.selectedDocuments.length === 0) {
        this.$modal.msgWarning("请选择要删除的文档")
        return
      }
      
      const documentIds = this.selectedDocuments.map(doc => doc.documentId)
      const documentNames = this.selectedDocuments.map(doc => doc.documentName).join(', ')
      
      this.$modal.confirm(`确认要删除以下文档吗？\n${documentNames}`).then(() => {
        return deleteDocuments(documentIds)
      }).then(() => {
        this.$modal.msgSuccess("批量删除成功")
        this.getDocumentList()
        this.$emit('document-deleted')
      })
    },
    /** 清理失败文档 */
    handleCleanupFailed() {
      this.$modal.confirm('确认要清理所有处理失败的文档吗？此操作不可恢复。').then(() => {
        return cleanupFailedDocuments(this.knowledgeId)
      }).then(response => {
        if (response.success) {
          this.$modal.msgSuccess(response.message)
          this.getDocumentList()
          this.$emit('cleanup-completed', response.data)
        } else {
          this.$modal.msgError(response.message)
        }
      })
    },
    /** 获取文件图标 */
    getFileIcon(fileType) {
      const iconMap = {
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        txt: 'el-icon-tickets',
        md: 'el-icon-tickets',
        html: 'el-icon-document'
      }
      return iconMap[fileType] || 'el-icon-document'
    },
    /** 格式化文件大小 */
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    /** 获取处理状态类型 */
    getProcessStatusType(status) {
      const typeMap = {
        'PENDING': 'info',
        'PROCESSING': 'warning',
        'SUCCESS': 'success',
        'FAILED': 'danger'
      }
      return typeMap[status] || 'info'
    },
    /** 获取向量化状态类型 */
    getVectorStatusType(status) {
      return this.getProcessStatusType(status)
    },
    /** 获取处理状态文本 */
    getProcessStatusText(status) {
      return PROCESS_STATUS_TEXT_MAP[status] || status
    },
    /** 获取向量化状态文本 */
    getVectorStatusText(status) {
      return VECTOR_STATUS_TEXT_MAP[status] || status
    },
    /** 检查并启动状态轮询 */
    checkAndStartStatusPolling() {
      // 检查是否有正在处理的文档
      const hasProcessingDocs = this.documentList.some(doc =>
        doc.processStatus === 'PENDING' ||
        doc.processStatus === 'PROCESSING' ||
        doc.vectorStatus === 'PENDING' ||
        doc.vectorStatus === 'PROCESSING'
      )

      if (hasProcessingDocs) {
        this.startStatusPolling()
      } else {
        this.stopStatusPolling()
      }
    },
    /** 启动状态轮询 */
    startStatusPolling() {
      // 如果已经有轮询在运行，先停止
      this.stopStatusPolling()

      this.statusPollingTimer = setInterval(() => {
        // 静默刷新文档列表（不显示loading）
        this.refreshDocumentListSilently()
      }, this.pollingInterval)
    },
    /** 停止状态轮询 */
    stopStatusPolling() {
      if (this.statusPollingTimer) {
        clearInterval(this.statusPollingTimer)
        this.statusPollingTimer = null
      }
    },
    /** 静默刷新文档列表 */
    refreshDocumentListSilently() {
      if (!this.knowledgeId) return

      const query = buildDocumentQuery({
        ...this.queryParams,
        knowledgeId: this.knowledgeId,
        keyword: this.searchKeyword,
        ...this.filterParams
      })

      getDocumentList(query).then(response => {
        const newDocumentList = response.rows || []

        // 检查是否还有正在处理的文档
        const hasProcessingDocs = newDocumentList.some(doc =>
          doc.processStatus === 'PENDING' ||
          doc.processStatus === 'PROCESSING' ||
          doc.vectorStatus === 'PENDING' ||
          doc.vectorStatus === 'PROCESSING'
        )

        // 更新文档列表
        this.documentList = newDocumentList
        this.total = response.total || 0

        // 如果没有正在处理的文档，停止轮询
        if (!hasProcessingDocs) {
          this.stopStatusPolling()
        }
      }).catch(error => {
        console.error('静默刷新文档列表失败:', error)
        // 发生错误时停止轮询，避免持续的错误请求
        this.stopStatusPolling()
      })
    }
  }
}
</script>

<style scoped>
.document-manager {
  padding: 20px;
}

.document-toolbar {
  margin-bottom: 15px;
}

.document-filters {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.document-name {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.document-name i {
  margin-right: 8px;
  color: #409eff;
}

.document-info {
  font-size: 12px;
  color: #909399;
}

.document-info .file-size {
  margin-right: 10px;
}

.document-info .file-type {
  background-color: #e4e7ed;
  padding: 2px 6px;
  border-radius: 3px;
}

.preview-content {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.preview-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: inherit;
}

.no-preview {
  text-align: center;
  color: #909399;
  padding: 50px 0;
}

.no-preview i {
  font-size: 48px;
  margin-bottom: 15px;
}
</style>
