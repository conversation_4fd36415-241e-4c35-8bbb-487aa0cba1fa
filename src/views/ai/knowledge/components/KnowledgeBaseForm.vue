<template>
  <div>
    <!-- 添加或修改知识库对话框 -->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="知识库名称" prop="knowledgeName">
          <el-input 
            v-model="form.knowledgeName" 
            placeholder="请输入知识库名称" 
            maxlength="100"
            show-word-limit
            @blur="checkNameUnique"
          />
          <div v-if="nameCheckResult.checked && !nameCheckResult.unique" class="name-check-error">
            <i class="el-icon-warning"></i>
            <span>该名称已存在，请使用其他名称</span>
          </div>
          <div v-if="nameCheckResult.checked && nameCheckResult.unique" class="name-check-success">
            <i class="el-icon-success"></i>
            <span>名称可用</span>
          </div>
        </el-form-item>
        <el-form-item label="知识库描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            placeholder="请输入知识库描述"
            maxlength="500"
            show-word-limit
            :rows="4"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number 
            v-model="form.sort" 
            :min="0" 
            :max="999" 
            controls-position="right"
            placeholder="排序值"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input 
            v-model="form.remark" 
            type="textarea" 
            placeholder="请输入备注信息"
            maxlength="500"
            show-word-limit
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getKnowledgeBaseDetail, 
  createKnowledgeBase, 
  updateKnowledgeBase,
  checkKnowledgeNameUnique,
  validateKnowledgeBaseData
} from "@/api/ai/knowledgeBase"

export default {
  name: "KnowledgeBaseForm",
  data() {
    return {
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      dialogVisible: false,
      // 提交loading
      submitLoading: false,
      // 表单参数
      form: {},
      // 名称唯一性检查结果
      nameCheckResult: {
        checked: false,
        unique: false
      },
      // 表单校验
      rules: {
        knowledgeName: [
          { required: true, message: "知识库名称不能为空", trigger: "blur" },
          { min: 1, max: 100, message: "知识库名称长度必须在1到100个字符之间", trigger: "blur" },
          { validator: this.validateKnowledgeName, trigger: "blur" }
        ],
        description: [
          { max: 500, message: "描述长度不能超过500个字符", trigger: "blur" }
        ],

        status: [
          { required: true, message: "请选择状态", trigger: "change" }
        ]
      }
    }
  },
  methods: {
    /** 打开对话框 */
    openDialog(knowledgeId) {
      this.reset()
      if (knowledgeId != null) {
        this.title = "修改知识库"
        getKnowledgeBaseDetail(knowledgeId).then(response => {
          this.form = response
          this.nameCheckResult = { checked: true, unique: true } // 编辑时默认名称可用
        })
      } else {
        this.title = "添加知识库"
      }
      this.dialogVisible = true
    },
    /** 表单重置 */
    reset() {
      this.form = {
        knowledgeId: null,
        knowledgeName: null,
        description: null,
        knowledgeType: "PRIVATE",
        status: "0",
        sort: 1,
        remark: null
      }
      this.nameCheckResult = {
        checked: false,
        unique: false
      }
      this.resetForm("form")
    },
    /** 取消按钮 */
    cancel() {
      this.dialogVisible = false
      this.reset()
    },
    /** 表单提交 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.submitLoading = true

          // 确保知识库类型始终为私有
          this.form.knowledgeType = "PRIVATE"

          // 前端数据验证
          const validation = validateKnowledgeBaseData(this.form)
          if (!validation.isValid) {
            this.$modal.msgError(validation.errors.join('; '))
            this.submitLoading = false
            return
          }

          // 检查名称唯一性（新增时或名称发生变化时）
          if (!this.form.knowledgeId || this.nameCheckResult.checked) {
            if (!this.nameCheckResult.unique) {
              this.$modal.msgError("知识库名称已存在，请使用其他名称")
              this.submitLoading = false
              return
            }
          }

          if (this.form.knowledgeId != null) {
            updateKnowledgeBase(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.dialogVisible = false
              this.$emit('success')
            }).finally(() => {
              this.submitLoading = false
            })
          } else {
            createKnowledgeBase(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.dialogVisible = false
              this.$emit('success')
            }).finally(() => {
              this.submitLoading = false
            })
          }
        }
      })
    },
    /** 检查名称唯一性 */
    async checkNameUnique() {
      if (!this.form.knowledgeName || this.form.knowledgeName.trim() === '') {
        this.nameCheckResult = { checked: false, unique: false }
        return
      }

      try {
        const isUnique = await checkKnowledgeNameUnique(
          this.form.knowledgeName, 
          this.form.knowledgeId
        )
        this.nameCheckResult = {
          checked: true,
          unique: isUnique
        }
      } catch (error) {
        console.error('检查名称唯一性失败:', error)
        this.nameCheckResult = { checked: false, unique: false }
      }
    },
    /** 自定义名称验证器 */
    validateKnowledgeName(rule, value, callback) {
      if (value && this.nameCheckResult.checked && !this.nameCheckResult.unique) {
        callback(new Error('该名称已存在，请使用其他名称'))
      } else {
        callback()
      }
    }
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.name-check-error {
  color: #F56C6C;
  font-size: 12px;
  margin-top: 5px;
}

.name-check-success {
  color: #67C23A;
  font-size: 12px;
  margin-top: 5px;
}

.name-check-error i,
.name-check-success i {
  margin-right: 5px;
}
</style>
