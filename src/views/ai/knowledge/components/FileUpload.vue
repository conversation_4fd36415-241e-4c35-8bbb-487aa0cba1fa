<template>
  <el-dialog 
    title="上传文档" 
    :visible.sync="dialogVisible" 
    width="600px" 
    append-to-body
    @close="handleClose"
  >
    <div class="upload-container">
      <!-- 支持的文件类型提示 -->
      <div class="file-type-tip">
        <i class="el-icon-info"></i>
        <span>支持的文件类型：{{ supportedTypesText }}</span>
      </div>

      <!-- 文件上传区域 -->
      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        :action="uploadUrl"
        :headers="uploadHeaders"
        :data="uploadData"
        :multiple="true"
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-remove="handleFileRemove"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :on-progress="handleUploadProgress"
        :file-list="fileList"
        :accept="acceptTypes"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <div>支持单个或批量上传，单个文件大小不超过 {{ maxFileSize }}MB</div>
        </div>
      </el-upload>

      <!-- 上传进度 -->
      <div v-if="uploadProgress.show" class="upload-progress">
        <div class="progress-header">
          <span>上传进度</span>
          <el-button type="text" size="mini" @click="cancelUpload">取消上传</el-button>
        </div>
        <el-progress
          :percentage="uploadProgress.percentage"
          :status="uploadProgress.status || undefined"
          :show-text="true"
        ></el-progress>
        <div class="progress-info">
          <span>{{ uploadProgress.current }} / {{ uploadProgress.total }}</span>
          <span>{{ uploadProgress.message }}</span>
        </div>
      </div>



      <!-- 上传结果统计 -->
      <div v-if="uploadResult.show" class="upload-result">
        <div class="result-header">
          <i :class="uploadResult.success ? 'el-icon-success' : 'el-icon-warning'"></i>
          <span>{{ uploadResult.title }}</span>
        </div>
        <div class="result-details">
          <p v-if="uploadResult.successCount > 0">
            成功上传：{{ uploadResult.successCount }} 个文件
          </p>
          <p v-if="uploadResult.failureCount > 0" class="error-text">
            失败：{{ uploadResult.failureCount }} 个文件
          </p>
          <p v-if="uploadResult.invalidCount > 0" class="warning-text">
            不支持的文件：{{ uploadResult.invalidCount }} 个
          </p>
          <div v-if="uploadResult.invalidFiles.length > 0" class="invalid-files">
            <span>不支持的文件：</span>
            <el-tag 
              v-for="fileName in uploadResult.invalidFiles" 
              :key="fileName"
              type="warning"
              size="mini"
              style="margin: 2px;"
            >
              {{ fileName }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button 
        type="primary" 
        @click="submitUpload"
        :loading="uploading"
        :disabled="fileList.length === 0"
      >
        {{ uploading ? '上传中...' : '开始上传' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { 
  getSupportedFileTypes,
  validateFileType,
  DEFAULT_SUPPORTED_FILE_TYPES
} from "@/api/ai/document"
import { 
  batchUploadWithProgress 
} from "@/api/ai/knowledgeService"
import { getToken } from "@/utils/auth"

export default {
  name: "FileUpload",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    knowledgeId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      uploading: false,
      fileList: [],
      supportedTypes: DEFAULT_SUPPORTED_FILE_TYPES,
      maxFileSize: 50, // MB
      uploadProgress: {
        show: false,
        percentage: 0,
        status: null, // 使用null而不是空字符串，避免ElProgress组件警告
        current: 0,
        total: 0,
        message: ''
      },
      uploadResult: {
        show: false,
        success: false,
        title: '',
        successCount: 0,
        failureCount: 0,
        invalidCount: 0,
        invalidFiles: []
      },
      lastUploadResult: null
    }
  },
  computed: {
    uploadUrl() {
      return process.env.VUE_APP_BASE_API + '/ai/documents/batch-upload'
    },
    uploadHeaders() {
      return {
        'Authorization': 'Bearer ' + getToken()
      }
    },
    uploadData() {
      return {
        knowledgeId: this.knowledgeId
      }
    },
    supportedTypesText() {
      return this.supportedTypes.join(', ').toUpperCase()
    },
    acceptTypes() {
      return this.supportedTypes.map(type => `.${type}`).join(',')
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  mounted() {
    this.loadSupportedTypes()
  },
  methods: {
    /** 加载支持的文件类型 */
    async loadSupportedTypes() {
      try {
        const types = await getSupportedFileTypes()
        this.supportedTypes = types
      } catch (error) {
        console.warn('获取支持的文件类型失败，使用默认类型')
      }
    },
    /** 文件变化处理 */
    handleFileChange(file, fileList) {
      this.fileList = fileList
      
      // 验证文件类型
      const validation = validateFileType(file.raw, this.supportedTypes)
      if (!validation.isValid) {
        this.$modal.msgError(validation.error)
        this.handleFileRemove(file, fileList)
        return
      }
      
      // 验证文件大小
      if (file.size > this.maxFileSize * 1024 * 1024) {
        this.$modal.msgError(`文件 ${file.name} 大小超过 ${this.maxFileSize}MB 限制`)
        this.handleFileRemove(file, fileList)
        return
      }
    },
    /** 文件移除处理 */
    handleFileRemove(file, fileList) {
      this.fileList = fileList
    },
    /** 上传前检查 */
    beforeUpload(file) {
      // 这里可以添加额外的上传前检查
      return true
    },
    /** 提交上传 */
    async submitUpload() {
      if (this.fileList.length === 0) {
        this.$modal.msgWarning('请选择要上传的文件')
        return
      }

      this.uploading = true
      this.resetProgress()
      this.resetResult()
      
      try {
        const files = this.fileList.map(item => item.raw)
        const result = await batchUploadWithProgress(
          this.knowledgeId, 
          files, 
          this.handleUploadProgress
        )
        
        if (result.success) {
          // 保存上传结果数据，供后续使用
          this.lastUploadResult = result
          this.$emit('success', result.data)
        } else {
          this.$modal.msgError(result.message)
        }
      } catch (error) {
        this.$modal.msgError('上传失败：' + error.message)
      } finally {
        this.uploading = false
      }
    },
    /** 上传进度处理 */
    handleUploadProgress(progress) {
      if (progress.phase === 'validation') {
        this.uploadProgress.show = true
        this.uploadProgress.message = '验证文件类型...'
        this.uploadProgress.percentage = 10

        if (progress.invalidCount > 0) {
          this.uploadResult.invalidCount = progress.invalidCount
          this.uploadResult.invalidFiles = progress.invalidFiles
        }
      } else if (progress.phase === 'uploading') {
        this.uploadProgress.message = progress.message
        this.uploadProgress.percentage = 50
      } else if (progress.phase === 'completed') {
        this.uploadProgress.percentage = 100
        this.uploadProgress.status = 'success'
        this.uploadProgress.message = '上传完成'

        // 延迟1秒后自动关闭弹窗并显示提示
        setTimeout(() => {
          this.handleUploadCompleted(progress)
        }, 1000)
      }
    },
    /** 处理上传完成 */
    handleUploadCompleted(progress) {
      if (this.lastUploadResult && this.lastUploadResult.success) {
        console.log("uploadResult: ",this.lastUploadResult);
        const result = this.lastUploadResult.data.uploaded
        const uploadedFiles = result.data || []
        const summary = result.data.summary || {}

        // 计算成功上传的文件数量（使用后端返回的data数组长度）
        const successCount = uploadedFiles.length

        // 构建提示消息
        let message = `成功上传 ${successCount} 个文件，文档正在后台处理中...`

        // 如果有无效文件，添加到提示中
        if (summary.invalidCount > 0) {
          message += `\n有 ${summary.invalidCount} 个文件格式不支持已跳过`
        }

        // 显示成功提示
        this.$modal.msgSuccess(message)

        // 关闭弹窗并重置状态
        this.handleClose()
      }
    },

    /** 上传成功处理 */
    handleUploadSuccess(response, file, fileList) {
      // 使用批量上传，这个方法可能不会被调用
    },
    /** 上传错误处理 */
    handleUploadError(err, file, fileList) {
      this.$modal.msgError(`文件 ${file.name} 上传失败`)
    },
    /** 取消上传 */
    cancelUpload() {
      this.$refs.upload.abort()
      this.uploading = false
      this.resetProgress()
    },
    /** 显示上传结果 */
    showUploadResult(summary, message) {
      this.uploadResult = {
        show: true,
        success: summary.failureCount === 0,
        title: message,
        successCount: summary.successCount,
        failureCount: summary.failureCount || 0,
        invalidCount: summary.invalidCount || 0,
        invalidFiles: this.uploadResult.invalidFiles
      }
    },
    /** 重置进度 */
    resetProgress() {
      this.uploadProgress = {
        show: false,
        percentage: 0,
        status: null, // 使用null而不是空字符串，避免ElProgress组件警告
        current: 0,
        total: 0,
        message: ''
      }
    },
    /** 重置结果 */
    resetResult() {
      this.uploadResult = {
        show: false,
        success: false,
        title: '',
        successCount: 0,
        failureCount: 0,
        invalidCount: 0,
        invalidFiles: []
      }
    },

    /** 关闭对话框 */
    handleClose() {
      this.dialogVisible = false
      this.fileList = []
      this.resetProgress()
      this.resetResult()
      this.lastUploadResult = null
    },
    /** 获取文件图标 */
    getFileIcon(fileType) {
      const iconMap = {
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        txt: 'el-icon-tickets',
        md: 'el-icon-tickets',
        html: 'el-icon-document'
      }
      return iconMap[fileType] || 'el-icon-document'
    },
    /** 获取状态类型 */
    getStatusType(status) {
      const typeMap = {
        'PENDING': 'info',
        'PROCESSING': 'warning',
        'SUCCESS': 'success',
        'FAILED': 'danger'
      }
      return typeMap[status] || 'info'
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const textMap = {
        'PENDING': '待处理',
        'PROCESSING': '处理中',
        'SUCCESS': '成功',
        'FAILED': '失败'
      }
      return textMap[status] || status
    }
  }
}
</script>

<style scoped>
.upload-container {
  padding: 20px 0;
}

.file-type-tip {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  color: #409eff;
}

.file-type-tip i {
  margin-right: 8px;
}

.upload-progress {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-size: 12px;
  color: #909399;
}



.upload-result {
  margin-top: 20px;
  padding: 15px;
  border-radius: 4px;
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-weight: bold;
}

.result-header i {
  margin-right: 8px;
  font-size: 18px;
}

.result-header .el-icon-success {
  color: #67c23a;
}

.result-header .el-icon-warning {
  color: #e6a23c;
}

.result-details p {
  margin: 5px 0;
}

.error-text {
  color: #f56c6c;
}

.warning-text {
  color: #e6a23c;
}

.invalid-files {
  margin-top: 10px;
}

.invalid-files span {
  display: block;
  margin-bottom: 5px;
  font-size: 12px;
  color: #909399;
}
</style>
