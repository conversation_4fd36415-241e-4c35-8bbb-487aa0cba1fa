<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 页面标题 -->
      <div slot="header" class="clearfix">
        <span class="card-title">AI知识库管理</span>
        <el-button 
          style="float: right; padding: 3px 0" 
          type="text"
          @click="handleRefresh"
        >
          刷新
        </el-button>
      </div>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
          <el-form-item label="知识库名称" prop="knowledgeName">
            <el-input
              v-model="queryParams.knowledgeName"
              placeholder="请输入知识库名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
              <el-option label="正常" value="0" />
              <el-option label="停用" value="1" />
            </el-select>
          </el-form-item>
          <el-form-item label="类型" prop="knowledgeType">
            <el-select v-model="queryParams.knowledgeType" placeholder="请选择类型" clearable>
              <el-option label="公共知识库" value="PUBLIC" />
              <el-option label="私有知识库" value="PRIVATE" />
            </el-select>
          </el-form-item>
          <el-form-item label="关键字" prop="keyword">
            <el-input
              v-model="queryParams.keyword"
              placeholder="搜索名称或描述"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="toolbar">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['ai:knowledgeBase:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['ai:knowledgeBase:remove']"
            >删除</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </div>

      <!-- 知识库列表 -->
      <el-table
        v-loading="loading"
        :data="knowledgeBaseList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="知识库名称" align="center" prop="knowledgeName" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-link type="primary" @click="handleDetail(scope.row)">
              {{ scope.row.knowledgeName }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="描述" align="center" prop="description" :show-overflow-tooltip="true" />
        <el-table-column label="类型" align="center" prop="knowledgeType">
          <template slot-scope="scope">
            <el-tag :type="scope.row.knowledgeType === 'PUBLIC' ? 'success' : 'info'">
              {{ getTypeText(scope.row.knowledgeType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="0"
              inactive-value="1"
              @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="文档数量" align="center" prop="documentCount">
          <template slot-scope="scope">
            <span>{{ scope.row.documentCount || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="向量数量" align="center" prop="vectorCount">
          <template slot-scope="scope">
            <span>{{ scope.row.vectorCount || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleDetail(scope.row)"
              v-hasPermi="['ai:knowledgeBase:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['ai:knowledgeBase:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-refresh"
              @click="handleRefreshStats(scope.row)"
              v-hasPermi="['ai:knowledgeBase:edit']"
            >刷新统计</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['ai:knowledgeBase:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改知识库对话框 -->
    <knowledge-base-form
      ref="knowledgeBaseForm"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script>
import {
  getKnowledgeBaseList,
  deleteKnowledgeBases,
  updateKnowledgeBase,
  refreshStatistics,
  buildKnowledgeBaseQuery,
  STATUS_TEXT_MAP,
  TYPE_TEXT_MAP
} from "@/api/ai/knowledgeBase"
import KnowledgeBaseForm from "./components/KnowledgeBaseForm"

export default {
  name: "KnowledgeBase",
  components: {
    KnowledgeBaseForm
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 知识库表格数据
      knowledgeBaseList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        knowledgeName: null,
        status: null,
        knowledgeType: null,
        keyword: null,
        orderByColumn: 'createTime',
        isAsc: 'desc'
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询知识库列表 */
    getList() {
      this.loading = true
      const query = buildKnowledgeBaseQuery(this.queryParams)
      getKnowledgeBaseList(query).then(response => {
        // API层已经处理了数据结构适配
        this.knowledgeBaseList = response.rows || []
        this.total = response.total || 0
        this.loading = false
      }).catch(error => {
        console.error('获取知识库列表失败:', error)
        this.knowledgeBaseList = []
        this.total = 0
        this.loading = false
        // 显示错误信息给用户
        this.$modal.msgError('获取知识库列表失败，请检查网络连接或联系管理员')
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.knowledgeId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.knowledgeBaseForm.openDialog()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const knowledgeId = row.knowledgeId || this.ids
      this.$refs.knowledgeBaseForm.openDialog(knowledgeId)
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.$router.push(`/ai/knowledge/detail/${row.knowledgeId}`)
    },

    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用"
      this.$modal.confirm('确认要"' + text + '""' + row.knowledgeName + '"知识库吗？').then(function() {
        return updateKnowledgeBase(row)
      }).then(() => {
        this.$modal.msgSuccess(text + "成功")
      }).catch(function() {
        row.status = row.status === "0" ? "1" : "0"
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const knowledgeIds = row.knowledgeId || this.ids
      this.$modal.confirm('是否确认删除知识库编号为"' + knowledgeIds + '"的数据项？').then(function() {
        return deleteKnowledgeBases(knowledgeIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 刷新统计信息 */
    handleRefreshStats(row) {
      this.$modal.confirm('确认要刷新"' + row.knowledgeName + '"的统计信息吗？').then(() => {
        return refreshStatistics(row.knowledgeId)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("统计信息刷新成功")
      }).catch(() => {})
    },
    /** 刷新列表 */
    handleRefresh() {
      this.getList()
    },
    /** 表单成功回调 */
    handleFormSuccess() {
      this.getList()
    },
    /** 获取状态文本 */
    getStatusText(status) {
      return STATUS_TEXT_MAP[status] || status
    },
    /** 获取类型文本 */
    getTypeText(type) {
      return TYPE_TEXT_MAP[type] || type
    }
  }
}
</script>

<style scoped>
.search-area {
  margin-bottom: 20px;
}

.toolbar {
  margin-bottom: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}
</style>
