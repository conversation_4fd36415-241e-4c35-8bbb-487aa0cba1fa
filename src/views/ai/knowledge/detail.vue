<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          <el-link @click="$router.push('/ai/knowledge')" :underline="false">
            AI知识库管理
          </el-link>
        </el-breadcrumb-item>
        <el-breadcrumb-item>知识库详情</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 知识库信息卡片 -->
    <el-card class="knowledge-info-card" v-loading="loading">
      <div slot="header" class="card-header">
        <div class="knowledge-title">
          <h2>{{ knowledgeBase.knowledgeName }}</h2>
          <div class="knowledge-meta">
            <el-tag 
              :type="knowledgeBase.knowledgeType === 'PUBLIC' ? 'success' : 'info'"
              size="small"
            >
              {{ getTypeText(knowledgeBase.knowledgeType) }}
            </el-tag>
            <el-tag 
              :type="knowledgeBase.status === '0' ? 'success' : 'danger'"
              size="small"
            >
              {{ getStatusText(knowledgeBase.status) }}
            </el-tag>
          </div>
        </div>
        <div class="knowledge-actions">
          <el-button 
            type="primary" 
            size="small"
            icon="el-icon-edit"
            @click="handleEdit"
            v-hasPermi="['ai:knowledgeBase:edit']"
          >
            编辑知识库
          </el-button>
          <el-button 
            type="warning" 
            size="small"
            icon="el-icon-refresh"
            @click="handleRefreshStats"
            v-hasPermi="['ai:knowledgeBase:edit']"
          >
            刷新统计
          </el-button>
        </div>
      </div>

      <div class="knowledge-content">
        <el-row :gutter="20">
          <el-col :span="16">
            <div class="knowledge-description">
              <h4>描述</h4>
              <p>{{ knowledgeBase.description || '暂无描述' }}</p>
            </div>
            <div class="knowledge-remark" v-if="knowledgeBase.remark">
              <h4>备注</h4>
              <p>{{ knowledgeBase.remark }}</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="knowledge-stats">
              <h4>统计信息</h4>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-value">{{ statistics.totalDocuments }}</div>
                  <div class="stat-label">文档总数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ statistics.totalVectors }}</div>
                  <div class="stat-label">向量总数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ statistics.totalChunks }}</div>
                  <div class="stat-label">分块总数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ formatFileSize(totalFileSize) }}</div>
                  <div class="stat-label">总文件大小</div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 处理状态统计 -->
        <div class="status-stats">
          <h4>文档处理状态分布</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="status-chart">
                <h5>处理状态</h5>
                <div class="status-items">
                  <div 
                    v-for="(count, status) in statistics.processStatusStats" 
                    :key="status"
                    class="status-item"
                  >
                    <el-tag :type="getProcessStatusType(status)" size="small">
                      {{ getProcessStatusText(status) }}
                    </el-tag>
                    <span class="status-count">{{ count }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="status-chart">
                <h5>向量化状态</h5>
                <div class="status-items">
                  <div 
                    v-for="(count, status) in statistics.vectorStatusStats" 
                    :key="status"
                    class="status-item"
                  >
                    <el-tag :type="getVectorStatusType(status)" size="small">
                      {{ getVectorStatusText(status) }}
                    </el-tag>
                    <span class="status-count">{{ count }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 基本信息 -->
        <div class="basic-info">
          <h4>基本信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="知识库ID">{{ knowledgeBase.knowledgeId }}</el-descriptions-item>
            <el-descriptions-item label="排序">{{ knowledgeBase.sort }}</el-descriptions-item>
            <el-descriptions-item label="创建者">{{ knowledgeBase.createBy }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ parseTime(knowledgeBase.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
            </el-descriptions-item>
            <el-descriptions-item label="更新者">{{ knowledgeBase.updateBy }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ parseTime(knowledgeBase.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>

    <!-- 文档管理区域 -->
    <el-card class="document-card">
      <div slot="header" class="card-header">
        <span class="card-title">文档管理</span>
        <div class="document-actions">
          <!-- 上传功能已集成在DocumentManager组件中 -->
        </div>
      </div>

      <!-- 文档管理组件 -->
      <document-manager 
        :knowledge-id="knowledgeId"
        @upload-success="handleDocumentChange"
        @document-deleted="handleDocumentChange"
        @cleanup-completed="handleCleanupCompleted"
      />
    </el-card>

    <!-- 知识库编辑表单 -->
    <knowledge-base-form
      ref="knowledgeBaseForm"
      @success="handleFormSuccess"
    />

    <!-- 移除重复的文件上传对话框，使用DocumentManager中的即可 -->
  </div>
</template>

<script>
import { 
  getKnowledgeBaseDetail,
  refreshStatistics,
  STATUS_TEXT_MAP,
  TYPE_TEXT_MAP
} from "@/api/ai/knowledgeBase"
import { 
  getKnowledgeBaseOverview 
} from "@/api/ai/knowledgeService"
import { 
  PROCESS_STATUS_TEXT_MAP,
  VECTOR_STATUS_TEXT_MAP
} from "@/api/ai/document"
import DocumentManager from "./components/DocumentManager"
import KnowledgeBaseForm from "./components/KnowledgeBaseForm"

export default {
  name: "KnowledgeBaseDetail",
  components: {
    DocumentManager,
    KnowledgeBaseForm
  },
  data() {
    return {
      loading: false,
      knowledgeId: null,
      knowledgeBase: {},
      statistics: {
        totalDocuments: 0,
        totalVectors: 0,
        totalChunks: 0,
        processStatusStats: {},
        vectorStatusStats: {}
      },
      documents: []
    }
  },
  computed: {
    totalFileSize() {
      return this.documents.reduce((total, doc) => total + (doc.fileSize || 0), 0)
    }
  },
  created() {
    this.knowledgeId = this.$route.params.id
    this.getKnowledgeBaseInfo()
  },
  methods: {
    /** 获取知识库信息 */
    async getKnowledgeBaseInfo() {
      this.loading = true
      try {
        const overview = await getKnowledgeBaseOverview(this.knowledgeId)
        if (overview.success) {
          this.knowledgeBase = overview.data.knowledgeBase
          this.statistics = overview.data.statistics
          this.documents = overview.data.documents
        } else {
          this.$modal.msgError(overview.message)
        }
      } catch (error) {
        this.$modal.msgError('获取知识库信息失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    /** 编辑知识库 */
    handleEdit() {
      this.$refs.knowledgeBaseForm.openDialog(this.knowledgeId)
    },
    /** 刷新统计信息 */
    handleRefreshStats() {
      this.$modal.confirm('确认要刷新统计信息吗？').then(() => {
        return refreshStatistics(this.knowledgeId)
      }).then(() => {
        this.$modal.msgSuccess("统计信息刷新成功")
        this.getKnowledgeBaseInfo()
      }).catch(() => {})
    },
    /** 表单成功回调 */
    handleFormSuccess() {
      this.getKnowledgeBaseInfo()
    },
    /** 文档变化回调 */
    handleDocumentChange() {
      this.getKnowledgeBaseInfo()
    },
    /** 上传成功回调（通过DocumentManager组件事件） */
    handleUploadSuccess() {
      this.getKnowledgeBaseInfo()
    },
    /** 清理完成回调 */
    handleCleanupCompleted(data) {
      this.$modal.msgSuccess(`成功清理 ${data.cleanedCount} 个失败文档`)
      this.getKnowledgeBaseInfo()
    },
    /** 格式化文件大小 */
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    /** 获取状态文本 */
    getStatusText(status) {
      return STATUS_TEXT_MAP[status] || status
    },
    /** 获取类型文本 */
    getTypeText(type) {
      return TYPE_TEXT_MAP[type] || type
    },
    /** 获取处理状态类型 */
    getProcessStatusType(status) {
      const typeMap = {
        'PENDING': 'info',
        'PROCESSING': 'warning',
        'SUCCESS': 'success',
        'FAILED': 'danger'
      }
      return typeMap[status] || 'info'
    },
    /** 获取向量化状态类型 */
    getVectorStatusType(status) {
      return this.getProcessStatusType(status)
    },
    /** 获取处理状态文本 */
    getProcessStatusText(status) {
      return PROCESS_STATUS_TEXT_MAP[status] || status
    },
    /** 获取向量化状态文本 */
    getVectorStatusText(status) {
      return VECTOR_STATUS_TEXT_MAP[status] || status
    }
  }
}
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
}

.knowledge-info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.knowledge-title h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #303133;
}

.knowledge-meta .el-tag {
  margin-right: 10px;
}

.knowledge-content {
  padding: 20px 0;
}

.knowledge-description,
.knowledge-remark,
.knowledge-stats,
.status-stats,
.basic-info {
  margin-bottom: 30px;
}

.knowledge-description h4,
.knowledge-remark h4,
.knowledge-stats h4,
.status-stats h4,
.basic-info h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #606266;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.knowledge-description p,
.knowledge-remark p {
  margin: 0;
  line-height: 1.6;
  color: #606266;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.status-chart h5 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #909399;
}

.status-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.status-count {
  font-weight: bold;
  color: #606266;
}

.document-card {
  margin-bottom: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}
</style>
