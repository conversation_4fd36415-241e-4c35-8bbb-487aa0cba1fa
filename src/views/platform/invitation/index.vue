<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="类型" prop="invitationType">
                <el-select v-model="queryParams.invitationType" placeholder="请选择模版类型" clearable filterable
                    style="width: 240px">
                    <el-option label="长图" value="0"></el-option>
                    <el-option label="翻页" value="1"></el-option>
                    <el-option label="海报" value="2"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="分类" prop="category">
                <el-select v-model="queryParams.category" placeholder="请选择模版分类" clearable filterable
                    style="width: 240px">
                    <el-option v-for="dict in dict.type.platform_invitation_template_category" :key="dict.value"
                        :label="dict.label" :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="名称" prop="invitationName">
                <el-input v-model="queryParams.invitationName" placeholder="模版名称" clearable style="width: 240px"
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="enable">
                <el-select v-model="queryParams.enable" placeholder="请选择模版状态" clearable filterable
                    style="width: 240px">
                    <el-option label="启用" value="1"></el-option>
                    <el-option label="禁用" value="0"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" icon="el-icon-plus" @click="handleAdd">创建新模版</el-button>
            </el-col>
            <!-- <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                    @click="handleDelete" v-hasPermi="['platform:feed:post:remove']">删除</el-button>
            </el-col> -->
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-row :gutter="12" v-loading="loading">
            <el-col :span="6" v-for="item in feedPostList" :key="item.postId">
                <el-card shadow="hover">
                    <!-- 卡片的头部位 -->
                    <template #header>
                        <div class="card-header">
                            <span>{{ item.invitationName }}</span>
                            <!-- <el-checkbox v-model="checked" :label="item.postId" @change="ids(item)">{{ item.content }}</el-checkbox> -->
                            <div>
                                <!-- 修改按钮 -->
                                <el-button type="text" icon="el-icon-edit-outline" @click="handleUpdate(item)" />
                                <!-- 删除按钮 -->
                                <el-button type="text" icon="el-icon-view" @click="handleView(item.invitationUrl)" />
                                <!-- 开关按钮 -->
                                <!-- <el-button type="text" icon="el-icon-open" @click="devicePowerBtn(item)" /> -->
                                <el-switch v-model="item.enable" style="left: 10px;" @change='changeStatus(item)'></el-switch>
                            </div>
                        </div>
                    </template>
                    <!-- 卡片显示的内容 -->
                    <div>
                        <img :src="item.photoUrl" class="image">

                        <div class="bottom clearfix">
                            <div class="details">
                                {{ 
                                    item.invitationType === 0
                                            ? "长图"
                                            : item.invitationType === 1
                                            ? "翻页"
                                            : item.invitationType === 2
                                            ? "海报"
                                            : "未知类型"
                                }}
                                 | {{ item.imageNum }}图 | {{ formatNumber(item.collectNum) }}人喜欢
                            </div>
                        </div>
                    </div>
                </el-card>
            </el-col>

        </el-row>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" :page-sizes="[4]"/>

        <!-- 新增模版 start -->
        <el-dialog :title="title" :visible.sync="visible" width="800px" v-loading="dialogLoading" append-to-body>
            <div style="height: 600px;overflow: auto;">
                <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                    <el-form-item label="模版名称" prop="invitationName">
                        <el-input type="text" v-model="form.invitationName" placeholder="请输入模版名称" />
                    </el-form-item>
                    <el-form-item label="模版类型" prop="invitationType">
                        <el-radio-group v-model="form.invitationType" size="medium">
                            <el-radio-button :label="0">长图</el-radio-button>
                            <el-radio-button :label="1">翻页</el-radio-button>
                            <el-radio-button :label="2">海报</el-radio-button>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="模版分类" prop="category">
                        <el-select v-model="form.category" placeholder="请选择模版分类" clearable filterable
                            style="width: 100%">
                            <el-option v-for="dict in dict.type.platform_invitation_template_category" :key="dict.value"
                                :label="dict.label" :value="dict.value" />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="模版链接" prop="invitationUrl">
                        <el-input placeholder="请输入内容" v-model="form.invitationUrl">
                            <template slot="prepend">https://</template>
                        </el-input>
                    </el-form-item>

                    <el-form-item label="是否启用" prop="enable">
                        <el-switch v-model="form.enable"></el-switch>
                    </el-form-item>

                    <el-form-item label="图数" prop="imageNum">
                        <!-- <el-switch v-model="form.imageNum"></el-switch> -->
                        <el-input-number v-model="form.imageNum" :min="1" label="图片数量" controls-position="right"></el-input-number>
                    </el-form-item>

                    <el-form-item label="封面" prop="photoUrl" v-if="form.videos == null || form.videos.length <= 0">
                        <image-upload v-model="form.photoUrl" :limit="1" :is-show-tip="false" />
                    </el-form-item>

                </el-form>

            </div>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 认</el-button>
                <el-button @click="handlerClean">取 消</el-button>
            </div>
        </el-dialog>
        <!-- 新增模版 end -->

    </div>
</template>

<script>
import ImagePreview from "@/components/ImagePreview/index";
import ImageUpload from "@/components/ImageUpload/index";
import { templateSave, templateUpdate, queryTemplatePage, getTemplateDetail, updateTemplateStatus } from "@/api/platform/invitation";

export default {
    name: "InvitationTemplate",
    components: { ImagePreview, ImageUpload },
    dicts: ["platform_invitation_template_category"],
    data() {
        return {
            tabList: [],
            listData: [],
            // 遮罩层
            loading: true,
            // 显示搜索条件
            showSearch: true,
            // 选中数组
            ids: [],
            multiple: true,
            // 总条数
            total: 0,
            // 表格数据
            feedPostList: [],
            // 弹出层标题
            title: "",
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 4,
                category: undefined,
                invitationType: undefined,
                invitationName: undefined
            },
            // 表单参数
            form: {
                invitationName: null,
                invitationType: null,
                category: null,
                invitationUrl: null,
                enable: null,
                photoUrl: null,
                imageNum: null
            },
            rules: {
                invitationName: [
                    { required: true, message: "模版名称不能为空", trigger: "blur" },
                ],
                invitationType: [
                    { required: true, message: "模版类型不能为空", trigger: "blur" },
                ],
                category: [
                    { required: true, message: "模版分类不能为空", trigger: "blur" },
                ],
                invitationUrl: [
                    { required: true, message: "模版链接不能为空", trigger: "blur" },
                ],
                enable: [
                    { required: true, message: "是否启用不能为空", trigger: "blur" },
                ],
                photoUrl: [
                    { required: true, message: "模版小图不能为空", trigger: "blur" },
                ],
                imageNum: [
                    { required: true, message: "模版图片数量不能为空", trigger: "blur" },
                ]
            },
            visible: false,
            fullscreenLoading: false,
            dialogLoading: false,
            enable: false
        };
    },
    created() {
        this.getList();
    },
    methods: {
        updateStatus(row) {
            this.getupdate(row.status, row.id);
        },
        /** 查询参数列表 */
        getList() {
            this.loading = true;
            queryTemplatePage(this.queryParams).then((response) => {
                this.feedPostList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 表单重置
        reset() {
            this.form = {
                invitationName: null,
                invitationType: null,
                category: null,
                invitationUrl: null,
                enable: null,
                photoUrl: null,
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.fullscreenLoading = true;
            this.reset();
            getTemplateDetail(row.id).then(res => {
                this.form = res.data;
                let photoUrls = [];
                photoUrls.push(res.data.photoUrl);
                this.form.photoUrl = photoUrls;
                this.title = "修改模版";
                this.fullscreenLoading = false;
                this.visible = true;
            });
        },
        submitForm: function () {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    this.dialogLoading = true;
                    if (this.form.photoUrl) {
                        this.form.photoUrl = this.form.photoUrl[0];
                    }
                    if (this.form.id != null) {
                        templateUpdate(this.form).then((res) => {
                            this.$modal.msgSuccess("修改成功");
                            this.getList();
                        }).finally(() => {
                            this.visible = false;
                            this.dialogLoading = false;
                        });
                    } else {
                        templateSave(this.form).then((res) => {
                            this.$modal.msgSuccess("新增成功");
                            this.getList();
                        }).finally(() => {
                            this.visible = false;
                            this.dialogLoading = false;
                        });
                    }
                }
            });
        },
        handleAdd() {
            this.title = '新增模版'
            this.reset();
            this.visible = true;
        },
        handlerClean() {
            this.visible = false;
        },
        handleView(url) {
            window.open(url, '_blank');
        },
        changeStatus(row) {
            updateTemplateStatus(row.id, row.enable).then(res=>{

            });
        },
        formatNumber(num) {
            if (num >= 1000 && num <= 10000) {
                return (num / 1000).toFixed(1) + "k";
            }
            if (num >= 10000) {
                return (num / 10000).toFixed(1) + "w";
            }
            return num.toString();
        },
    },
};
</script>
<style scoped lang="scss">
.app-container {
    background-color: aliceblue;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.image {
    width: 100%;
    display: block;
    height: 500px;
    object-fit: contain;
}

.bottom {
    margin-top: 13px;
    line-height: 12px;
}

.details {
    font-size: 14px;
    color: #999;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both
}
</style>