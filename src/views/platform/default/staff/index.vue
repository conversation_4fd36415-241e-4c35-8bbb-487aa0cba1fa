<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="员工名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="员工名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增员工</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="头像" align="center" width="300">
        <template slot-scope="scope">
          <image-preview :src="scope.row.staffAvatar" />
        </template>
      </el-table-column>
      <el-table-column label="名称" align="center" prop="staffName" :show-overflow-tooltip="true" />
      <el-table-column label="工种" align="center" prop="staffJobType" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          {{scope.row.staffJobType | formatJopType}}
        </template>
      </el-table-column>
      <el-table-column label="职位" align="center" prop="staffPost" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['platform:feed:post:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['platform:feed:post:remove']">删除</el-button>
        </template>

      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="姓名" prop="staffName">
          <el-input type="text" v-model="form.staffName" placeholder="请输入员工姓名" />
        </el-form-item>
        <el-form-item label="头像" prop="staffAvatar">
          <image-upload v-model="form.staffAvatar" :isShowTip="false" :limit="1"></image-upload>
        </el-form-item>
        <el-form-item label="工种" prop="staffJobType">
          <el-select v-model="form.staffJobType" placeholder="请选择员工工种" clearable filterable style="width: 100%">
            <el-option v-for="option in JobTypeOptions" :key="option.value" :label="option.name"
              :value="option.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="职位" prop="staffPost">
          <el-select v-model="form.staffPost" placeholder="请选择员工职位" clearable filterable style="width: 100%">
            <el-option v-for="option in postOptions" :key="option.id" :label="option.name" :value="option.name"/>
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import ImageUpload from "@/components/ImageUpload/index"
import { staff_page, staff_add, staff_detail, staff_update, staff_remove } from "@/api/platform/cmsDefualt";
export default {
  name: "DefualtCustomer",
  components: { ImageUpload },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nodeName: null
      },
      showSearch: true,
      dataList: [],
      loading: false,
      total: 0,
      title: '',
      open: false,
      form: {},
      rules: {
        staffName: [
          { required: true, message: "员工姓名不能为空", trigger: "blur" }
        ],
        staffAvatar: [
          { required: true, message: "员工头像不能为空", trigger: "blur" }
        ],
        staffJobType: [
          { required: true, message: "员工工种不能为空", trigger: "blur" }
        ],
        staffPost: [
          { required: true, message: "员工职位不能为空", trigger: "blur" }
        ]
      },
      JobTypeOptions: [
        { name: '服务人员', value: "0"},
        { name: '销售人员', value: "1"},
        { name: '管理人员', value: "2"},
      ],
      postOptions: [
        {
          id: 1,
          name: '孕产厨师'
        },
        {
          id: 2,
          name: '孕产医师'
        }, {
          id: 3,
          name: '孕产护士'
        }, {
          id: 4,
          name: '孕产月嫂'
        }, {
          id: 5,
          name: '孕产育婴员'
        }, {
          id: 6,
          name: '孕产心理咨询师'
        },
        {
          id: 7,
          name: '孕产产后康复师'
        },
        {
          id: 8,
          name: '孕产健康管理师'
        },
        {
          id: 9,
          name: '孕产母婴护理师'
        },
      ]
    }
  },
  filters: {
    formatJopType(val){
      switch(val){
        case "0":
          return "服务人员";
        case "1":
          return "销售人员";
        case "2":
          return "管理人员";
        default: 
          return "";
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    handleQuery() {

    },
    resetQuery() {

    },
    getList() {
      staff_page(this.queryParams).then(res => {
        this.dataList = res.rows;
        this.total = res.total;
      });
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增默认员工";

    },
    handleSelectionChange() {

    },
    handleUpdate(row) {
      this.reset();
      staff_detail(row.id).then(res => {
        let avatar = [];
        this.form = res.data;
        avatar.push(res.data.staffAvatar);
        this.form.staffAvatar = avatar;
        this.open = true;
        this.title = "修改默认员工";
      });
    },
    handleDelete(row) {
      this.$modal.confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        return staff_remove(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    reset() {
      this.form = {
        name: undefined,
        avatar: undefined,
        checkinDate: undefined
      };
      this.resetForm("form");
    },

    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          let data = Object.assign(this.form, {});
          data.staffAvatar = this.form.staffAvatar[0];
          if (data.id != null) {
            staff_update(data).then(res => {
              this.open = false;
              this.getList();
            });

          } else {
            staff_add(data).then(res => {
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    }
  }
}
</script>

<style scoped lang="scss"></style>