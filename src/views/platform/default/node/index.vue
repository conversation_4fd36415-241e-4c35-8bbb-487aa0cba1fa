<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="节点名称" prop="nodeName">
        <el-input v-model="queryParams.nodeName" placeholder="节点名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增节点</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="节点名称" align="center" width="300" prop="nodeName" />
      <el-table-column label="节点类型" align="center" width="300">
        <template slot-scope="scope">
          <span>{{ findDictLabelByValue(scope.row.type) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="shortOrder" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['platform:feed:post:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['platform:feed:post:remove']">删除</el-button>
        </template>

      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="open" width="400px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="节点名称" prop="nodeName">
          <el-input type="text" v-model="form.nodeName" placeholder="请输入节点名称" />
        </el-form-item>

        
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%;">
            <el-option
              :label="item.dictLabel"
              :value="item.dictValue"
              v-for="(item, index) in nodeTypeOptions"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="节点排序" prop="shortOrder">
          <!-- <el-input type="text" v-model="form.shortOrder" placeholder="请输入节点名称" /> -->
          <el-input-number v-model="form.shortOrder" style="width: 100%;" label="请输入节点排序"></el-input-number>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import ImageUpload from "@/components/ImageUpload/index"
import { page, add, detail, update, remove_node } from "@/api/platform/cmsDefualt";
export default {
  name: "DefualtNode",
  components: { ImageUpload },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nodeName: null
      },
      showSearch: true,
      dataList: [],
      loading: false,
      total: 0,
      title: '',
      open: false,
      form: {
        nodeName: undefined,
        shortOrder: 0,
        type: undefined
      },
      rules: {
        nodeName: [
          { required: true, message: "节点名称不能为空", trigger: "blur" }
        ],
        type: [
          { required: true, message: "节点类型不能为空", trigger: "blur" }
        ]
      },
      nodeTypeOptions: [
        {
          dictLabel: "产后康复",
          dictValue: "CHKF",
        },
        {
          dictLabel: "月子膳食",
          dictValue: "YZSS",
        },
        {
          dictLabel: "护理服务",
          dictValue: "HLFW",
        },
        {
          dictLabel: "其他服务",
          dictValue: "QTFW",
        },
      ],
      roleOptions: [
        {
          dictLabel: "护理",
          dictValue: "NURSE",
        },
        {
          dictLabel: "产康",
          dictValue: "POSTPARTUM",
        },
        {
          dictLabel: "厨师",
          dictValue: "CHEF",
        },
        {
          dictLabel: "月嫂",
          dictValue: "MATERNITY_NANNY",
        },
      ],
    }
  },
  created() {
    this.getList();
  },
  methods: {
    handleQuery() {

    },
    resetQuery() {

    },
    getList() {
      page(this.queryParams).then(res => {
        this.dataList = res.rows;
        this.total = res.total;
      });
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增节点";

    },
    handleSelectionChange() {

    },
    handleUpdate(row) {
      this.reset();
      detail(row.id).then(res=>{
        this.form = res.data;
        this.open = true;
        this.title = "修改节点";
      });
    },
    handleDelete(row) {
      this.$modal.confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        return remove_node(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    reset() {
      this.form = {
        nodeName: undefined,
        shortOrder: undefined,
      };
      this.resetForm("form");
    },

    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            update(this.form).then(res=>{
              this.open = false;
              this.getList();
            });

          } else {
            add(this.form).then(res => {
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    findDictLabelByValue(dictValue) {
      const item = this.nodeTypeOptions.find(item => item.dictValue === dictValue);
      return item ? item.dictLabel : null;
    }
  }
}
</script>

<style scoped lang="scss"></style>