<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="用户名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="员工名称"></el-input>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增动态</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="发布人" align="center" prop="authorName" :show-overflow-tooltip="true" />
      <el-table-column label="头像" align="center" width="300">
        <template slot-scope="scope">
          <image-preview :src="scope.row.authorAvatar" />
        </template>
      </el-table-column>

      <el-table-column label="类型" align="center" prop="type" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          {{ scope.row.type | formatType }}
        </template>
      </el-table-column>
      <el-table-column label="内容" align="center" prop="content" :show-overflow-tooltip="true" />
      <el-table-column label="图文|视频" align="center" prop="staffPost" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.contentPhotos != null && scope.row.contentPhotos.length > 0">图文</el-tag>
          <el-tag type="warning" v-else-if="scope.row.videos != null && scope.row.contentPhotos.length > 0">视频</el-tag>
          <el-tag type="info" v-else>文本</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="发布时间" align="center" prop="authorTime" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['platform:feed:post:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['platform:feed:post:remove']">删除</el-button>
        </template>

      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择动态类型" clearable filterable style="width: 100%">
            <el-option v-for="option in feedpostType" :key="option.value" :label="option.label" :value="option.value" />
          </el-select>
        </el-form-item>
        <template v-if="form.type === 'USER'">
          <el-form-item label="用户" prop="authorId">
            <el-select v-model="form.authorId" placeholder="请选择用户" clearable filterable style="width: 100%">
              <el-option v-for="option in customerList" :key="option.id" :label="option.name"
                :value="option.id" />
            </el-select>
          </el-form-item>
        </template>

        <template v-if="form.type === 'STAFF'">
          <el-form-item label="员工" prop="authorId">
            <el-select v-model="form.authorId" placeholder="请选择员工" clearable filterable style="width: 100%" @change="staffChange">
              <el-option v-for="option in staffList" :key="option.id" :label="option.staffName"
                :value="option.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="客户" prop="defaultCustomerId">
            <el-select v-model="form.defaultCustomerId" placeholder="请选择客户" clearable filterable style="width: 100%" >
              <el-option v-for="option in clientList" :key="option.defaultCuatomerId" :label="option.customerName"
                :value="option.defaultCuatomerId" />
            </el-select>
          </el-form-item>


          <el-form-item label="节点" prop="defaultNodeId">
            <el-select v-model="form.defaultNodeId" placeholder="请选择节点" clearable filterable style="width: 100%">
              <el-option v-for="option in nodeList" :key="option.id" :label="option.nodeName"
                :value="option.id" />
            </el-select>
          </el-form-item>
        </template>

        <el-form-item label="时间" prop="authorTime">
          <el-date-picker v-model="form.authorTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择动态时间"
            style="width: 100%;">
          </el-date-picker>
        </el-form-item>


        <el-form-item label="动态内容" prop="content">
          <el-input type="textarea" :rows="5" v-model="form.content" placeholder="请输入动态内容" />
        </el-form-item>
        <el-form-item label="图片" prop="contentPhotos">
          <image-upload v-model="form.contentPhotos" :limit="9" />
        </el-form-item>
        <el-form-item label="视频" prop="videos">
          <file-upload v-model="form.videos" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import ImageUpload from "@/components/ImageUpload/index"
import ImagePreview from "@/components/ImagePreview/index"
import FileUpload from "@/components/FileUpload/index"
import { staff_page, customer_page, feedpost_add, feddpost_detail, feddpost_update, feedpost_remove, 
  get_staff_customer_list, page as node_list, get_feedpost_page } from "@/api/platform/cmsDefualt";
export default {
  name: "DefualtCustomer",
  components: { ImageUpload },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nodeName: null
      },
      showSearch: true,
      dataList: [],
      loading: false,
      total: 0,
      title: '',
      open: false,
      form: {},
      rules: {
        type: [
          { required: true, message: "请选择动态类型", trigger: "blur" }
        ],
        authorId: [
          { required: true, message: "动态发布人不能为空", trigger: "blur" }
        ],
        defaultCustomerId: [
          { required: true, message: "客户不能为空", trigger: "blur" }
        ],
        defaultNodeId: [
          { required: true, message: "节点不能为空", trigger: "blur" }
        ],
        authorTime: [
          { required: true, message: "发布时间不能为空", trigger: "blur" }
        ],
        content: [
          { required: true, message: "动态内容不能为空", trigger: "blur" }
        ]
      },
      feedpostType: [
        { label: "员工动态", value: "STAFF" },
        { label: "会所动态", value: "CLUB" },
        { label: "客户动态", value: "USER" },
      ],
      customerList: [],
      staffList: [],
      clientList: [],
      nodeList: []
    }
  },
  filters: {
    formatType(val) {
      switch (val) {
        case "STAFF":
          return "员工动态";
        case "CLUB":
          return "会所动态";
        case "USER":
          return "客户动态";
        default:
          return "未知";
      }
    }
  },
  async created() {
    this.getList();
    this.getCustomerList();
    this.getStaffList();
    this.getNodeList();

  },
  methods: {
    handleQuery() {

    },
    resetQuery() {

    },
    getList() {
      get_feedpost_page(this.queryParams).then(res => {
        this.dataList = res.rows;
        this.total = res.total;
      });
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增默认动态";

    },
    handleSelectionChange() {

    },
    handleUpdate(row) {
      this.reset();
      feddpost_detail(row.id).then(res => {
        this.form = res.data;
        this.open = true;
        this.title = "修改默认动态";
        if(res.data.type === 'STAFF'){
          this.getClientList(res.data.authorId);
        }
        
      });
    },
    handleDelete(row) {
      this.$modal.confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        return feedpost_remove(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    reset() {
      this.form = {
        type: undefined,
        content: undefined,
        contentPhotos: undefined,
        videos: undefined,
        defaultNodeId: undefined,
        defaultCustomerId: undefined,
        authorId: undefined,
        authorTime: undefined,
      };
      this.resetForm("form");
    },

    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            feddpost_update(this.form).then(res => {
              this.open = false;
              this.getList();
            });

          } else {
            feedpost_add(this.form).then(res => {
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    getCustomerList(){
      let params = {
        pageNum: 1,
        pageSize: 100
      }
      customer_page(params).then(res=>{
        this.customerList = res.rows;
      });
    },
    getStaffList(){
      let params = {
        pageNum: 1,
        pageSize: 100
      }
      staff_page(params).then(res=>{
        this.staffList = res.rows;
      });
    },
    getClientList(id){
      get_staff_customer_list(id).then(res=>{
        this.clientList = res.data;
      });
    },
    getNodeList(){
      let params = {
        pageNum: 1,
        pageSize: 100
      }
      node_list(params).then(res=>{
        this.nodeList = res.rows;
      });
    },
    staffChange(val){
      this.clientList = [];
      this.$set(this.form, 'defaultCustomerId', null)
      this.getClientList(val);
    },
  }
}
</script>

<style scoped lang="scss"></style>