<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="客户名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="客户名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增客户</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="头像" align="center" width="300">
        <template slot-scope="scope">
          <image-preview :src="scope.row.avatar" />
        </template>
      </el-table-column>
      <el-table-column label="名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="入住日期" align="center" prop="checkinDate" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['platform:feed:post:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['platform:feed:post:remove']">删除</el-button>
        </template>

      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="客户名称" prop="name">
          <el-input type="text" v-model="form.name" placeholder="请输入客户名称" />
        </el-form-item>
        <el-form-item label="入住日期" prop="checkinDate">
          <el-date-picker v-model="form.checkinDate" type="date" value-format="yyyy-MM-dd" placeholder="选择入住日期"
            style="width: 100%;">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="头像" prop="avatar">
          <image-upload v-model="form.avatar" :isShowTip="false" :limit="1"></image-upload>
        </el-form-item>

        <el-form-item label="绑定员工" prop="avatar">
          <el-table :data="staffList" style="width: 100%" height="250" @selection-change="handleSelectionChange"
            ref="staffTable">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column prop="staffAvatar" label="头像" width="100">
              <template slot-scope="scope">
                <image-preview :src="scope.row.staffAvatar" />
              </template>
            </el-table-column>
            <el-table-column prop="staffName" label="姓名" width="100">
            </el-table-column>
            <el-table-column prop="staffPost" label="职位" width="100">
            </el-table-column>
            <el-table-column prop="staffJobType" label="工种">
              <template slot-scope="scpoe">
                {{ scpoe.row.staffJobType | formatJopType }}
              </template>
            </el-table-column>
          </el-table>

        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import ImageUpload from "@/components/ImageUpload/index"
import ImagePreview from "@/components/ImagePreview/index"
import { customer_page, customer_add, customer_detail, customer_update, customer_remove, staff_page } from "@/api/platform/cmsDefualt";
export default {
  name: "DefualtCustomer",
  components: { ImageUpload },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nodeName: null
      },
      showSearch: true,
      dataList: [],
      loading: false,
      total: 0,
      title: '',
      open: false,
      form: {},
      rules: {
        name: [
          { required: true, message: "客户名称不能为空", trigger: "blur" }
        ],
        avatar: [
          { required: true, message: "客户头像不能为空", trigger: "blur" }
        ],
        checkinDate: [
          { required: true, message: "客户入住日期不能为空", trigger: "blur" }
        ]
      },
      staffList: [],
      staffIdList: []
    }
  },
  created() {
    this.getList();
    this.getStaffList();
  },
  watch: {
    staffIdList(val) {
      this.$nextTick(() => {
        for (const row of val) {
          this.$refs['staffTable'].toggleRowSelection(this.staffList.find(item => { return row == item.id }), true);
        }
      });
    }
  },
  filters: {
    formatJopType(val) {
      switch (val) {
        case "0":
          return "服务人员";
        case "1":
          return "销售人员";
        case "2":
          return "管理人员";
        default:
          return "";
      }
    }
  },
  methods: {
    handleQuery() {

    },
    resetQuery() {

    },
    getList() {
      customer_page(this.queryParams).then(res => {
        this.dataList = res.rows;
        this.total = res.total;
      });
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增默认客户";

    },
    handleUpdate(row) {
      this.reset();
      const that = this;
      customer_detail(row.id).then(res => {
        let avatar = [];
        this.form = res.data;
        avatar.push(res.data.avatar);
        this.form.avatar = avatar;
        this.open = true;
        this.title = "修改默认客户";
        this.staffIdList = res.data.staffIdList
      });
    },
    handleDelete(row) {
      this.$modal.confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        return customer_remove(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    reset() {
      this.form = {
        name: undefined,
        avatar: undefined,
        checkinDate: undefined
      };
      this.resetForm("form");
    },

    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          let data = Object.assign(this.form, {});
          data.avatar = this.form.avatar[0];
          if (data.id != null) {
            customer_update(data).then(res => {
              this.open = false;
              this.getList();
            });

          } else {
            customer_add(data).then(res => {
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    getStaffList() {
      let params = {
        pageNum: 1,
        pageSize: 100
      }
      staff_page(params).then(res => {
        this.staffList = res.rows;
      });
    },
    handleSelectionChange(val) {
      this.form.staffIdList = val.map(item => item.id);
    }

  }
}
</script>

<style scoped lang="scss"></style>