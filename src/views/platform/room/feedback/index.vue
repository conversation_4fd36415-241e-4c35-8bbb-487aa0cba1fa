<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户" prop="authorId">
        <el-select v-model="queryParams.authorId" placeholder="请选择用户" clearable filterable style="width: 240px">
          <el-option v-for="option in wechatUserOptions" :key="option.value" :label="option.label"
            :value="option.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="满意度" prop="satisfaction">
        <el-select v-model="queryParams.postType" placeholder="请选择满意度" clearable style="width: 240px">
          <el-option v-for="dict in satisfactionOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['platform:feed:post:customer:add']">新增反馈</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['platform:feed:post:remove']">删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="feedbackList" @expand-change="expandChange"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column type="expand">
        <template slot-scope="scope">
          <div class="block">
            <el-timeline v-if="scope.row.status === '1' || scope.row.status === '2'">
              <el-timeline-item :timestamp="reply.createTime" placement="top" v-for="reply in replyList"
                :key="reply.todoId">
                <el-card>
                  <h4>{{ reply.replyContent }}</h4>
                  <p>回复于 {{ reply.createTime }}</p>
                </el-card>
              </el-timeline-item>
              <!-- <el-timeline-item timestamp="2018/4/3" placement="top">
                <el-card>
                  <h4>更新 Github 模板</h4>
                  <p>王小虎 回复于 2018/4/3 20:46</p>
                </el-card>
              </el-timeline-item> -->
              <el-timeline-item timestamp="2018/4/2" placement="top" v-if="scope.row.status === '2'">
                <el-card>
                  <h4 v-if="scope.row.resultSatisfaction === '2'" text="">处理结果满意度： 满意</h4>
                  <h4 v-if="scope.row.resultSatisfaction === '3'">处理结果满意度： 一般</h4>
                  <h4 v-if="scope.row.resultSatisfaction === '4'">处理结果满意度： 不满意</h4>
                  <h4 v-else>等待用户确认处理结果满意度</h4>
                </el-card>
              </el-timeline-item>
            </el-timeline>
            <el-empty description="暂无回复" v-if="scope.row.status === '0'"></el-empty>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="客户" align="center" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.customerName }} ({{ scope.row.customerPhone }})</span>
        </template>
      </el-table-column>
      <el-table-column label="员工" align="center" width="150">
        <template slot-scope="scope">
          <el-row>
            <el-col :span="4">
              <el-avatar :size="40" :src="scope.row.avatar"></el-avatar>
            </el-col>
            <el-col :span="20">
              <span>{{ scope.row.nickname }}</span>
            </el-col>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column label="反馈内容" align="center" prop="content" :show-overflow-tooltip="true" />
      <el-table-column label="标签" align="center">
        <template slot-scope="scope">
          <el-tag v-for="tag in scope.row.tags" :key="tag" type="info">{{ tag }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="反馈时间" align="center" prop="createTime" :show-overflow-tooltip="true"></el-table-column>

      <el-table-column label="满意度" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.satisfaction === '2'" type="success">满意</el-tag>
          <el-tag v-if="scope.row.satisfaction === '3'" type="warning">一般</el-tag>
          <el-tag v-if="scope.row.satisfaction === '4'" type="danger">不满意</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.status === '0'">待处理</span>
          <span v-if="scope.row.status === '1'">处理中</span>
          <span v-if="scope.row.status === '2'">已处理</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-chat-line-round" @click="reply(scope.row)"
            v-if="scope.row.status !== '2'"
            v-hasPermi="['platform:feed:post:update']">回复</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['platform:feed:post:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['platform:feed:post:remove']">删除</el-button>
        </template>

      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 新增反馈对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="客户" prop="customerId">
              <el-select v-model="form.customerId" placeholder="请选择客户" @change="customerSelectChange" clearable
                filterable style="width: 100%">
                <el-option v-for="option in customerOptions" :key="option.value" :label="option.label"
                  :value="option.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="服务人员" prop="staffId">
              <el-select v-model="form.staffId" placeholder="请选择服务人员" clearable filterable style="width: 100%">
                <el-option v-for="option in staffList" :key="option.value" :label="option.label"
                  :value="option.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="满意度" prop="satisfaction">
              <el-select v-model="form.satisfaction" placeholder="请选择满意度" clearable style="width: 100%">
                <el-option v-for="dict in satisfactionOptions" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="标签" prop="tags">
          <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll"
            @change="handleCheckAllChange">全选</el-checkbox>
          <div style="margin: 15px 0;"></div>
          <el-checkbox-group v-model="checkedTags" @change="handleCheckedCitiesChange">
            <el-checkbox v-for="tag in tagList" :label="tag" :key="tag">{{ tag }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="反馈内容" prop="content">
          <el-input type="textarea" :rows="5" v-model="form.content" placeholder="请输入动态内容" />
        </el-form-item>
        <el-form-item label="时间" prop="createTime">
          <el-date-picker v-model="form.createTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期时间" style="width: 100%;">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { queryOptions } from "@/api/system/tenant";
import { queryPage, createRoomFeedback, queryReplyList } from "@/api/platform/feedback";
import { queryWechatUserOptions } from "@/api/platform/wechatUser";
import { getCustomerOptions } from "@/api/platform/customer";
import { getCustomerServicePersonnelOptions, replyRoomFeedback } from "@/api/platform/room";
const tagOptions = [
  '细心',
  '有责任感',
  '做事靠谱',
  '可信赖',
  '耐心细致',
  '热情友好',
  '专业护理',
  '情感支持',
];
export default {
  name: "Feedback",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 选中数组
      ids: [],
      multiple: true,
      // 总条数
      total: 0,
      // 表格数据
      feedbackList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        configName: undefined,
        configKey: undefined,
        configType: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        customerId: [
          { required: true, message: "客户不能为空", trigger: "blur" }
        ],
        staffId: [
          { required: true, message: "服务人员不能为空", trigger: "blur" }
        ],
        content: [
          { required: true, message: "反馈内容不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "反馈时间不能为空", trigger: "blur" }
        ],
        satisfaction: [
          { required: true, message: "满意度不能为空", trigger: "blur" }
        ]
      },
      tenantOptions: [],
      satisfactionOptions: [
        { label: "满意", value: "2" },
        { label: "一般", value: "3" },
        { label: "不满意", value: "4" }
      ],
      wechatUserOptions: [],
      customerOptions: [],
      staffList: [],
      tagList: tagOptions,
      checkAll: false,
      isIndeterminate: true,
      checkedTags: ['细心', '有责任感',],
      replyList: []
    };
  },
  created() {
    this.getList();
    this.queryTenantOptions();
    queryWechatUserOptions().then(response => {
      this.wechatUserOptions = response.data;
    });
    getCustomerOptions().then(response => {
      this.customerOptions = response.data;
    });
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      queryPage(this.queryParams).then(response => {
        this.feedbackList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    queryTenantOptions() {
      queryOptions().then(response => {
        this.tenantOptions = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        customerId: undefined,
        staffId: undefined,
        tags: [],
        content: undefined,
        createTime: undefined,
        satisfaction: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
    },
    /** 提交按钮 */
    submitForm: function () {
      this.form.tags = this.checkedTags;
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.feedbackId != null) {
            updateStaffFeedPost(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            createRoomFeedback(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 删除动态
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal.confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        return removeFeedPost(postIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.postId);
      this.multiple = !selection.length;
    },
    customerSelectChange(value) {
      if (value === '' || value === undefined || value === null) {
        return;
      }
      getCustomerServicePersonnelOptions(value).then(response => {
        this.staffList = response.data;
      });
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增房间反馈";
    },
    handleCheckAllChange(val) {
      this.checkedTags = val ? tagOptions : [];
      this.isIndeterminate = false;
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.tagList.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.tagList.length;
    },
    expandChange(row, expanded) {
      const { feedbackId } = row;
      this.replyList = [];
      queryReplyList(feedbackId).then(response => {
        this.replyList = response.data;
      });
    },
    reply(row) {
      const { feedbackId } = row;
      this.$prompt('请输入回复内容', '提示', {
        confirmButtonText: '回复',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        replyRoomFeedback({ feedbackId, replyContent: value }).then(() => {
          this.$message({
            type: 'success',
            message: '回复成功'
          });
          this.getList();
        });
      });
    }

  }
};
</script>
