<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="房型名称" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入房型名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['platform:suites:hot:select']">选择房型</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleCancelRecommended"
          v-hasPermi="['platform:suites:hot:cancel']">取消推荐</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="suitesList" @selection-change="handleSuitesSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="房型名称" align="center" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.roomName }} ({{ scope.row.roomType }})</span>
        </template>
      </el-table-column>
      <el-table-column label="房型照片" align="center" width="200">
        <template slot-scope="scope">
          <image-preview :src="scope.row.suitePhotos[0]"></image-preview>
        </template>
      </el-table-column>
      <el-table-column label="朝向" align="center" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.orientation }}</span>
        </template>
      </el-table-column>
      <el-table-column label="面积" align="center" prop="minArea" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleCancel(scope.row)"
            v-hasPermi="['platform:suites:hot:cancel']">取消推荐</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 选择推荐房型 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <!-- 表格 -->
      <div style="height: 600px;overflow: auto;">
        <el-table :data="tableData" border style="width: 100%" @selection-change="handleSelectionChange"
          ref="multipleTable" v-loading="loading" @row-click="clickRow">
          <!-- 多选框 -->
          <el-table-column type="selection" width="55">
          </el-table-column>
          <!-- 列名 -->
          <el-table-column label="房型" width="200">
            <template slot-scope="scope">
              <span>{{ scope.row.roomName }} ({{ scope.row.roomType }})</span>
            </template>
          </el-table-column>
          <el-table-column prop="age" label="图片" width="200">
            <template slot-scope="scope">
              <image-preview :src="scope.row.suitePhotos[0]"></image-preview>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="朝向">
            <template slot-scope="scope">
              <span>{{ scope.row.orientation }}</span>
            </template>
          </el-table-column>
          <el-table-column label="面积" align="center" prop="minArea" :show-overflow-tooltip="true" />
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitSelection">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { page, setRecommended, cancelRecommended } from "@/api/platform/suite";
import ImagePreview from "@/components/ImagePreview/index";
export default {
  name: "RecommendedSuites",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 选中数组
      ids: [],
      multiple: true,
      // 总条数
      total: 0,
      // 表格数据
      suitesList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        isRecommended: true
      },
      tableData: [],
      selectedItems: [],

    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      this.queryParams.isRecommended = true;
      page(this.queryParams).then(response => {
        this.suitesList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getTableData() {
      this.loading = true;
      this.queryParams.isRecommended = false;
      page(this.queryParams).then(response => {
        this.tableData = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.selectedItems = [];
      this.reset();
    },
    // 表单重置
    reset() {
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSuitesSelectionChange(selection) {
      this.ids = selection.map(item => item.suiteId);
      this.multiple = !selection.length;
    },
    handleAdd() {
      this.getTableData();
      this.reset();
      this.open = true;
      this.title = "选择房型";
    },
    // 当选择发生变化时，更新选中的数据
    handleSelectionChange(val) {
      this.selectedItems = val;
    },
    // 确认选中的数据
    submitSelection() {
      if (this.selectedItems.length <= 0) {
        this.open = false;
        return;
      }
      const data = this.selectedItems.map(item => item.suiteId).join(",");
      setRecommended(data).then(res => {
        this.$message({
          type: 'success',
          message: '推荐成功'
        });
        this.getList();
      }).finally(() => {
        this.open = false;
      });
    },
    handleCancelRecommended(){
      const data = this.ids.join(",");
      cancelRecommended(data).then(res=>{
        this.$message({
          type: 'success',
          message: '取消热门推荐成功'
        });
        this.getList();
      });

    },
    handleCancel(row){
      cancelRecommended(row.suiteId).then(res=>{
        this.$message({
          type: 'success',
          message: '取消热门推荐成功'
        });
        this.getList();
      });
    },
    clickRow(row){
      this.$refs.multipleTable.toggleRowSelection(row);
    }
  }
};
</script>