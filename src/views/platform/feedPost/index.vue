<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="用户" prop="authorId">
        <el-select
          v-model="queryParams.authorId"
          placeholder="请选择用户"
          clearable
          filterable
          style="width: 240px"
          @change="handleChange"
        >
          <el-option
            v-for="option in wechatUserOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="动态类型" prop="postType">
        <el-select
          v-model="queryParams.postType"
          placeholder="请选择动态类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in postTypeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="附件" prop="isAttachments">
        <el-select
          v-model="queryParams.isAttachments"
          placeholder="请选择是否有附件"
          clearable
          style="width: 240px"
        >
          <el-option label="有附件" value="true" />
          <el-option label="无附件" value="false" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="是否精选" prop="isFeatured">
        <el-select
          v-model="queryParams.isFeatured"
          placeholder="请选择是否为精选动态"
          clearable
          style="width: 240px"
        >
          <el-option label="精选动态" value="true" />
          <el-option label="非精选动态" value="false" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="标签" prop="nodeId">
        <el-select/
          v-model="queryParams.nodeId"
          placeholder="请选择标签"
          clearable
          filterable
          style="width: 240px"
        >
          <el-option
            v-for="option in nodeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >发布动态</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleClubAdd"
          >发布会所动态</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="choiceStyle"
          >选择动态样式</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['platform:feed:post:import']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['platform:feed:post:remove']"
          >删除</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="feedPostList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column
        label="昵称"
        align="center"
        width="100"
        prop="nickname"
      />
      <el-table-column label="头像" align="center">
        <template slot-scope="scope">
          <image-preview :src="scope.row.avatar" />
        </template>
      </el-table-column>
      <el-table-column label="内容" align="center" width="300px">
        <template slot-scope="scope">
          <div style="height: auto; overflow-wrap: break-word">
            {{ scope.row.content }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="点赞" align="center">
        <template slot-scope="scope">
          <span>{{
            scope.row.likesCount == null ? 0 : scope.row.likesCount
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="评论" align="center">
        <template slot-scope="scope">
          <span>{{
            scope.row.commentsCount == null ? 0 : scope.row.commentsCount
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="收藏" align="center">
        <template slot-scope="scope">
          <span>{{
            scope.row.favoriteCount == null ? 0 : scope.row.favoriteCount
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分享" align="center">
        <template slot-scope="scope">
          <span>{{
            scope.row.shareCount == null ? 0 : scope.row.shareCount
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="发布时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否精选" align="center" prop="isFeatured">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isFeatured"
            :active-value="true"
            :inactive-value="false"
            @change="handleFeaturedChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['platform:feed:post:update']"
            v-loading.fullscreen.lock="fullscreenLoading"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['platform:feed:post:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog title="选择动态样式" :visible.sync="dialogTableVisible">
      <el-table :data="listData" align="center">
        <el-table-column
          property="name"
          label="名称"
          width="150"
        ></el-table-column>
        <el-table-column property="name" label="图片" width="300">
          <template slot-scope="scope">
            <image-preview
              :src="scope.row.picture"
              style="width: 100px; height: 100px"
            />
          </template>
        </el-table-column>
        <el-table-column property="status" label="状态">
          <template slot-scope="scope">
            <p v-if="scope.row.status">在线</p>
            <p v-if="!scope.row.status">离线</p>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="200"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="updateStatus(scope.row)"
              v-if="scope.row.status"
              >离线</el-button
            >
            <el-button
              size="mini"
              type="text"
              @click="updateStatus(scope.row)"
              v-if="!scope.row.status"
              >上线</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 动态导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
            >下载模板</el-link
          >
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 新增动态 start -->
    <el-dialog
      title="发布动态"
      :visible.sync="create_visible"
      width="800px"
      v-loading="dialogLoading"
      append-to-body
    >
      <div style="height: 600px; overflow: auto">
        <el-form label-width="80px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="标题" prop="title">
                <el-input
                  type="text"
                  :rows="8"
                  v-model="form.title"
                  placeholder="请输入标题"
                />
                <!-- <el-cascader v-model="form.title" :props="props" style="width: 100%;" ref="titleRef"
                  placeholder="请选择 房间/用户/标签" :disabled="title_disabled"></el-cascader> -->
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="时间" prop="date">
                <el-date-picker
                  v-model="form.date"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="请选择动态发布时间"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="用户" prop="authorId">
                <el-select
                  v-model="form.authorId"
                  placeholder="请选泽用户"
                  style="width: 100%"
                  @change="getTeacherId"
                >
                  <el-option
                    v-for="item in customerOptions"
                    :key="item.userId"
                    :label="item.label"
                    :value="item.userId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="标签" prop="taskNodeId">
                <!-- <el-input type="text" :rows="8" v-model="form.title" placeholder="请选择标签" /> -->
                <el-select
                  v-model="form.taskNodeId"
                  placeholder="请选择标签"
                  style="width: 100%"
                  @change="getTeacherId1"
                >
                  <el-option
                    v-for="item in lableOptions"
                    :key="item.taskNodeId"
                    :label="item.nodeName"
                    :value="item.taskNodeId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="内容" prop="content">
            <el-input
              type="textarea"
              :rows="8"
              v-model="form.content"
              placeholder="请输入动态内容"
            />
            <el-popover
              placement="bottom"
              title="请选择要使用的动态模版"
              width="800"
              trigger="click"
              v-if="stencilList.length > 0"
              ref="popoverRef"
            >
              <p
                :class="stencilIndex == index ? 'activeStencil' : 'stencil'"
                v-for="(item, index) in stencilList"
                :key="index"
                @click="stencil(index, item)"
                style="cursor: pointer"
              >
                {{ item.content }}
              </p>
              <!-- <el-button >选择模版</el-button> -->
              <!-- <el-link slot="reference">查看模版<i class="el-icon-view el-icon--right"></i> </el-link> -->
              <div class="" slot="reference" style="cursor: pointer">
                查看模版
              </div>
            </el-popover>
          </el-form-item>
          <el-form-item
            label="图片"
            prop="contentPhotos"
            v-if="form.videos == null || form.videos.length <= 0"
          >
            <image-upload v-model="form.contentPhotos" :limit="9" />
          </el-form-item>
          <el-form-item
            label="视频"
            prop="videos"
            v-if="form.contentPhotos == null || form.contentPhotos.length <= 0"
          >
            <file-upload :limit="1" v-model="form.videos" />
          </el-form-item>
          <!-- <el-form-item label="模板" v-if="stencilList.length > 0">
            <p :class="stencilIndex == index ? 'activeStencil' : 'stencil'" v-for="(item, index) in stencilList"
              :key="index" @click="stencil(index, item)" style='cursor:pointer;'>
              {{ item.content }}
            </p>
          </el-form-item> -->
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="customerSubmitForm">确 认</el-button>
        <el-button @click="handlerCreateClean">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 新增动态 end -->

    <!-- 新增会所动态 start -->
    <el-dialog
      :title="club_title"
      :visible.sync="club_visible"
      width="800px"
      v-loading="dialogLoading"
      append-to-body
    >
      <div style="height: 600px; overflow: auto">
        <el-form
          ref="club_form"
          :model="club_form"
          :rules="club_rules"
          label-width="80px"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="用户" prop="authorId">
                <el-select
                  v-model="club_form.authorId"
                  placeholder="请选择用户"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in wechatUserOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="时间" prop="date">
                <el-date-picker
                  v-model="club_form.date"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="请选择动态发布时间"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="内容" prop="content">
            <el-input
              type="textarea"
              :rows="8"
              v-model="club_form.content"
              placeholder="请输入动态内容"
            />
          </el-form-item>
          <el-form-item
            label="图片"
            prop="contentPhotos"
            v-if="club_form.videos == null || club_form.videos.length <= 0"
          >
            <image-upload v-model="club_form.contentPhotos" :limit="9" />
          </el-form-item>
          <el-form-item
            label="视频"
            prop="videos"
            v-if="
              club_form.contentPhotos == null ||
              club_form.contentPhotos.length <= 0
            "
          >
            <file-upload :limit="1" v-model="club_form.videos" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitClubForm">确 认</el-button>
        <el-button @click="clubCancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 新增会所动态 end -->
  </div>
</template>

<script>
import { getList as getRoomList, getRoomUserList } from "@/api/platform/room";
import { getTemplateByNodeId } from "@/api/platform/feedPostTemplate";
import {
  queryPage,
  featuredFeedPost,
  removeFeedPost,
  create,
  page,
  update,
  getQueryStaffName,
  getQueryRooms,
  listByType,
  listByLabel,
  getDetail,
  updateFeedPost,
  createClubPost,
} from "@/api/platform/feedPost";
import ImagePreview from "@/components/ImagePreview/index";
import { getToken } from "@/utils/auth";
import { queryWechatUserOptions } from "@/api/platform/wechatUser";
import { getNodeOptions } from "@/api/platform/nodeDeployment";
import ImageUpload from "@/components/ImageUpload/index";
import FileUpload from "@/components/FileUpload/index";

export default {
  name: "FeedPost",
  components: { ImagePreview, ImageUpload },
  data() {
    return {
      userId: "",
      authorId: "",
      stencilList: [],
      tabList: [],
      roomList: [],
      roleList: [
        {
          name: "客户",
          roleCode: "CUSTOMER",
        },
        {
          name: "护理",
          roleCode: "NURSE",
        },
        {
          name: "产康",
          roleCode: "POSTPARTUM",
        },
        {
          name: "厨师",
          roleCode: "CHEF",
        },
      ],
      stencilIndex: -1,
      dialogTableVisible: false,
      listData: [],
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 选中数组
      ids: [],
      multiple: true,
      // 总条数
      total: 0,
      // 表格数据
      feedPostList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        configName: undefined,
        configKey: undefined,
        configType: undefined,
        authorId: "",
      },
      // 表单参数
      form: {
        content: null,
        date: null,
        title: null,
        contentPhotos: null,
        videos: null,
        taskNodeId: null,
        authorId: null,
      },
      // 表单校验
      club_rules: {
        authorId: [
          { required: true, message: "用户不能为空", trigger: "blur" },
        ],
        content: [
          { required: true, message: "动态内容不能为空", trigger: "blur" },
        ],
        date: [
          { required: true, message: "动态发布时间不能为空", trigger: "blur" },
        ],
        taskNodeId: [
          { required: true, message: "标签不能为空", trigger: "blur" },
        ],
        authorId: [
          { required: true, message: "用户不能为空", trigger: "blur" },
        ],
      },
      rules1: {
        content: [
          { required: true, message: "动态内容不能为空", trigger: "blur" },
        ],
        date: [
          { required: true, message: "动态发布时间不能为空", trigger: "blur" },
        ],
        title: [{ required: true, message: "标题不能为空", trigger: "blur" }],
        taskNodeId: [
          { required: true, message: "标签不能为空", trigger: "blur" },
        ],
        authorId: [
          { required: true, message: "用户不能为空", trigger: "blur" },
        ],
      },
      postTypeOptions: [
        { label: "会所", value: "CLUB" },
        { label: "客户", value: "USER" },
        { label: "员工", value: "STAFF" },
      ],
      // 动态导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: {
          Clientid: process.env.VUE_APP_CILENT_ID,
          Authorization: "Bearer " + getToken(),
        },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/platform/feed_post/import",
      },
      wechatUserOptions: [],
      lableOptions: [],
      customerOptions: [],
      nodeOptions: [],
      create_visible: false,
      roomList: [],
      props: {
        lazy: true,
        lazyLoad: this.lazyLoad,
        checkStrictly: true,
      },
      feedPostDetail: {},
      title: false,
      club_visible: false,
      club_form: {
        content: null,
        date: null,
        authorId: null,
        contentPhotos: null,
        videos: null,
      },
      club_title: "",
      fullscreenLoading: false,
      dialogLoading: false,
    };
  },
  created() {
    this.getList();
    this.getLists();
    queryWechatUserOptions().then((response) => {
      this.wechatUserOptions = response.data;
    });
    let data = {
      roleCode: this.form.roleCode,
    };
    listByType(data).then((response) => {
      this.lableOptions = response.data;
    });

    getRoomUserList(0).then((res) => {
      var result;
      result = res.data;
      result.forEach((item) => {
        item.value = item.userId;
        var role = "(暂未设置角色)";
        if (item.roleName) {
          role = `(${item.roleName})`;
        }
        item.label = item.name + role;
      });
      // resolve(result);
      console.log("ltftest:" + result);
      this.customerOptions = result;
    });
  },
  methods: {
    handleChange(e) {
      console.log(e);
    },
    getTeacherId(val) {
      this.$nextTick(() => {
        this.$forceUpdate();
        this.form.authorId = val;
      });
    },
    getTeacherId1(val) {
      this.form.taskNodeId = val;
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },
    stencil(index, row) {
      this.stencilIndex = index;
      this.form.content = row.content;
      this.$refs.popoverRef.doClose();
    },
    updateStatus(row) {
      this.getupdate(row.status, row.id);
    },
    choiceStyle() {
      this.dialogTableVisible = !this.dialogTableVisible;
    },
    getLists() {
      //列表
      page().then((res) => {
        this.listData = res.rows;
      });
    },
    getupdate(status, id) {
      //修改状态
      this.loading = true;
      let data = {
        status: !status,
        id: id,
      };
      update(data).then((res) => {
        if (res.code == 200) {
          this.$message(res.msg);
          this.getLists();
          this.loading = false;
          return;
        } else {
          this.$message(res.msg);
        }
      });
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      queryPage(this.queryParams).then((response) => {
        this.feedPostList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        content: null,
        date: null,
        title: null,
        contentPhotos: null,
        videos: null,
      };
      this.resetForm("form");
    },
    resetClub() {
      this.club_form = {
        content: null,
        date: null,
        contentPhotos: null,
        videos: null,
        authorId: null,
      };
      this.resetForm("club_form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      let type = row.type;
      this.fullscreenLoading = true;
      if (type === "CLUB") {
        this.resetClub();
        getDetail(row.postId).then((res) => {
          const data = res.data;
          this.club_form.authorId = data.userId;
          this.club_form.content = data.content;
          this.club_form.contentPhotos = data.contentPhotos;
          this.club_form.date = data.createTime;
          this.club_form.videos = data.videos;
          this.club_form.postId = data.postId;
          this.club_visible = true;
          this.club_title = "修改会所动态";
          this.fullscreenLoading = false;
        });
      } else {
        this.reset();
        getDetail(row.postId).then((res) => {
          let detail = res.data;
          this.form.postId = detail.postId;
          this.form.content = detail.content;
          this.form.contentPhotos = detail.contentPhotos;
          this.form.videos = detail.videos;
          this.form.date = detail.createTime;
          this.form.title = detail.title;
          this.form.taskNodeId = res.data.taskNodeId;
          this.form.authorId = res.data.userId;
          this.create_visible = true;
          this.title_disabled = true;
          this.title = "修改动态";
          this.fullscreenLoading = false;
          this.getRoomList();
          console.log(this.form.taskNodeId, this.form.authorId);
        });
      }
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.stepId != null) {
            updateFeedPost(this.form).then((res) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    submitClubForm: function () {
      this.$refs["club_form"].validate((valid) => {
        if (valid) {
          this.dialogLoading = true;
          if (this.club_form.postId != null) {
            if (!this.club_form.videos) {
              this.club_form.videos = [];
            }
            updateFeedPost(this.club_form).then((res) => {
              this.$modal.msgSuccess("修改成功");
              this.club_visible = false;
              this.dialogLoading = false;
              this.getList();
            });
          } else {
            if (!this.club_form.videos) {
              this.club_form.videos = [];
            }
            createClubPost(this.club_form).then((res) => {
              this.$modal.msgSuccess("新增成功");
              this.club_visible = false;
              this.dialogLoading = false;
              this.getList();
            });
          }
        }
      });
    },
    /**
     * 精选动态
     * @param {动态信息} row
     */
    handleFeaturedChange(row) {
      featuredFeedPost(row.postId, row.isFeatured)
        .then((response) => {
          this.$modal.msgSuccess("操作成功");
        })
        .catch(() => {
          row.isFeatured = row.isFeatured === true ? false : true;
        });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "朋友圈动态导入";
      this.upload.open = true;
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download(
        "system/user/importTemplate",
        {},
        `user_template_${new Date().getTime()}.xlsx`
      );
    },
    // 删除动态
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm("确定删除吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          this.loading = true;
          return removeFeedPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.multiple = !selection.length;
    },
    // staffChange(value) {
    //   //选择角色
    //   this.form.customerId = undefined;
    //   this.form.roomId = undefined;
    //   this.form.taskNodeId = undefined;
    //   this.getQueryStaffName();
    //   this.listByType();
    // },
    roomSelect(e) {
      this.form.roomNumber = e.roomNumber;
      this.form.roomId = e.roomId;
    },
    // nameSelect(e) {
    //   //选择人员
    //   this.form.customerId = e.tenantId;
    //   this.form.staffUserId = e.userId;
    //   this.form.nickname = e.nickname;
    //   this.form.roomId = undefined;
    //   this.roomList = [];
    //   this.getQueryRooms();
    // },
    tagSelect(e) {
      this.listByLabel();
    },
    /** 通过角色获取人员名称 */
    getQueryStaffName() {
      this.loading = true;
      getQueryStaffName(this.form.roleCode).then((response) => {
        // this.customerOptions = response.data;
        this.loading = false;
      });
    },
    /** 通过人员获取方房间 */
    getQueryRooms() {
      this.loading = true;
      getQueryRooms(this.form.staffUserId).then((response) => {
        this.roomList = response.data;
        this.loading = false;
      });
    },
    /** 通过类型与角色查询标签列表 */
    listByType() {
      this.loading = true;
      let data = {
        roleCode: this.form.roleCode,
      };
      listByType(data).then((response) => {
        this.tabList = response.data;
        this.loading = false;
      });
    },
    /** 通过标签名称查询模板 */
    listByLabel() {
      this.loading = true;
      listByLabel(this.form.taskNodeId).then((response) => {
        this.stencilList = response.data;
        this.loading = false;
      });
    },
    customerCancel() {
      this.reset();
    },
    customerSubmitForm: function () {
      let from = this.form;
      if (from.title == "" || from.title == null) {
        this.$modal.msgSuccess("标题不能为空");
        return;
      }
      if (from.date == "" || from.date == null) {
        this.$modal.msgSuccess("动态发布时间不能为空");
        return;
      }
      if (from.authorId == "" || from.authorId == null) {
        this.$modal.msgSuccess("用户不能为空");
        return;
      }
      if (from.taskNodeId == "" || from.taskNodeId == null) {
        this.$modal.msgSuccess("标签不能为空");
        return;
      }
      if (from.content == "" || from.content == null) {
        this.$modal.msgSuccess("动态内容不能为空");
        return;
      }
      this.dialogLoading = true;
      if (this.form.postId != null) {
        updateFeedPost(this.form).then((res) => {
          this.$modal.msgSuccess("修改成功");
          this.create_visible = false;
          this.dialogLoading = false;
          this.getList();
        });
      } else {
        create(this.form).then((res) => {
          this.$modal.msgSuccess("新增成功");
          this.create_visible = false;
          this.dialogLoading = false;
          this.getList();
        });
      }

      // this.$nextTick(() => {
      //   this.$refs.form.validate((valid) => {
      //     console.log(valid);
      //     if (valid) {
      //       this.dialogLoading = true;
      //       if (this.form.postId != null) {
      //         updateFeedPost(this.form).then((res) => {
      //           this.$modal.msgSuccess("修改成功");
      //           this.create_visible = false;
      //           this.dialogLoading = false;
      //           this.getList();
      //         });
      //       } else {
      //         create(this.form).then((res) => {
      //           this.$modal.msgSuccess("新增成功");
      //           this.create_visible = false;
      //           this.dialogLoading = false;
      //           this.getList();
      //         });
      //       }
      //     }
      //   });
      // });
    },
    handleAdd() {
      this.reset();
      this.create_visible = true;
      this.title_disabled = false;
      this.getRoomList();
    },
    handlerCreateClean() {
      this.create_visible = false;
      this.title_disabled = false;
    },
    getRoomList() {
      getRoomList().then((res) => {
        this.roomList = res.data;
      });
    },
    lazyLoad(node, resolve) {
      this.getData(node, resolve);
    },
    getData(node, resolve) {
      const level = node.level;
      // getRoomUserList().then(res => {
      //     var result;
      //     result = res.data;
      //     result.forEach((item) => {
      //       item.value = item.userId;
      //       var role = "(暂未设置角色)";
      //       if (item.roleName) {
      //         role = `(${item.roleName})`;
      //       }
      //       item.label = item.name + role;
      //     });
      //     resolve(result);
      //   });
      if (level === 0) {
        // 房间数据
        getRoomList().then((res) => {
          var result = res.data;
          result.forEach((item) => {
            item.value = item.roomId;
            item.label = item.roomNumber;
          });
          resolve(result);
        });
      }

      if (level === 2) {
        var roleCode = "";
        roleCode = node.data.roleCode;
        const query = {
          roleCode: roleCode,
        };
        getNodeOptions(query).then((res) => {
          var result;
          result = res.data;
          result.forEach((item) => {
            item.value = item.value;
            item.label = item.label;
            item.leaf = level >= 2;
          });
          resolve(result);
        });
      }
      if (level === 3) {
        resolve(null);
      }
    },
    cascaderChange(val) {
      console.log(val);
      this.form.taskNodeId = val;
      // console.log(val.length);
      // if (val.length != 3) {
      //   return;
      // }
      // const nodeId = val[2];
      // getTemplateByNodeId(nodeId).then((res) => {
      //   this.stencilList = res.data;
      // });
      // this.$refs.CascaderRef.dropDownVisible = false;
    },
    getDetail(postId) {
      getDetail(postId).then((res) => {
        this.feedPostDetail = res.data;
      });
    },
    handleClubAdd() {
      this.resetClub();
      this.club_visible = true;
      this.club_title = "发布会所动态";
    },
    clubCancel() {
      this.club_visible = false;
    },
    dropDownVisible() {},
  },
};
</script>
<style scoped lang="scss">
.stencil {
  background: #f0f1f5;
  border-radius: 10px;
  padding: 12px 18px;
  color: #000000;
  font-size: 12rpx;
}

.activeStencil {
  background: #1890ff;
  border-radius: 10px;
  padding: 12px 18px;
  color: #fff;
  font-size: 12rpx;
}
</style>
