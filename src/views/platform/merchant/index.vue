<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="商家名称" prop="keyword">
        <el-input v-model="queryParams.keyword" placeholder="请输入商家名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 240px">
          <el-option v-for="auditStatus in auditStatusList" :key="auditStatus.val" :label="auditStatus.name"
            :value="auditStatus.val"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['platform:suites:hot:select']">选择房型</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleCancelRecommended" v-hasPermi="['platform:suites:hot:cancel']">取消推荐</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="merchantApplyList" @selection-change="handleSuitesSelectionChange">
      <!-- <el-table-column type="selection" width="50" align="center" /> -->
      <el-table-column label="商家名称" align="center" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.merchantName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系人姓名" align="center" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.contactName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.contactPhone }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系邮箱" align="center" prop="contactEmail" :show-overflow-tooltip="true" />
      <el-table-column label="地址" align="center" prop="address" :show-overflow-tooltip="true" />
      <el-table-column label="申请日期" align="center" prop="applicationDate" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag type="warning" v-if="scope.row.status === 1">已通过</el-tag>
          <el-tag type="success" v-if="scope.row.status === 0">待审核</el-tag>
          <el-tag type="danger" v-if="scope.row.status === 2">已拒绝</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-dropdown trigger="click" v-if="scope.row.status === 0">
            <el-button class="el-dropdown-link" size="mini" type="text" icon="el-icon-s-tools">
              审核<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item icon="el-icon-success" @click.native="handlePass(scope.row)"
                v-loading.fullscreen.lock="fullscreenLoading"
                v-hasPermi="['system:wechat:mini:program:qrCode']">通过</el-dropdown-item>
              <el-dropdown-item icon="el-icon-error" @click.native="handleRejection(scope.row)"
                v-loading.fullscreen.lock="fullscreenLoading"
                v-hasPermi="['system:wechat:mini:program:like']">拒绝</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
      
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { page, audit, detail} from "@/api/platform/merchant";
export default {
  name: "RecommendedSuites",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 选中数组
      ids: [],
      multiple: true,
      // 总条数
      total: 0,
      // 表格数据
      merchantApplyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        status: undefined
      },
      fullscreenLoading: false,
      auditStatusList: [
        {name: "待审核", val: 0},
        {name: "已通过", val: 1},
        {name: "已拒绝", val: 2},
      ]

    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then(response => {
        this.merchantApplyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSuitesSelectionChange(selection) {
      this.ids = selection.map(item => item.suiteId);
      this.multiple = !selection.length;
    },
    handlePass(row) {
      const data = {
        id: row.id,
        status: 1
      }
      this.handleAudit(data);
    },
    handleRejection(row) {
      const data = {
        id: row.id,
        status: 2
      }
      this.handleAudit(data);
    },
    handleAudit(data){
      this.fullscreenLoading = true;
      audit(data).then(res=>{
        this.$modal.msgSuccess("操作成功");
        this.getList();
      }).finally(()=>{
        this.fullscreenLoading = false;
      });
    }
  }
};
</script>
