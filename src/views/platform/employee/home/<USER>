<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="关键字" prop="keyword">
                <el-input v-model="queryParams.keyword" placeholder="姓名、手机号" clearable style="width: 240px"
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="类型" prop="employeeType">
                <el-select v-model="queryParams.employeeType" placeholder="请选择职位类型" clearable style="width: 240px">
                    <el-option v-for="type in employeeTypeList" :key="type.val" :label="type.name" :value="type.val" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                    v-hasPermi="['platform:employee:create']">新增</el-button>
            </el-col>

            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="employeeList" @selection-change="handleSuitesSelectionChange">
            <!-- <el-table-column type="selection" width="50" align="center" /> -->
            <el-table-column label="姓名" align="center" width="200">
                <template slot-scope="scope">
                    <span>{{ scope.row.name }}</span>
                </template>
            </el-table-column>
            <el-table-column label="手机" align="center" width="200">
                <template slot-scope="scope">
                    <span>{{ scope.row.phone }}</span>
                </template>
            </el-table-column>
            <el-table-column label="照片" align="center" width="150">
                <template slot-scope="scope">
                    <image-preview :src="scope.row.photos"></image-preview>
                </template>
            </el-table-column>
            <el-table-column label="角色" align="center" >
                <template slot-scope="scope">
                    <el-tag type="warning" v-if="scope.row.roleName">{{ scope.row.roleName }}</el-tag>
                    <span v-else>-</span>
                </template>
            </el-table-column>
            <!-- <el-table-column label="类型" align="center" prop="postType">
                <template slot-scope="scope">
                    <el-tag type="warning" v-if="scope.row.postType === 'NURSE'">护理</el-tag>
                    <el-tag type="success" v-if="scope.row.postType === 'SALE'">销售</el-tag>
                    <el-tag type="success" v-if="scope.row.postType === 'OTHER'">其他</el-tag>
                </template>
            </el-table-column> -->

            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
                <template slot-scope="scope">
                    <el-dropdown trigger="click">
                        <span class="el-dropdown-link">
                            操作<i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <!-- <el-dropdown-item icon="el-icon-info"></el-dropdown-item> -->
                            <el-dropdown-item icon="el-icon-info" v-hasPermi="['platform:employee:detail']"
                                @click.native="handleDetail(scope.row)">详情</el-dropdown-item>
                            <el-dropdown-item icon="el-icon-edit-outline"
                                v-hasPermi="['platform:employee:update']" @click.native="handleUpdate(scope.row)">修改</el-dropdown-item>
                            <el-dropdown-item icon="el-icon-refresh" @click.native="handleResetPwd(scope.row)"
                                v-hasPermi="['platform:employee:reset:password']">重置登录密码</el-dropdown-item>
                            <el-dropdown-item icon="el-icon-error" @click.native="handleDisable(scope.row, 1)"
                                v-if="!scope.row.disable"
                                v-hasPermi="['platform:employee:disable']">禁用</el-dropdown-item>
                            <el-dropdown-item icon="el-icon-check" @click.native="handleDisable(scope.row, 0)"
                                v-if="scope.row.disable"
                                v-hasPermi="['platform:employee:disable']">解除禁用</el-dropdown-item>
                            <el-dropdown-item icon="el-icon-delete-solid" v-hasPermi="['platform:employee:remove']"
                                @click.native="handleRemove(scope.row)">删除</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 新增员工 -->
        <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
            <div style="height: 600px;overflow: auto;">
                <el-form ref="form" :model="form" :rules="rules" label-width="80px">

                    <el-form-item label="姓名" prop="name">
                        <el-input v-model="form.name" type="text" placeholder="请输入姓名"></el-input>
                    </el-form-item>
                    <el-form-item label="手机号" prop="phone">
                        <el-input v-model="form.phone" type="text" placeholder="请输入手机号"></el-input>
                    </el-form-item>
                    <el-form-item label="照片" prop="photos">
                        <image-upload v-model="form.photos" :limit="1" :isShowTip="false" />
                    </el-form-item>

                    <!-- <el-form-item label="员工类型" prop="employeeType">
                        <el-select v-model="form.employeeType" placeholder="请选择员工类型" clearable style="width: 100%;">
                            <el-option v-for="type in employeeTypeList" :key="type.val" :label="type.name"
                                :value="type.val" />
                        </el-select>
                    </el-form-item> -->
                    <!-- <el-form-item label="职位" prop="postId">
                        <el-select v-model="form.postId" placeholder="请选择员工职位" clearable style="width: 100%;">
                            <el-option v-for="post in employeePostList" :key="post.value" :label="post.label"
                                :value="post.value" />
                        </el-select>
                    </el-form-item> -->
                    <el-form-item label="角色" prop="roleId">
                        <el-select v-model="form.roleId" placeholder="请选择员工类型" clearable style="width: 100%;">
                            <el-option v-for="role in employeeRoleList" :key="role.id" :label="role.name"
                                :value="role.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="简介" prop="description">
                        <el-input v-model="form.description" type="textarea" placeholder="请输入简介"></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="入职日期" prop="date">
                        <el-date-picker v-model="form.hireDate" type="date" value-format="yyyy-MM-dd"
                            placeholder="请选择入职日期" style="width: 100%;">
                        </el-date-picker>
                    </el-form-item> -->
                    <template v-if="form.employeeType === 'NURSE'">
                        <el-form-item label="从业时间" prop="nurse.practiceTime">
                            <el-date-picker v-model="form.nurse.practiceTime" type="date" value-format="yyyy-MM-dd"
                                placeholder="请选择从业日期" style="width: 100%;">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="服务总数" prop="nurse.serviceNum">
                            <el-input-number v-model="form.nurse.serviceNum" @change="handleChange" :min="1"
                                label="请输入服务客户总数" style="width: 100%;">

                            </el-input-number>
                        </el-form-item>
                        <el-form-item label="标签" prop="nurse.tag">
                            <el-select v-model="form.nurse.tag" multiple placeholder="请选择标签" style="width: 100%;">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>

                    </template>
                </el-form>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm()">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { createEmployee, page, getPostList, getRoleList, remove, disable, resetPassword } from "@/api/platform/employee";
export default {
    name: "Employee",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 显示搜索条件
            showSearch: true,
            // 选中数组
            ids: [],
            multiple: true,
            // 总条数
            total: 0,
            // 表格数据
            employeeList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            form: {
                nurse: {},
                sales: {}
            },
            rules: {
                name: [
                    { required: true, message: "员工姓名不能为空", trigger: "blur" }
                ],
                phone: [
                    { required: true, message: "员工手机号不能为空", trigger: "blur" }
                ],
                postId: [
                    { required: true, message: "员工职位不能为空", trigger: "blur" }
                ],
                roleId: [
                    { required: true, message: "员工角色不能为空", trigger: "blur" }
                ],
                "nurse.practiceTime": [
                    { required: true, message: "从业时间不能为空", trigger: "blur" }
                ],
                "nurse.serviceNum": [
                    { required: true, message: "服务总数不能为空", trigger: "blur" }
                ],
                "nurse.tag": [
                    { required: true, message: "护理人员标签不能为空", trigger: "blur" }
                ]
            },
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                id: undefined,
                status: undefined
            },
            fullscreenLoading: false,
            auditStatusList: [
                { name: "待审核", val: 0 },
                { name: "已通过", val: 1 },
                { name: "已拒绝", val: 2 },
            ],
            employeeTypeList: [
                { name: '护理', val: 'NURSE' },
                { name: '销售', val: 'SALE' },
                { name: '其他', val: 'OTHER' },
            ],
            employeePostList: [],
            employeeRoleList: []

        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询参数列表 */
        getList() {
            this.loading = true;
            page(this.queryParams).then(response => {
                this.employeeList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        getPostOptions() {
            getPostList().then(res => {
                this.employeePostList = res.data;
            });
        },
        getRoleOptions() {
            getRoleList().then(res => {
                this.employeeRoleList = res.data;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        handleSuitesSelectionChange(selection) {
            this.ids = selection.map(item => item.suiteId);
            this.multiple = !selection.length;
        },
        handleDetail(row) {
            this.$router.push({ path: "/detail" , query: {'id': row.employeeId }});
        },
        handleRejection(row) {
        },
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "新增员工";
            // this.getPostOptions();
            this.getRoleOptions();
        },
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.employeeId != null) {
                        // createEmployee(this.form).then(response => {
                        //     this.$modal.msgSuccess("修改成功");
                        //     this.customer_open = false;
                        //     this.getList();
                        // });
                    } else {
                        if(this.form.photos && this.form.photos.length > 0){
                            this.form.photos = this.form.photos[0];
                        }
                        createEmployee(this.form).then(res => {
                            this.$modal.msgSuccess("新增成功");
                            this.getList();
                        }).finally(() => {
                            this.open = false;
                        });
                    }
                }
            });
        },
        handleRemove(row) {
            const id = row.employeeId;
            this.$confirm('此操作将永久删除该员工, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                remove(id).then(res => {
                    this.$modal.msgSuccess("删除成功");
                    this.getList();
                });
            }).catch(() => {
                console.log("取消");
            });

        },
        handleDisable(row, status) {
            const id = row.employeeId;
            this.$confirm('此操作将永禁用该员工, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                disable(id, status).then(res => {
                    this.getList();
                });
            }).catch(() => {
                console.log("取消");
            });
        },
        handleResetPwd(row) {
            this.$prompt('请输入新密码', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            }).then(({ value }) => {
                const data = {
                    employeeId: row.employeeId,
                    newPassword: value
                };
                resetPassword(data).then(res => {
                    this.$modal.msgSuccess("密码重置成功");
                    this.getList();
                });
            });
        },
        handleUpdate(row){
            this.reset();
            this.open = true;
            this.title = "修改员工";
            this.getRoleOptions();

        }
    }
};
</script>