<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="关键字" prop="keyword">
                <el-input v-model="queryParams.keyword" placeholder="职位名称" clearable style="width: 240px"
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="职位类型" prop="type">
                <el-select v-model="queryParams.type" placeholder="请选择职位类型" clearable style="width: 240px">
                    <el-option v-for="type in employeeTypeList" :key="type.val" :label="type.name" :value="type.val" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                    v-hasPermi="['platform:employee:post:create']">新增</el-button>
            </el-col>

            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="employeePostList" @selection-change="handleSuitesSelectionChange">
            <!-- <el-table-column type="selection" width="50" align="center" /> -->
            <el-table-column label="序号" min-width="50" align="center">
                <template slot-scope="scope">
                    {{ scope.$index + 1}}
                </template>
            </el-table-column>

            <el-table-column label="职位名称" align="center" width="200">
                <template slot-scope="scope">
                    <span>{{ scope.row.name }}</span>
                </template>
            </el-table-column>
            <el-table-column label="职位类型" align="center" prop="type">
                <template slot-scope="scope">
                    <el-tag type="warning" v-if="scope.row.type === 'NURSE'">护理</el-tag>
                    <el-tag type="success" v-if="scope.row.type === 'SALE'">销售</el-tag>
                    <el-tag v-if="scope.row.type === 'OTHER'">其他</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="职位排序" align="center" prop="sort" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                        v-hasPermi="['platform:employee:post:update']">修改</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />

        <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
            <div style="height: 600px;overflow: auto;">
                <el-form ref="form" :model="form" :rules="rules" label-width="80px">

                    <el-form-item label="职位名称" prop="name">
                        <el-input v-model="form.name" type="text" placeholder="请输入职位名称"></el-input>
                    </el-form-item>

                    <el-form-item label="职位类型" prop="type">
                        <el-select v-model="form.type" placeholder="请选择职位类型" clearable style="width: 100%;">
                            <el-option v-for="type in employeeTypeList" :key="type.val" :label="type.name"
                                :value="type.val" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="职位排序" prop="sort">
                        <el-input-number v-model="form.sort" :min="0" :precision="0" label="请输入服职位排序"
                            style="width: 100%;">
                        </el-input-number>
                    </el-form-item>
                </el-form>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm()" v-loading.fullscreen.lock="fullscreenLoading">确定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { create, page, detail, update } from "@/api/platform/employeePost";
export default {
    name: "EmployeePost",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 显示搜索条件
            showSearch: true,
            // 选中数组
            ids: [],
            multiple: true,
            // 总条数
            total: 0,
            // 表格数据
            employeePostList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            form: {
                sort: 0
            },
            rules: {
                name: [
                    { required: true, message: "职位名称不能为空", trigger: "blur" }
                ],
                type: [
                    { required: true, message: "职位类型不能为空", trigger: "blur" }
                ],
                sort: [
                    { required: true, message: "职位排序不能为空", trigger: "blur" }
                ],
            },
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                id: undefined,
                status: undefined
            },
            fullscreenLoading: false,
            employeeTypeList: [
                { name: '护理', val: 'NURSE' },
                { name: '销售', val: 'SALE' },
                { name: '其他', val: 'OTHER' },
            ],
            employeePostList: [],
            employeeRoleList: []

        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询参数列表 */
        getList() {
            this.loading = true;
            page(this.queryParams).then(response => {
                this.employeePostList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        getDetail(id){
            detail(id).then(res=>{
                this.form = res.data;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        handleSuitesSelectionChange(selection) {
            this.ids = selection.map(item => item.suiteId);
            this.multiple = !selection.length;
        },
        handleDetail(row) {
            const data = {
                id: row.id,
                status: 1
            }
            this.handleAudit(data);
        },
        handleRejection(row) {
            const data = {
                id: row.id,
                status: 2
            }
            this.handleAudit(data);
        },
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "新增员工职位";
        },
        handleUpdate(row) {
            const id = row.id;
            this.reset();
            this.open = true;
            this.title = "修改员工职位";
            this.getDetail(id);
        },
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    this.fullscreenLoading = true;
                    if (this.form.id != null) {
                        update(this.form).then(res => {
                            this.$modal.msgSuccess("修改成功");
                            this.getList();
                        }).finally(()=>{
                            this.open = false;
                            this.fullscreenLoading = false;
                        });
                    } else {
                        create(this.form).then(res => {
                            this.$modal.msgSuccess("新增成功");
                            this.getList();
                        }).finally(() => {
                            this.open = false;
                            this.fullscreenLoading = false;
                        });
                    }
                }
            });
        }
    }
};
</script>