<template>
  <article id='img-upload'>
    <section class="img-upload-content" :style="uploadStyle">
      <div class="have-img" v-if="copyPath">
        <el-image class="img" :src="copyPath" fit="contain" />
        <div class="img-slot">
          <label class="upload-label">
            <input type="file" class="hidden-input" @change="handleFileChange" accept="image/*">
            <i class="el-icon-edit-outline"></i>
          </label>
          <i @click="deleteImg" class="el-icon-delete"></i>
          <!-- <i @click="toCropImg" class="el-icon-crop"></i> -->
        </div>
      </div>
      <template v-else>
        <slot name='defCover' v-if="btnUpload"></slot>
        <div class="imgUpload" v-else>
          <label class="upload-label">
            <input type="file" class="hidden-input" @change="handleFileChange" accept="image/*">
            <slot name='imgUpload'></slot>
          </label>
        </div>
      </template>
    </section>
    <slot name='tip'></slot>
    <label class="upload-label" v-if="btnUpload">
      <input type="file" class="hidden-input" @change="handleFileChange" accept="image/*">
      <slot name='btnUpload'></slot>
    </label>
    <img-crop v-bind="$attrs" :crop-visibility="cropVisibility" :fileInfo="fileInfo" :crop-picture="cropPicture"
      @finishCrop="finishCrop" @close="cropVisibility = false" :fixed-number="fixedNumber"/>
  </article>
</template>

<script>
import ImgCrop from '@/components/ImgCrop/index';
import { getToken } from "@/utils/auth";

export default {
  name: 'ImgUpload',
  components: { ImgCrop },
  props: {
    width: String,
    height: String,
    path: String,
    maxSize: {
      type: Number,
      default: 5 * 1024 * 1024
    },
    bgColor: {
      type: String,
      default: 'white'
    },
    border: {
      type: Boolean,
      default: false
    },
    borderWidth: {
      type: String,
      default: '1px'
    },
    borderColor: {
      type: String,
      default: '#d1d1d1'
    },
    borderStyle: {
      type: String,
      default: 'solid'
    },
    borderRadius: {
      type: String,
      default: '5px'
    },
    btnUpload: {
      type: Boolean,
      default: false
    },
    fixedNumber: {
      type: Array,
      default: [1, 1]
    }
  },
  model: {
    prop: 'path',
    event: 'change'
  },
  data() {
    return {
      copyPath: '',
      fileInfo: null,
      cropVisibility: false,
      cropPicture: '',
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/common/uploadOSS",
      headers: {
        Clientid: process.env.VUE_APP_CILENT_ID,
        Authorization: "Bearer " + getToken(),
      },
    };
  },
  computed: {
    uploadStyle() {
      let borderObj = {
        borderWidth: this.borderWidth,
        borderRadius: this.borderRadius,
        borderColor: this.borderColor,
        borderStyle: this.borderStyle
      };
      let styleObj = {
        width: this.width,
        height: this.height,
        backgroundColor: this.bgColor
      };
      if (this.border) {
        Object.assign(styleObj, borderObj);
      }
      return styleObj;
    },
    maxImageSize() {
      let me = this;
      let _M = me.maxSize / 1024 / 1024;
      let _Kb = me.maxSize / 1024 + '';
      if (_M >= 1) {
        let y = String(_M).indexOf('.') + 1;
        if (y > 0) {
          return _M.toFixed(2) + 'M';
        } else {
          return _M + 'M';
        }

      } else {
        return parseInt(_Kb) + 'Kb';
      }
    }
  },
  watch: {
    path: {
      immediate: true,
      handler(val) {
        let me = this;
        me.copyPath = val;
      }
    }
  },
  methods: {
    handleFileChange(event) {
      let me = this;
      const file = event.target.files[0];
      
      if (!file) return;
      
      if (file.size > me.maxSize) {
        me.$message.error('上传失败，图片大于' + this.maxImageSize);
        return;
      }
      
      me.fileInfo = {
        raw: file,
        name: file.name,
        size: file.size
      };
      
      let reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = function (event) {
        me.cropPicture = event.target.result;
        me.cropVisibility = true;
      };
      
      event.target.value = '';
    },
    toCropImg() {
      let index = this.copyPath.lastIndexOf('/');
      let str = this.copyPath.substring(index + 1);
      this.fileInfo = { name: str };
      this.cropPicture = this.copyPath;
      this.cropVisibility = true;
    },
    finishCrop(data) {
      this.copyPath = data;
      this.$emit('change', data);
    },
    deleteImg() {
      this.copyPath = '';
      this.$emit('change', '');
    }
  }
};
</script>

<style scoped lang="scss">
#img-upload {
  .img-upload-content {
    overflow: hidden;

    .have-img {
      width: 100%;
      height: 100%;
      position: relative;

      .img {
        width: 100%;
        height: 100%;
      }

      .img-slot {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-color: #424242;
        z-index: 1;
        background-color: rgba(0, 0, 0, 0.5);
        transition: 0.3s;
        display: flex;
        justify-content: space-evenly;
        align-items: flex-end;
        opacity: 0;

        i {
          cursor: pointer;
          line-height: 40px;
          font-style: normal;
          font-weight: 400;
          font-size: 20px;
          color: white;
        }
      }

      &:hover .img-slot {
        opacity: 1;
      }
    }
  }

  .imgUpload {
    height: 100%;

    ::v-deep .el-upload {
      width: 100%;
      height: 100%;
    }
  }

  .hidden-input {
    display: none;
  }
  
  .upload-label {
    cursor: pointer;
    display: inline-block;
  }
}
</style>