<template>
  <div :style="{ width: width + 'px', height: height + 'px' }" @click="handlePreview" class="video-preview-wrapper">
    <video :src="src" :style="{ width: '100%', height: '100%' }" muted></video>
    <div class="play-mask">
      <i class="el-icon-video-play"></i>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      width="800px"
      :append-to-body="true"
      :destroy-on-close="true"
      @close="handleClose"
      custom-class="video-preview-dialog">
      <video
        v-if="dialogVisible"
        :src="src"
        class="video-player"
        controls
        autoplay>
        您的浏览器不支持视频播放
      </video>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    src: String,
    width: {
      type: Number,
      default: 240
    },
    height: {
      type: Number,
      default: 135
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  methods: {
    handlePreview() {
      this.dialogVisible = true;
    },
    handleClose() {
      this.dialogVisible = false;
    }
  }
}
</script>

<style scoped>
.video-preview-wrapper {
  position: relative;
  cursor: pointer;
}

.play-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  transition: all 0.3s;
}

.el-icon-video-play {
  font-size: 32px;
  color: #fff;
}

video {
  object-fit: cover;
}

.video-player {
  width: 100%;
  max-height: 80vh;
  object-fit: contain;
}
</style> 