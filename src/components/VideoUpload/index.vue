<template>
  <el-upload 
    class="avatar-uploader" 
    :action="uploadImgUrl"
    :headers="headers"
    :data="uploadParams"
    :on-progress="handleProgress"
    :on-success="handleSuccess"
    :before-upload="handleBeforeUpload"
    :show-file-list="false">
    <video 
      v-if="value && !uploading" 
      :src="value"
      class="avatar video-avatar" 
      controls="controls">
      您的浏览器不支持视频播放
    </video>
    <i v-else-if="!value && !uploading" class="el-icon-plus avatar-uploader-icon"></i>
    <el-progress 
      v-if="uploading" 
      type="circle" 
      :percentage="uploadPercent">
    </el-progress>
  </el-upload>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  name: 'VideoUpload',
  
  props: {
    value: {
      type: String,
      default: ''
    },
    maxSize: {
      type: Number,
      default: 50
    }
  },

  data() {
    return {
      uploading: false,
      uploadPercent: 0,
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/common/uploadOSS",
      headers: {
        Clientid: process.env.VUE_APP_CILENT_ID,
        Authorization: "Bearer " + getToken(),
      },
      uploadParams: {
        module: 'video',
        type: 'public'
      },
      // 允许的视频格式
      allowTypes: [
      "video/mp3",
        "video/mp4",
        "video/ogg",
        "video/flv",
        "video/avi",
        "video/wmv",
        "video/mov"
      ]
    }
  },

  methods: {
    handleBeforeUpload(file) {
      const isValidType = this.allowTypes.includes(file.type)
      const isValidSize = file.size / 1024 / 1024 < this.maxSize

      if (!isValidType) {
        this.$message.error('请上传正确的视频格式')
        return false
      }
      if (!isValidSize) {
        this.$message.error(`视频大小不能超过${this.maxSize}MB`)
        return false
      }
      
      this.uploading = true
      return true
    },

    handleProgress(event, file) {
      this.uploadPercent = Math.floor(file.percentage)
    },

    handleSuccess(response) {
      this.uploading = false
      this.uploadPercent = 0
      
      if (response.code === 200) {
        this.$emit('input', response.data.url)
      } else {
        this.$message.error(response.message || '上传失败')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.avatar-uploader-icon {
  border: 1px dashed #d9d9d9 !important;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9 !important;
  border-radius: 6px !important;
  position: relative !important;
  overflow: hidden !important;
}

.avatar-uploader .el-upload:hover {
  border: 1px dashed #d9d9d9 !important;
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 300px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 300px;
  height: 178px;
  display: block;
}
</style>