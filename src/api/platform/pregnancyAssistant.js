import request from '@/utils/request'//孕期助手
export function page(query){
    return request({
      url: '/platform/pregnancy_tracker/page',
      method: 'get',
      params: query
    })
  }
//添加
  export function save(data){
    return request({
      url: '/platform/pregnancy_tracker',
      method: 'post',
      data: data
    })
  }
    // 删除表数据
    export function delTable(tableId) {
        return request({
          url: '/platform/pregnancy_tracker/' + tableId,
          method: 'delete'
        })
      }
  //查询信息
export function info(query){
    return request({
      url: '/platform/pregnancy_tracker/'+query,
      method: 'get',
    })
  }
    //修改
    export function update(data){
        return request({
          url: '/platform/pregnancy_tracker',
          method: 'put',
          data: data
        })
      }