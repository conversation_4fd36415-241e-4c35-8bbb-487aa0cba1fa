import request from '@/utils/request'//月子套房

export function page(query){
  return request({
    url: '/platform/suite/page',
    method: 'get',
    params: query
  })
}

export function save(data){
  return request({
    url: '/platform/suite/save',
    method: 'put',
    data: data
  })
}

// 删除表数据
export function delTable(tableId) {
  return request({
    url: '/platform/suite/' + tableId,
    method: 'delete'
  })
}

//查询月子套房信息
export function info(query){
  return request({
    url: '/platform/suite/info/'+query,
    method: 'get',
  })
}
//月子套房上下架
export function online(query){
  return request({
    url: `/platform/suite/online/${query.suiteId}/${query.status}`,
    method: 'get'
  })
}

//修改月子套房
export function update(data){
  return request({
    url: '/platform/suite/update',
    method: 'post',
    data: data
  })
}
//月子套房展示状态
export function suiteShow(query){
  return request({
    url: `/platform/suite/show/${query.suiteId}/${query.status}`,
    method: 'get'
  })
}

//设置热门推荐房型
export function setRecommended(data){
  return request({
    url: '/platform/suite/recommended',
    method: 'post',
    data: data
  })
}

//取消热门推荐房型
export function cancelRecommended(data){
  return request({
    url: '/platform/suite/cancel_recommended',
    method: 'post',
    data: data
  })
}