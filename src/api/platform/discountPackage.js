import request from '@/utils/request'//优惠套餐配置
//列表
export function page(query){
    return request({
      url: '/platform/package/page',
      method: 'get',
      params: query
    })
  }
//添加
  export function save(data){
    return request({
      url: '/platform/package/save',
      method: 'put',
      data: data
    })
  }
  
  // 删除表数据
  export function delTable(tableId) {
    return request({
      url: '/platform/package/' + tableId,
      method: 'delete'
    })
  }
  
  //查询
  export function info(query){
    return request({
      url: '/platform/package/info/'+query,
      method: 'get',
    })
  }
  //上下架
  export function online(query){
    return request({
      url: `/platform/package/online/${query.packageId}/${query.status}`,
      method: 'post'
    })
  }
  
  //修改
  export function update(data){
    return request({
      url: '/platform/package/update',
      method: 'post',
      data: data
    })
  }

    //改变优惠套餐主页显示状态
    export function homepage(query){
        return request({
          url: `/platform/package/homepage/${query.packageId}/${query.status}`,
          method: 'post'
        })
      }



//房型列表
export function houseList(query){
  return request({
    url: '/platform/suite/page',
    method: 'get',
    params: query
  })
}
//宝宝，妈妈用品列表
export function usePage(query){
  return request({
    url: '/platform/supplies/page',
    method: 'get',
    params: query
  })
}
//宝宝，妈妈护理列表
export function nursePage(query){
  return request({
    url: '/platform/nursing_project/page',
    method: 'get',
    params: query
  })
}