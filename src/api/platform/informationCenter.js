import request from '@/utils/request'//客户管理
//列表
export function page(query){
    return request({
      url: '/platform/customer/page',
      method: 'get',
      params: query
    })
  }
//添加
  export function save(data){
    return request({
      url: '/platform/customer/save',
      method: 'put',
      data: data
    })
  }
  //查询
  export function info(query){
    return request({
      url: '/platform/customer/info/'+query,
      method: 'get',
    })
  }
  //修改
  export function update(data){
    return request({
      url: '/platform/customer/update',
      method: 'post',
      data: data
    })
  }
