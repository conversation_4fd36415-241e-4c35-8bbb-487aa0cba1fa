import request from '@/utils/request'

export function page(query) {
    return request({
        url: '/platform/default_node/page',
        method: 'get',
        params: query
    })
}

//添加
export function add(data) {
    return request({
        url: '/platform/default_node/add',
        method: 'post',
        data: data
    })
}

// 详情
export function detail(id){
    return request({
        url: '/platform/default_node/detail/' + id,
        method: 'get'
    });
}

//修改节点
export function update(data) {
    return request({
        url: '/platform/default_node/update',
        method: 'put',
        data: data
    })
}

//删除节点
export function remove_node(id) {
    return request({
        url: '/platform/default_node/' + id,
        method: 'delete'
    })
}

// 客户列表
export function customer_page(query) {
    return request({
        url: '/platform/default_customer/page',
        method: 'get',
        params: query
    })
}

//添加客户
export function customer_add(data) {
    return request({
        url: '/platform/default_customer/add',
        method: 'post',
        data: data
    })
}

// 客户详情
export function customer_detail(id){
    return request({
        url: '/platform/default_customer/detail/' + id,
        method: 'get'
    });
}

//修改客户
export function customer_update(data) {
    return request({
        url: '/platform/default_customer/update',
        method: 'put',
        data: data
    })
}

//删除客户
export function customer_remove(id) {
    return request({
        url: '/platform/default_customer/' + id,
        method: 'delete'
    })
}

// 员工列表
export function staff_page(query) {
    return request({
        url: '/platform/default_staff/page',
        method: 'get',
        params: query
    })
}

// 获取员工客户列表
export function get_staff_customer_list(id){
    return request({
        url: '/platform/default_staff/customers_staff_list/'+id,
        method: 'get',
    }) 
}

//添加员工
export function staff_add(data) {
    return request({
        url: '/platform/default_staff/add',
        method: 'post',
        data: data
    })
}

// 员工详情
export function staff_detail(id){
    return request({
        url: '/platform/default_staff/detail/' + id,
        method: 'get'
    });
}

//修改员工
export function staff_update(data) {
    return request({
        url: '/platform/default_staff/update',
        method: 'put',
        data: data
    })
}

//删除员工
export function staff_remove(id) {
    return request({
        url: '/platform/default_staff/' + id,
        method: 'delete'
    })
}

//添加动态
export function feedpost_add(data) {
    return request({
        url: '/platform/default_feed_post/add',
        method: 'post',
        data: data
    })
}

// 获取动态列表
export function get_feedpost_page(params){
    return request({
        url: '/platform/default_feed_post/page',
        method: 'get',
        params: params
    }) 
}

// 获取动态详情
export function feddpost_detail(id){
    return request({
        url: '/platform/default_feed_post/detail/' + id,
        method: 'get'
    });
}

// 获取动态详情
export function feddpost_update(data){
    return request({
        url: '/platform/default_feed_post/update',
        method: 'put',
        data: data
    });
}

//删除动态
export function feedpost_remove(id) {
    return request({
        url: '/platform/default_feed_post/' + id,
        method: 'delete'
    })
}