import request from '@/utils/request'//签到
export function page(query){
    return request({
      url: '/platform/checkin_gifts/page',
      method: 'get',
      params: query
    })
  }
//添加
  export function save(data){
    return request({
      url: '/platform/checkin_gifts/save',
      method: 'put',
      data: data
    })
  }
    // 删除表数据
    export function delTable(tableId) {
        return request({
          url: '/platform/checkin_gifts/' + tableId,
          method: 'delete'
        })
      }
  //查询信息
export function info(query){
    return request({
      url: '/platform/checkin_gifts/info/'+query,
      method: 'get',
    })
  }
    //修改
    export function update(data){
        return request({
          url: '/platform/checkin_gifts/update',
          method: 'post',
          data: data
        })
      }
      //上下架
  export function online(query){
    return request({
      url: `/platform/checkin_gifts/change/${query.status}/${query.giftId}`,
      method: 'post'
    })
  }