import request from '@/utils/request'


// 新增员工
export function createEmployee(data) {
    return request({
        url: '/platform/employee/save',
        method: 'post',
        data: data
    })
}

// 修改员工
export function updateEmployee(data) {
    return request({
        url: '/platform/employee/update',
        method: 'post',
        data: data
    })
}

// 查询员工列表
export function page(params) {
    return request({
        url: `/platform/employee/page`,
        method: 'get',
        params: params
    })
}

// 查询员工列表
export function list(params) {
    return request({
        url: `/platform/employee/list`,
        method: 'get',
        params: params
    })
}

// 查询员工岗位列表
export function getPostList(params) {
    return request({
        url: `/platform/employee_post/options`,
        method: 'get',
        params: params
    })
}

// 查询角色列表
export function getRoleList(params) {
    return request({
        url: `/platform/employee/role_list`,
        method: 'get',
        params: params
    })
}

// 删除员工
export function remove(id) {
    return request({
        url: `/platform/employee/remove/${id}`,
        method: 'delete'
    })
}

// 禁用员工
export function disable(id, disable) {
    return request({
        url: `/platform/employee/disable/${id}/${disable}`,
        method: 'post'
    })
}

// 重置密码
export function resetPassword(data) {
    return request({
        url: `/platform/employee/reset_password`,
        method: 'post',
        data: data
    })
}

// 绑定用户
export function bindEmployee(data){
    return request({
        url: `/platform/staff/bind_employee`,
        method: 'post',
        data: data
    });
}

// 查询员工详情
export function detail(id) {
    return request({
        url: `/platform/employee/detail/${id}`,
        method: 'get'
    })
}