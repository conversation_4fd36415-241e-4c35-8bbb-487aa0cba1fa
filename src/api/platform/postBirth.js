import request from '@/utils/request'//产后康复配置
//列表
export function page(query){
  return request({
    url: '/platform/postpartum_recovery/page',
    method: 'get',
    params: query
  })
}
//新增
export function save(data){
  return request({
    url: '/platform/postpartum_recovery/save ',
    method: 'put',
    data: data
  })
}

// 删除表数据
export function delTable(tableId) {
  return request({
    url: '/platform/postpartum_recovery/' + tableId,
    method: 'delete'
  })
}

//产后康复项目上架&下架
export function online(query){
  console.log(query);
  return request({
    url: `/platform/postpartum_recovery/onShelf/${query.id}/${query.status}`,
    method: 'post'
  })
}
//查询产后康复项目信息
export function info(query){
    return request({
      url: '/platform/postpartum_recovery/info/'+query,
      method: 'get',
    })
  }
  //修改产后康复
export function update(data){
  return request({
    url: '/platform/postpartum_recovery/update',
    method: 'post',
    data: data
  })
}
//改变产后康复项目主页显示状态
export function homepage(data) {
  return request({
    url: `/platform/postpartum_recovery/homepage/${data.projectId}/${data.status}`,
    method: 'post',
  })
}
