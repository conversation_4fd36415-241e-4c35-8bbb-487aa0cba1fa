import request from '@/utils/request'//护理人员
export function page(query) {
  return request({
    url: '/platform/staff/page',
    method: 'get',
    params: query
  })
}
//添加
export function save(data) {
  return request({
    url: '/platform/staff/save',
    method: 'put',
    data: data
  })
}
// 删除表数据
export function delTable(tableId) {
  return request({
    url: '/platform/staff/' + tableId,
    method: 'delete'
  })
}
//查询信息
export function info(query) {
  return request({
    url: '/platform/staff/info/' + query,
    method: 'get',
  })
}
//修改
export function update(data) {
  return request({
    url: '/platform/staff/update',
    method: 'post',
    data: data
  })
}

//改变护理人员主页显示状态
export function homepage(data) {
  return request({
    url: `/platform/staff/homepage/${data.staffId}/${data.status}`,
    method: 'post',
  })
}

  //查询护理人员列表
  export function queryStaffList(){
    return request({
      url: '/platform/staff/list',
      method: 'get',
    })
  }
