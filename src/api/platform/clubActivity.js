import request from '@/utils/request'

export function page(query){
  return request({
    url: '/platform/club_activity/page',
    method: 'get',
    params: query
  })
}

export function save(data){
  return request({
    url: '/platform/club_activity/save',
    method: 'post',
    data: data
  })
}

export function detail(activityId){
  return request({
    url: '/platform/club_activity/detail/'+activityId,
    method: 'get',
  })
}

export function remove(activityIds){
  return request({
    url: '/platform/club_activity/remove?activityIds='+activityIds,
    method: 'delete',
  })
}

export function update(data){
  return request({
    url: '/platform/club_activity/update',
    method: 'post',
    data: data
  })
}