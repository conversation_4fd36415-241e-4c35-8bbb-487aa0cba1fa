import request from '@/utils/request'//用品管理
//列表
export function page(query){
  return request({
    url: '/platform/supplies/page',
    method: 'get',
    params: query
  })
}
//新增妈妈
export function save1(data){
  return request({
    url: '/platform/supplies/save_mama',
    method: 'put',
    data: data
  })
}
//新增宝宝
export function save2(data){
    return request({
      url: '/platform/supplies/save_baby',
      method: 'put',
      data: data
    })
  }

// 删除表数据
export function delTable(tableId) {
  return request({
    url: '/platform/supplies/' + tableId,
    method: 'delete'
  })
}