import request from '@/utils/request'//配送  移动月嫂
export function page(query){
    return request({
      url: '/platform/mobile_nanny/page',
      method: 'get',
      params: query
    })
  }
//添加
  export function save(data){
    return request({
      url: '/platform/mobile_nanny/save',
      method: 'put',
      data: data
    })
  }
    // 删除表数据
    export function delTable(tableId) {
        return request({
          url: '/platform/mobile_nanny/' + tableId,
          method: 'delete'
        })
      }
  //查询信息
export function info(query){
    return request({
      url: '/platform/mobile_nanny/info/'+query,
      method: 'get',
    })
  }
    //修改
    export function update(data){
        return request({
          url: '/platform/mobile_nanny/update',
          method: 'post',
          data: data
        })
      }