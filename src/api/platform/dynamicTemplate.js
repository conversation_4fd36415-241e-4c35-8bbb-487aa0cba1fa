import request from '@/utils/request'//动态模板
//分页模板节点列表
export function page(query){
    return request({
      url: '/platform/templateDynamic/page',
      method: 'get',
      params: query
    })
  }
//添加
  export function save(data){
    return request({
      url: '/platform/templateDynamic/save',
      method: 'post',
      data: data
    })
  }

  // 删除表数据
  export function delTable(tableId) {
    return request({
      url: '/platform/templateDynamic/remove?id=' + tableId,
      method: 'get'
    })
  }

  //查询
  export function info(query){
    return request({
      url: '/platform/templateDynamic/info/'+query,
      method: 'get',
    })
  }
  //查询
  export function listByType(data){
    return request({
      url: '/platform/task_node/listByType',
      method: 'post',
      data: data
    })
  }
  //获取所有模板风格
export function templateStyle(query){
  return request({
    url: '/platform/template_style/list',
    method: 'get',
    params: query
  })
}
  //新增模板风格
  export function templateStyleAdd(data){
    return request({
      url: '/platform/template_style/save',
      method: 'put',
      data: data
    })
  }
