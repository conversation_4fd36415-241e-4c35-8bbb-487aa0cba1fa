import request from '@/utils/request'
//分页查询样式列表
export function page(query){
  return request({
    url: '/platform/styleDynamic/page',
    method: 'get',
    params: query
  })
}
// 创建员工朋友圈动态
export function update(data) {
  return request({
    url: '/platform/styleDynamic/save',
    method: 'post',
    data: data
  })
}
// 分页查询朋友圈动态列表
export function queryPage(query) {
  return request({
    url: '/platform/feed_post/page',
    method: 'get',
    params: query
  })
}

// 查询朋友圈动态详情
export function queryInfo(postId) {
  return request({
    url: `/platform/feed_post/${postId}`,
    method: 'get'
  })
}

// 查询朋友圈动态详情
export function getDetail(postId) {
  return request({
    url: `/platform/feed_post/detail/${postId}`,
    method: 'get'
  })
}

// 查询员工朋友圈动态详情
export function queryStaffFeedPostInfo(postId) {
  return request({
    url: `/platform/feed_post/info_staff/${postId}`,
    method: 'get'
  })
}

// 查询客户或者会所朋友圈动态详情
export function getInfoByCustomerOrClub(postId) {
  return request({
    url: `/platform/feed_post/info_customer_or_club/${postId}`,
    method: 'get'
  })
}


// 创建员工朋友圈动态
export function createClubPost(data) {
  return request({
    url: '/platform/feed_post/create_club',
    method: 'post',
    data: data
  })
}

// 修改动态
export function updateFeedPost(data) {
  return request({
    url: '/platform/feed_post/update',
    method: 'post',
    data: data
  })
}

// 修改员工朋友圈动态
export function updateStaffFeedPost(data) {
  return request({
    url: '/platform/feed_post/update_staff_feed_post',
    method: 'put',
    data: data
  })
}

// 更改动态精选状态
export function featuredFeedPost(postId, featuredStatus) {
  return request({
    url: `/platform/feed_post/featured/${postId}/${featuredStatus}`,
    method: 'post'
  })
}

// 删除朋友圈动态
export function removeFeedPost(postId) {
  return request({
    url: `/platform/feed_post/${postId}`,
    method: 'delete'
  })
}

// 查询员工已经服务的客户下拉列表
export function getStaffCustomerOptions(staffUserId) {
  return request({
    url: `/platform/room/customer_options?staffUserId=${staffUserId}`,
    method: 'get'
  })
}


// 创建客户或者会所朋友圈动态
export function create(data) {
  return request({
    url: '/platform/feed_post/create',
    method: 'post',
    data: data
  })
}


// 修改客户或会所朋友圈动态
export function updateCustomerOrClubFeedPost(data) {
  return request({
    url: '/platform/feed_post/update_customer_or_club_feed_post',
    method: 'put',
    data: data
  })
}


// 通过角色获取人员名称
export function getQueryStaffName(roleName) {
  return request({
    url: `/platform/feed_post/queryStaffName/${roleName}`,
    method: 'get'
  })
}
// 通过人员获取方房间
export function getQueryRooms(userId) {
  return request({
    url: `/platform/feed_post/queryRooms/${userId}`,
    method: 'get'
  })
}
// 通过类型与角色查询标签列表
export function listByType(data) {
  return request({
    url: '/platform/feed_post/listByType',
    method: 'post',
    data: data
  })
}
// 通过标签名称查询模板
export function listByLabel(name) {
  return request({
    url: `/platform/feed_post/listByLabel/${name}`,
    method: 'get',
  })
}
