import request from '@/utils/request'//查询 访客数、浏览量、人均浏览量、流失率、平均停留时长、每日访问量列表
export function totalVisit(query){
    return request({
      url: '/platform/behaviour_stats/total_visit',
      method: 'get',
      params: query
    })
  }
  export function birthdayList(query){//查询客户生日列表 tips：妈妈宝宝数据混合查询
    return request({
      url: '/platform/customer/birthday_list',
      method: 'get',
      params: query
    })
  }



  export function totalList(query){//查询 总访问用户量、浏览总时间、总访问次数
    return request({
      url: '/platform/behaviour_stats/total',
      method: 'get',
      params: query
    })
  }

  export function analysePage(query){//查询用户分析列表
    return request({
      url: '/platform/behaviour_stats/analyse_page',
      method: 'get',
      params: query
    })
  }

  export function userList(query){//查询用户统计 年龄分布、新老用户、性别比例、总用户数
    return request({
      url: '/platform/behaviour_stats/user',
      method: 'get',
      params: query
    })
  }


  export function detailVisit(query){//查询 用户浏览详情统计
    return request({
      url: '/platform/behaviour_stats/detail_visit',
      method: 'get',
      params: query
    })
  }

  export function statsModule(query){//查询模块统计 访问时长、访问次数、访问人数、分享次数、分享人数
    return request({
      url: '/platform/behaviour_stats/module',
      method: 'get',
      params: query
    })
  }

  export function moduleOne(query){//查询单个模块统计 访问时长、访问次数、访问人数、分享次数、分享人数
    return request({
      url: '/platform/behaviour_stats/module_one',
      method: 'get',
      params: query
    })
  }

  export function moduleOneDay(query){//查询单个模块每天访问次数、访问时长
    return request({
      url: '/platform/behaviour_stats/module_one_day',
      method: 'get',
      params: query
    })
  }

  export function statsDue(query){//查询孕期数据统计
    return request({
      url: '/platform/behaviour_stats/due',
      method: 'get',
      params: query
    })
  }

  export function dueList(query){//查询孕期数据统计列表
    return request({
      url: '/platform/behaviour_stats/due_list',
      method: 'get',
      params: query
    })
  }

  
  export function newStats(query){//查询 查询最近7天新增的评价数量
    return request({
      url: '/platform/review/new_stats',
      method: 'get',
      params: query
    })
  }