import request from '@/utils/request'//标签管理
export function page(query){
    return request({
      url: '/platform/customer_tag/page',
      method: 'get',
      params: query
    })
  }
//添加
  export function save(data){
    return request({
      url: '/platform/customer_tag/save',
      method: 'put',
      data: data
    })
  }
    // 删除
    export function delTable(tableId) {
        return request({
          url: '/platform/customer_tag/' + tableId,
          method: 'delete'
        })
      }
  //查询信息
export function info(query){
    return request({
      url: '/platform/customer_tag/detail/'+query,
      method: 'get',
    })
  }
    //修改
    export function update(data){
        return request({
          url: '/platform/customer_tag/update',
          method: 'post',
          data: data
        })
      }
       //更改详细咨询
    export function updateConsultation(data){
      return request({
        url: '/platform/customer_tag/update_consultation',
        method: 'post',
        data: data
      })
    }
      //查询详细咨询
export function getConsultation(){
  return request({
    url: '/platform/customer_tag/get_consultation',
    method: 'post',
  })
}
