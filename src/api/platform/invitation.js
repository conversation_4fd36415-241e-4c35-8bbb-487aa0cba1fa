import request from '@/utils/request'

export function templateSave(data) {
    return request({
        url: '/platform/invitation/template/save',
        method: 'post',
        data: data
    })
}

export function templateUpdate(data) {
    return request({
        url: '/platform/invitation/template/update',
        method: 'post',
        data: data
    })
}

export function queryTemplatePage(params) {
    return request({
        url: '/platform/invitation/template/page',
        method: 'get',
        params: params
    })
}

export function getTemplateDetail(id) {
    return request({
        url: `/platform/invitation/template/detail/${id}`,
        method: 'get'
    })
}

export function updateTemplateStatus(id, status) {
    return request({
        url: `/platform/invitation/template/${id}/status`,
        method: 'put',
        params: {
            "status": status
        }
    })
}