import request from '@/utils/request'//会所设施配置

  //查询
export function page(query){
    return request({
      url: '/platform/customer_track/page',
      method: 'get',
      params: query
    })
  }
    //分页查询销售员列表
export function userPage(query){
  return request({
    url: '/platform/customer_track/user_page',
    method: 'get',
    params: query
  })
}
    //分页查询跟进记录列表
    export function followedLogs(query){
      return request({
        url: '/platform/customer_track/followed_logs',
        method: 'get',
        params: query
      })
    }
     //分页查询操作记录列表
     export function operationLogs(query){
      return request({
        url: '/platform/customer_track/operation_logs',
        method: 'get',
        params: query
      })
    }
    //添加
    export function save(data){
      return request({
        url: '/platform/customer_track/create_customer',
        method: 'post',
        data: data
      })
    }
        //跟进
        export function customer(data){
          return request({
            url: '/platform/customer_track/customer_follow_up',
            method: 'post',
            data: data
          })
        }
           //分配
           export function assignLead(data){
            return request({
              url: '/platform/customer_track/assign_lead',
              method: 'post',
              data: data
            })
          }
           //更改标签
           export function changeTag(data){
            return request({
              url: '/platform/customer_track/change_tag',
              method: 'post',
              data: data
            })
          }
