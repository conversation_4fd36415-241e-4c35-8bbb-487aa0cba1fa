import request from '@/utils/request'//动态样式
//分页查询样式列表
export function page(query){
    return request({
      url: '/platform/styleDynamic/page',
      method: 'get',
      params: query
    })
  }
//添加
  export function save(data){
    return request({
      url: '/platform/styleDynamic/save',
      method: 'post',
      data: data
    })
  }

  // 删除表数据
  export function delTable(tableId) {
    return request({
      url: '/platform/styleDynamic/remove?id=' + tableId,
      method: 'get'
    })
  }

  //查询
  export function info(query){
    return request({
      url: '/platform/templateDynamic/info/'+query,
      method: 'get',
    })
  }
