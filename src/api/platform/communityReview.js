import request from '@/utils/request'//社区评论
export function list(query){//动态
    return request({
      url: '/platform/community/list',
      method: 'get',
      params: query
    })
  }
     // 删除动态
     export function delTable(query) {
      return request({
        url: '/platform/community/post?postIds=' + query,
        method: 'delete',
        // params: {
        //   postIds: query
        // }
      })
    }
         // 话题下拉查询
         export function options(query) {
          return request({
            url: '/platform/topic/options',
            method: 'get',
          })
        }

