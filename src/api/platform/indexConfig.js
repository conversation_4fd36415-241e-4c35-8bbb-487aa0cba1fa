import request from '@/utils/request'//主页配置
export function merchants(data){//查询当前租户会所配置信息
    return request({
      url: '/platform/club/info/merchants',
      method: 'get',
    })
  }
  //新增会所基本信息
export function save(data){
    return request({
      url: '/platform/club/save',
      method: 'put',
      data: data
    })
  }
    //修改会所基本信息
export function update(data){
    return request({
      url: '/platform/club/update',
      method: 'post',
      data: data
    })
  }
    //查询查询会所信息
export function info(query){
    return request({
      url: '/platform/club/info',
      method: 'get',
    })
  }
