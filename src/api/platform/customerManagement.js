import request from '@/utils/request'//客服管理

export function page(query) {//查询
  return request({
    url: '/cs/agent/page',
    method: 'get',
    params: query
  })
}
//添加
export function save(data) {
  return request({
    url: '/cs/agent/create',
    method: 'post',
    data: data
  })
}
// 删除
export function delTable(agentId) {
  return request({
    url: '/cs/agent/delete/' + agentId,
    method: 'delete'
  })
}
export function queryUserCustomer() {//查询微信用户（选择下拉框）
  return request({
    url: '/platform/customerService/queryUserCustomer',
    method: 'get',
  })
}
