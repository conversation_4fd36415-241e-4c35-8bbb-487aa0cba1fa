import request from '@/utils/request'//签到
export function page(query){
    return request({
      url: '/platform/contract_gift/page',
      method: 'get',
      params: query
    })
  }
//添加
  export function save(data){
    return request({
      url: '/platform/contract_gift/save',
      method: 'put',
      data: data
    })
  }
  //查询信息
export function info(query){
    return request({
      url: '/platform/contract_gift/info/'+query,
      method: 'get',
    })
  }
    //修改
    export function update(data){
        return request({
          url: '/platform/contract_gift/update',
          method: 'post',
          data: data
        })
      }
