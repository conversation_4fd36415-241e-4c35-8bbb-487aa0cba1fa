import request from '@/utils/request'


// 新增员工职位
export function create(data) {
    return request({
        url: '/platform/employee_post/save',
        method: 'post',
        data: data
    })
}

// 修改员工职位
export function update(data) {
    return request({
        url: '/platform/employee_post/update',
        method: 'post',
        data: data
    })
}

// 获取职位详情
export function detail(id){
    return request({
        url: `/platform/employee_post/detail/${id}`,
        method: "get"
    });
}

// 查询员工职位列表
export function page(params) {
    return request({
        url: `/platform/employee_post/page`,
        method: 'get',
        params: params
    })
}