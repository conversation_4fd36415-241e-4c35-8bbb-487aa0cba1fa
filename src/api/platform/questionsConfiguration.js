import request from '@/utils/request'//节点
export function page(query){
    return request({
      url: '/platform/question/page',
      method: 'get',
      params: query
    })
  }
//添加
  export function save(data){
    return request({
      url: '/platform/question/save',
      method: 'put',
      data: data
    })
  }
// 删除
export function delTable(tableId) {
    return request({
      url: '/platform/question/' + tableId,
      method: 'delete'
    })
  }
     //更改问题显示状态
     export function homepage(query){
      return request({
        url: `/platform/question/change/${query.state}/${query.questionId}`,
        method: 'put'
      })
    }
