import request from '@/utils/request'

// 分页查询房间反馈列表
export function queryPage(query) {
  return request({
    url: '/platform/room_feedback/page',
    method: 'get',
    params: query
  })
}

// 查询房间反馈回复列表
export function queryReplyList(feedbackId) {
  return request({
    url: `/platform/room_feedback/reply_list/${feedbackId}`,
    method: 'get'
  })
}

// 创建房间反馈
export function createRoomFeedback(data) {
  return request({
    url: '/platform/room_feedback/create',
    method: 'post',
    data: data
  })
}