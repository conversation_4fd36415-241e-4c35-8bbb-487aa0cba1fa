import request from '@/utils/request'//会所设施配置
  //添加
  export function saves(data){
    return request({
      url: '/platform/meal/saveOrUpdate',
      method: 'post',
      data: data
    })
  }
  //查询
export function infos(query){
    return request({
      url: '/platform/meal/info/merchants',
      method: 'get',
    })
  }

    //分页查询月子膳食列表
export function mealPage(query){
  return request({
    url: '/platform/meal/page',
    method: 'get',
    params: query
  })
}
//获取菜品详情
export function getItemDetail(id) {
  return request({
      url: '/platform/meal/itemDetail?itemId='+id,
      method: 'get'
  })
}
// 删除菜品
export function deleteItem(itemId) {
  return request({
    url: '/platform/meal/deleteItem?itemId='+itemId,
    method: 'get'
  })
}
  //添加修改
  export function saveItem(data){
    return request({
      url: '/platform/meal/saveItem',
      method: 'post',
      data: data
    })
  }
  // 是否推荐
export function isRec(itemId) {
  return request({
    url: '/platform/meal/isRec?itemId='+itemId,
    method: 'get'
  })
}
  // 是否启用膳食图片
  export function isMealTags(itemId) {
    return request({
      url: '/platform/meal/isMealTags?mealId='+itemId,
      method: 'get'
    })
  }
