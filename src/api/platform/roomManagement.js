import request from '@/utils/request'//房间管理
// 查询
export function list(query) {
  return request({
    url: '/platform/room/list',
    method: 'get',
    params: query
  })
}
//新增楼层
export function addLevel(data) {
  return request({
    url: '/platform/room/add_level',
    method: 'put',
    data: data
  })
}
// 获取楼层列表
export function listLevel(query) {
  return request({
    url: '/platform/room/list_level',
    method: 'get',
    params: query
  })
}
// 获取楼层房间列表
export function listLevelRoom(query) {
  return request({
    url: '/platform/room/list_level_room',
    method: 'get',
    params: query
  })
}
//新增房间
export function addRoom(data) {
  return request({
    url: '/platform/room/add_room',
    method: 'put',
    data: data
  })
}

//房型列表
export function houseList(query) {
  return request({
    url: '/platform/suite/page',
    method: 'get',
    params: query
  })
}
// 获取房型
export function options(query) {
  return request({
    url: '/platform/suite/options',
    method: 'get',
    params: query
  })
}
//房型列表
export function customer(query) {
  return request({
    url: '/platform/customer/options',
    method: 'get',
    params: query
  })
}

//修改房间&办理入住
export function saveRoom(data) {
  return request({
    url: '/platform/room',
    method: 'post',
    data: data
  })
}
//护理人员
export function staffPage(query) {
  return request({
    url: '/platform/staff/page',
    method: 'get',
    params: query
  })
}

//查询房间待办
export function todo(roomId) {
  return request({
    url: '/platform/room/todo/' + roomId,
    method: 'get',
  })
}

//回复投诉
export function reply(data) {
  return request({
    url: '/platform/room/reply',
    method: 'put',
    data: data
  })
}

//分页查询房间客户入住记录
export function checkoutPage(query) {
  return request({
    url: '/platform/room/checkout_page',
    method: 'get',
    params: query
  })
}

//查询房间退房详情信息
export function checkoutPages(checkoutId) {
  return request({
    url: '/platform/room/checkout_page/' + checkoutId,
    method: 'get',
  })
}

//查询房间信息
export function roomInfo(roomId) {
  return request({
    url: '/platform/room/info/' + roomId,
    method: 'get',
  })
}

//查询楼层下拉选项数据
export function roomOptions(roomId) {
  return request({
    url: '/platform/room/options',
    method: 'get',
  })
}

//退房
export function checkOut(roomId) {
  return request({
    url: `/platform/room/out/${roomId}`,
    method: 'post'
  })
}