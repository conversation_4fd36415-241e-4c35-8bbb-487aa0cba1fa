import request from '@/utils/request'//节点
export function page(query){
    return request({
      url: '/platform/task_node/page',
      method: 'get',
      params: query
    })
  }
//添加
  export function save(data){
    return request({
      url: '/platform/task_node/save',
      method: 'post',
      data: data
    })
  }
// 删除
export function delTable(tableId) {
    return request({
      url: '/platform/task_node/' + tableId,
      method: 'delete'
    })
  }
    //修改
    export function update(data){
      return request({
        url: '/platform/task_node/update',
        method: 'put',
        data: data
      })
    }
  export function getNodeOptions(query){
    return request({
      url: '/platform/task_node/options',
      method: 'get',
      params: query
    })
  }
  //更改节点精选状态
  export function changeFeatured(data){
    return request({
      url: '/platform/task_node/changeFeatured',
      method: 'post',
      data: data
    })
  }
