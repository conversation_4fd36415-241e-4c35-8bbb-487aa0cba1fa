import request from '@/utils/request'//话题
//列表
export function page(query){
    return request({
      url: '/platform/topic/page',
      method: 'get',
      params: query
    })
  }
//添加
  export function save(data){
    return request({
      url: '/platform/topic/save',
      method: 'put',
      data: data
    })
  }
  
  // 删除表数据
  export function delTable(tableId) {
    return request({
      url: '/platform/topic/' + tableId,
      method: 'delete'
    })
  }
  
  //查询
  export function info(query){
    return request({
      url: '/platform/topic/info/'+query,
      method: 'get',
    })
  }
  //上下架
  export function online(query){
    return request({
      url: `/platform/topic/online/${query.topicId}/${query.status}`,
      method: 'get'
    })
  }
  
  //修改
  export function update(data){
    return request({
      url: '/platform/topic/update',
      method: 'post',
      data: data
    })
  }
