import request from '@/utils/request'

// 查询入驻申请列表
export function page(params) {
    return request({
        url: `/platform/merchant/page`,
        method: 'get',
        params: params
    })
}

// 查询入驻申请详情
export function detail(id) {
    return request({
        url: `/platform/merchant/detail/` + id,
        method: 'get',
    })
}

// 审核商家入驻申请
export function audit(data) {
    return request({
        url: `/platform/merchant/audit`,
        method: 'post',
        data: data
    })
}