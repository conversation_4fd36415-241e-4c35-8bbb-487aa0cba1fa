import request from '@/utils/request'

/**
 * 查询员工已经服务的客户下拉列表
 * @param {*} staffUserId  员工id
 * @returns  返回员工已经服务的客户下拉列表
 */
export function getStaffCustomerOptions(staffUserId) {
    return request({
        url: `/platform/room/customer_options?staffUserId=${staffUserId}`,
        method: 'get'
    })
}

/**
 * 查询客户服务人员下拉列表
 * @param {*} customerId 客户id
 * @returns 返回客户服务人员下拉列表
 */
export function getCustomerServicePersonnelOptions(customerId) {
    return request({
        url: `/platform/room/staff_options?customerId=${customerId}`,
        method: 'get'
    })
}

// 回复房间反馈
export function replyRoomFeedback(data) {
    return request({
        url: '/platform/room/reply',
        method: 'post',
        data: data
    })
}


/**
 * 查询房间列表
 */
export function getList() {
    return request({
        url: `/platform/room/options`,
        method: 'get'
    })
}

/**
 * 查询房间用户列表
 */
export function getRoomUserList(roomId) {
    return request({
        url: `/platform/room/user_options?roomId=${roomId}`,
        method: 'get'
    })
}