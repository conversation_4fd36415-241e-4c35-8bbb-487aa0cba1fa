import request from '@/utils/request'//查询商家总体评价列表
export function page(query){
  console.log(query);
    return request({
      url: '/platform/review/list',
      method: 'get',
      params: query
    })
  }
//回复评价
  export function replies(data){
    return request({
      url: '/platform/review/replies',
      method: 'post',
      data: data
    })
  }
      // 删除评价
      export function delTable(tableId) {
        return request({
          url: '/platform/review/remove/' + tableId,
          method: 'delete'
        })
      }
         // 删除回复
         export function remove(tableId) {
          return request({
            url: '/platform/review/remove_replies/' + tableId,
            method: 'delete'
          })
        }