import request from '@/utils/request'//膳食配送
export function page(query){
    return request({
      url: '/platform/product_delivery/page',
      method: 'get',
      params: query
    })
  }
//添加
  export function save(data){
    return request({
      url: '/platform/product_delivery/save',
      method: 'put',
      data: data
    })
  }
    // 删除表数据
    export function delTable(tableId) {
        return request({
          url: '/platform/product_delivery/' + tableId,
          method: 'delete'
        })
      }
  //查询信息
export function info(query){
    return request({
      url: '/platform/product_delivery/info/'+query,
      method: 'get',
    })
  }
    //修改
    export function update(data){
        return request({
          url: '/platform/product_delivery/update',
          method: 'post',
          data: data
        })
      }