import request from '@/utils/request'
// 查询租户套餐列表
export function queryList(query) {
    return request({
      url: '/system/tenant/package/list',
      method: 'get',
      params: query
    })
}

//新增租户套餐
export function createPackage(data) {
    return request({
      url: '/system/tenant/package',
      method: 'post',
      data: data
    })
}

// 查询租户套餐详细信息
export function getById(id) {
    return request({
      url: `/system/tenant/package/${id}`,
      method: 'get'
    })
}

//修改租户套餐
export function updatePackage(data) {
    return request({
      url: '/system/tenant/package',
      method: 'put',
      data: data
    })
}

// 删除租户套餐
export function remove(id) {
    return request({
      url: `/system/tenant/package/${id}`,
      method: 'delete'
    })
}

// 改变租户套餐状态
export function changePackageStatus(data) {
    return request({
      url: '/system/tenant/package/changeStatus',
      method: 'put',
      data: data
    })
}