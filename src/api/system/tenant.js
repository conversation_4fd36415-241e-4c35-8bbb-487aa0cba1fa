import request from '@/utils/request'

// 查询租户列表
export function queryList(query) {
    return request({
      url: '/system/tenant/list',
      method: 'get',
      params: query
    })
}

// 查询租户下拉选项列表
export function queryOptions() {
  return request({
    url: '/system/tenant/options',
    method: 'get'
  })
}

// 查询租户详细信息
export function getById(id) {
    return request({
      url: `/system/tenant/${id}`,
      method: 'get'
    })
}

/**
 * 同步租户套餐
 * @param {同步参数} params 
 * @returns 
 */
export function syncTenantPackage(params) {
    return request({
      url: `/system/tenant/syncTenantPackage`,
      method: 'get',
      params: params
    })
}

// 查询租户套餐下拉列表
export function querySelectOptions() {
    return request({
      url: '/system/tenant/package/selectList',
      method: 'get'
    })
}

// 改变租户状态
export function changeTenantStatus(data) {
    return request({
      url: '/system/tenant/changeStatus',
      method: 'put',
      data: data
    })
}

//新增租户
export function createTenant(data) {
    return request({
      url: '/system/tenant',
      headers: {
        isEncrypt: true,
      },
      method: 'post',
      data: data
    })
}

//修改租户
export function updateTenant(data) {
    return request({
      url: '/system/tenant',
      method: 'put',
      data: data
    })
}

/**
 * 动态设置租户
 * @param {租户id} tenantId 
 * @returns 
 */
export function dynamicTenant(tenantId) {
  return request({
    url: `/system/tenant/dynamic/${tenantId}`,
    method: 'get',
  })
}

/**
 * 清除租户
 * @returns 
 */
export function clearTenant() {
  return request({
    url: `/system/tenant/dynamic/clear`,
    method: 'get',
  })
}