import request from '@/utils/request'

/**
 * 文档管理API
 * 基于知识库和文档管理API对接文档实现
 */

// 分页查询文档列表
export function getDocumentList(query) {
  return request({
    url: '/ai/documents/list',
    method: 'get',
    params: query
  }).then(response => {
    // 适配数据结构：统一返回 {rows, total} 格式
    if (response.data && response.data.records) {
      return {
        rows: response.data.records,
        total: response.data.total
      }
    } else if (response.data && response.data.rows) {
      return {
        rows: response.data.rows,
        total: response.data.total
      }
    }
    return response
  })
}

// 查询所有文档列表（不分页）
export function getAllDocuments(query) {
  return request({
    url: '/ai/documents/all',
    method: 'get',
    params: query
  })
}

// 根据知识库ID查询文档列表
export function getDocumentsByKnowledgeId(knowledgeId) {
  return request({
    url: `/ai/documents/knowledge-base/${knowledgeId}`,
    method: 'get'
  }).then(response => {
    // 根据实际API返回，这个接口返回分页格式 {data: {records, total}}
    // 适配数据结构：统一返回数组格式
    if (response.data && response.data.records) {
      return response.data.records
    } else if (response.data && response.data.rows) {
      return response.data.rows
    } else if (response.data && Array.isArray(response.data)) {
      return response.data
    }
    return response.data || []
  })
}

// 获取文档详细信息
export function getDocumentDetail(documentId) {
  return request({
    url: `/ai/documents/${documentId}`,
    method: 'get'
  }).then(response => {
    // 适配数据结构：返回data字段的内容
    return response.data || response
  })
}

// 获取文档内容预览
export function getDocumentPreview(documentId, maxLength = 1000) {
  return request({
    url: `/ai/documents/${documentId}/preview`,
    method: 'get',
    params: { maxLength }
  }).then(response => {
    // 适配数据结构：返回data字段的内容（预览内容是字符串）
    return response.data || response
  })
}

// 新增文档
export function createDocument(data) {
  return request({
    url: '/ai/documents',
    method: 'post',
    data: data
  })
}

// 修改文档
export function updateDocument(data) {
  return request({
    url: '/ai/documents',
    method: 'put',
    data: data
  })
}

// 删除文档
export function deleteDocuments(documentIds) {
  // 支持单个ID或ID数组
  const ids = Array.isArray(documentIds) ? documentIds.join(',') : documentIds
  return request({
    url: `/ai/documents/${ids}`,
    method: 'delete'
  })
}

// 上传单个文档
export function uploadDocument(knowledgeId, file) {
  const formData = new FormData()
  formData.append('knowledgeId', knowledgeId)
  formData.append('file', file)
  
  return request({
    url: '/ai/documents/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 批量上传文档
export function batchUploadDocuments(knowledgeId, files) {
  const formData = new FormData()
  formData.append('knowledgeId', knowledgeId)
  
  // 添加多个文件
  files.forEach(file => {
    formData.append('files', file)
  })
  
  return request({
    url: '/ai/documents/batch-upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 重新处理文档
export function reprocessDocument(documentId) {
  return request({
    url: `/ai/documents/${documentId}/reprocess`,
    method: 'put'
  })
}

// 获取支持的文件类型
export function getSupportedFileTypes() {
  return request({
    url: '/ai/documents/supported-file-types',
    method: 'get'
  }).then(response => {
    // 适配数据结构：返回data字段的内容（数组）
    return response.data || response
  })
}

// 检查文件类型是否支持
export function checkFileTypeSupported(fileName) {
  return request({
    url: '/ai/documents/check-file-type',
    method: 'get',
    params: { fileName }
  }).then(response => {
    // 适配数据结构：返回data字段的内容（布尔值）
    return response.data !== undefined ? response.data : response
  })
}

/**
 * 文档查询参数构建器
 */
export function buildDocumentQuery({
  pageNum = 1,
  pageSize = 10,
  knowledgeId = '',
  documentName = '',
  fileType = '',
  processStatus = '',
  vectorStatus = '',
  status = '',
  keyword = '',
  orderByColumn = '',
  isAsc = ''
} = {}) {
  const query = {}
  
  // 分页参数
  if (pageNum) query.pageNum = pageNum
  if (pageSize) query.pageSize = pageSize
  
  // 筛选参数
  if (knowledgeId) query.knowledgeId = knowledgeId
  if (documentName) query.documentName = documentName
  if (fileType) query.fileType = fileType
  if (processStatus) query.processStatus = processStatus
  if (vectorStatus) query.vectorStatus = vectorStatus
  if (status) query.status = status
  if (keyword) query.keyword = keyword
  
  // 排序参数
  if (orderByColumn) query.orderByColumn = orderByColumn
  if (isAsc) query.isAsc = isAsc
  
  return query
}

/**
 * 文档数据验证器
 */
export function validateDocumentData(data) {
  const errors = []
  
  // 必填字段验证
  if (!data.knowledgeId) {
    errors.push('知识库ID不能为空')
  }
  
  if (!data.documentName || data.documentName.trim() === '') {
    errors.push('文档名称不能为空')
  }
  
  // 长度验证
  if (data.documentName && data.documentName.length > 200) {
    errors.push('文档名称长度不能超过200个字符')
  }
  
  if (data.summary && data.summary.length > 1000) {
    errors.push('文档摘要长度不能超过1000个字符')
  }
  
  if (data.remark && data.remark.length > 500) {
    errors.push('备注长度不能超过500个字符')
  }
  
  // 状态验证
  if (data.status && !['0', '1'].includes(data.status)) {
    errors.push('文档状态必须为0（正常）或1（停用）')
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  }
}

/**
 * 文件类型验证器
 */
export function validateFileType(file, supportedTypes = []) {
  if (!file) {
    return { isValid: false, error: '文件不能为空' }
  }
  
  const fileName = file.name
  const fileExtension = fileName.split('.').pop().toLowerCase()
  
  if (supportedTypes.length > 0 && !supportedTypes.includes(fileExtension)) {
    return { 
      isValid: false, 
      error: `不支持的文件类型：${fileExtension}。支持的类型：${supportedTypes.join(', ')}` 
    }
  }
  
  return { isValid: true, error: null }
}

/**
 * 文档状态枚举
 */
export const DOCUMENT_STATUS = {
  NORMAL: '0',
  DISABLED: '1'
}

/**
 * 文档处理状态枚举
 */
export const PROCESS_STATUS = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED'
}

/**
 * 向量化状态枚举
 */
export const VECTOR_STATUS = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED'
}

/**
 * 状态显示文本映射
 */
export const DOCUMENT_STATUS_TEXT_MAP = {
  '0': '正常',
  '1': '停用'
}

export const PROCESS_STATUS_TEXT_MAP = {
  'PENDING': '待处理',
  'PROCESSING': '处理中',
  'SUCCESS': '处理成功',
  'FAILED': '处理失败'
}

export const VECTOR_STATUS_TEXT_MAP = {
  'PENDING': '待向量化',
  'PROCESSING': '向量化中',
  'SUCCESS': '向量化成功',
  'FAILED': '向量化失败'
}

/**
 * 默认支持的文件类型
 */
export const DEFAULT_SUPPORTED_FILE_TYPES = [
  'pdf', 'doc', 'docx', 'txt', 'md', 'html'
]
