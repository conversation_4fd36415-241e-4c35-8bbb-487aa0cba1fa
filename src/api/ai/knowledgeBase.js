import request from '@/utils/request'

/**
 * 知识库管理API
 * 基于知识库和文档管理API对接文档实现
 */

// 分页查询知识库列表
export function getKnowledgeBaseList(query) {
  return request({
    url: '/ai/knowledge-bases/list',
    method: 'get',
    params: query
  }).then(response => {
    // 适配数据结构：统一返回 {rows, total} 格式
    if (response.data && response.data.records) {
      return {
        rows: response.data.records,
        total: response.data.total
      }
    } else if (response.data && response.data.rows) {
      return {
        rows: response.data.rows,
        total: response.data.total
      }
    }
    return response
  })
}

// 查询所有知识库列表（不分页）
export function getAllKnowledgeBases(query) {
  return request({
    url: '/ai/knowledge-bases/all',
    method: 'get',
    params: query
  }).then(response => {
    // 适配数据结构：如果是分页格式，返回records；否则直接返回data
    if (response.data && response.data.records) {
      return response.data.records
    } else if (response.data && response.data.rows) {
      return response.data.rows
    }
    return response.data || response
  })
}

// 获取当前租户的知识库列表
export function getCurrentTenantKnowledgeBases() {
  return request({
    url: '/ai/knowledge-bases/current-tenant',
    method: 'get'
  }).then(response => {
    // 适配数据结构：如果是分页格式，返回records；否则直接返回data
    if (response.data && response.data.records) {
      return response.data.records
    } else if (response.data && response.data.rows) {
      return response.data.rows
    }
    return response.data || response
  })
}

// 获取知识库详细信息
export function getKnowledgeBaseDetail(knowledgeId) {
  return request({
    url: `/ai/knowledge-bases/${knowledgeId}`,
    method: 'get'
  }).then(response => {
    // 适配数据结构：返回data字段的内容
    return response.data || response
  })
}

// 新增知识库
export function createKnowledgeBase(data) {
  return request({
    url: '/ai/knowledge-bases',
    method: 'post',
    data: data
  })
}

// 修改知识库
export function updateKnowledgeBase(data) {
  return request({
    url: '/ai/knowledge-bases',
    method: 'put',
    data: data
  })
}

// 删除知识库
export function deleteKnowledgeBases(knowledgeIds) {
  // 支持单个ID或ID数组
  const ids = Array.isArray(knowledgeIds) ? knowledgeIds.join(',') : knowledgeIds
  return request({
    url: `/ai/knowledge-bases/${ids}`,
    method: 'delete'
  })
}

// 检查知识库名称是否唯一
export function checkKnowledgeNameUnique(knowledgeName, knowledgeId = null) {
  const params = { knowledgeName }
  if (knowledgeId) {
    params.knowledgeId = knowledgeId
  }
  return request({
    url: '/ai/knowledge-bases/check-name',
    method: 'get',
    params: params
  })
}

// 刷新知识库统计信息
export function refreshStatistics(knowledgeId) {
  return request({
    url: `/ai/knowledge-bases/${knowledgeId}/refresh-statistics`,
    method: 'put'
  })
}

/**
 * 知识库查询参数构建器
 * 用于构建标准化的查询参数
 */
export function buildKnowledgeBaseQuery({
  pageNum = 1,
  pageSize = 10,
  knowledgeName = '',
  status = '',
  knowledgeType = '',
  keyword = '',
  orderByColumn = '',
  isAsc = ''
} = {}) {
  const query = {}
  
  // 分页参数
  if (pageNum) query.pageNum = pageNum
  if (pageSize) query.pageSize = pageSize
  
  // 筛选参数
  if (knowledgeName) query.knowledgeName = knowledgeName
  if (status) query.status = status
  if (knowledgeType) query.knowledgeType = knowledgeType
  if (keyword) query.keyword = keyword
  
  // 排序参数
  if (orderByColumn) query.orderByColumn = orderByColumn
  if (isAsc) query.isAsc = isAsc
  
  return query
}

/**
 * 知识库数据验证器
 * 用于前端数据验证
 */
export function validateKnowledgeBaseData(data) {
  const errors = []
  
  // 必填字段验证
  if (!data.knowledgeName || data.knowledgeName.trim() === '') {
    errors.push('知识库名称不能为空')
  }
  
  // 长度验证
  if (data.knowledgeName && data.knowledgeName.length > 100) {
    errors.push('知识库名称长度不能超过100个字符')
  }
  
  if (data.description && data.description.length > 500) {
    errors.push('知识库描述长度不能超过500个字符')
  }
  
  // 枚举值验证
  if (data.status && !['0', '1'].includes(data.status)) {
    errors.push('状态值必须为0（正常）或1（停用）')
  }
  
  if (data.knowledgeType && !['PUBLIC', 'PRIVATE'].includes(data.knowledgeType)) {
    errors.push('知识库类型必须为PUBLIC（公共）或PRIVATE（私有）')
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  }
}

/**
 * 知识库状态枚举
 */
export const KNOWLEDGE_BASE_STATUS = {
  NORMAL: '0',
  DISABLED: '1'
}

/**
 * 知识库类型枚举
 */
export const KNOWLEDGE_BASE_TYPE = {
  PUBLIC: 'PUBLIC',
  PRIVATE: 'PRIVATE'
}

/**
 * 状态和类型的显示文本映射
 */
export const STATUS_TEXT_MAP = {
  '0': '正常',
  '1': '停用'
}

export const TYPE_TEXT_MAP = {
  'PUBLIC': '公共知识库',
  'PRIVATE': '私有知识库'
}
