import { 
  createKnowledgeBase, 
  checkKnowledgeNameUnique, 
  getKnowledgeBaseDetail,
  getCurrentTenantKnowledgeBases,
  refreshStatistics
} from './knowledgeBase'
import { 
  uploadDocument, 
  batchUploadDocuments, 
  getDocumentDetail,
  getDocumentsByKnowledgeId,
  deleteDocuments,
  checkFileTypeSupported,
  getSupportedFileTypes,
  PROCESS_STATUS,
  VECTOR_STATUS
} from './document'

/**
 * 知识库业务逻辑封装
 * 提供高级业务功能，简化常用操作流程
 */

/**
 * 带验证的知识库创建
 * @param {Object} data 知识库数据
 * @returns {Promise} 创建结果
 */
export async function createKnowledgeBaseWithValidation(data) {
  try {
    // 1. 检查名称唯一性
    const isUnique = await checkKnowledgeNameUnique(data.knowledgeName)
    if (!isUnique) {
      throw new Error('知识库名称已存在，请使用其他名称')
    }

    // 2. 创建知识库
    const result = await createKnowledgeBase(data)
    
    // 3. 获取创建后的知识库列表，找到新创建的知识库
    const knowledgeBases = await getCurrentTenantKnowledgeBases()
    const createdKnowledgeBase = knowledgeBases.find(kb => kb.knowledgeName === data.knowledgeName)
    
    return {
      success: true,
      data: createdKnowledgeBase,
      message: '知识库创建成功'
    }
  } catch (error) {
    return {
      success: false,
      data: null,
      message: error.message || '知识库创建失败'
    }
  }
}

/**
 * 带状态监控的文档上传
 * @param {number} knowledgeId 知识库ID
 * @param {File} file 文件对象
 * @param {Function} onStatusChange 状态变化回调
 * @returns {Promise} 上传结果
 */
export async function uploadDocumentWithMonitoring(knowledgeId, file, onStatusChange = null) {
  try {
    // 1. 检查文件类型
    const isSupported = await checkFileTypeSupported(file.name)
    if (!isSupported) {
      throw new Error('不支持的文件类型')
    }

    // 2. 上传文档
    const uploadResult = await uploadDocument(knowledgeId, file)
    const documentId = uploadResult.documentId

    // 3. 监控处理状态
    if (onStatusChange) {
      onStatusChange({ status: 'uploading', message: '文件上传成功，开始处理...' })
    }

    const finalDocument = await monitorDocumentProcessing(documentId, onStatusChange)
    
    return {
      success: true,
      data: finalDocument,
      message: '文档上传并处理完成'
    }
  } catch (error) {
    return {
      success: false,
      data: null,
      message: error.message || '文档上传失败'
    }
  }
}

/**
 * 带进度的批量上传
 * @param {number} knowledgeId 知识库ID
 * @param {File[]} files 文件数组
 * @param {Function} onProgress 进度回调
 * @returns {Promise} 批量上传结果
 */
export async function batchUploadWithProgress(knowledgeId, files, onProgress = null) {
  try {
    // 1. 获取支持的文件类型
    const supportedTypes = await getSupportedFileTypes()

    // 2. 过滤支持的文件
    const validFiles = []
    const invalidFiles = []

    for (const file of files) {
      const fileExtension = file.name.split('.').pop().toLowerCase()
      if (supportedTypes.includes(fileExtension)) {
        validFiles.push(file)
      } else {
        invalidFiles.push(file)
      }
    }

    if (onProgress) {
      onProgress({
        phase: 'validation',
        validCount: validFiles.length,
        invalidCount: invalidFiles.length,
        invalidFiles: invalidFiles.map(f => f.name)
      })
    }

    if (validFiles.length === 0) {
      throw new Error('没有支持的文件可以上传')
    }

    // 3. 批量上传
    if (onProgress) {
      onProgress({ phase: 'uploading', message: `正在上传 ${validFiles.length} 个文件...` })
    }

    const uploadResult = await batchUploadDocuments(knowledgeId, validFiles)

    // 4. 上传完成，后端将异步处理文档
    if (onProgress) {
      onProgress({
        phase: 'completed',
        uploadedCount: uploadResult.length,
        totalCount: validFiles.length
      })
    }

    return {
      success: true,
      data: {
        uploaded: uploadResult,
        summary: {
          successCount: uploadResult.length,
          failureCount: 0,
          invalidCount: invalidFiles.length
        }
      },
      message: `文件上传完成：成功上传 ${uploadResult.length} 个文件，正在后台处理中...`
    }
  } catch (error) {
    return {
      success: false,
      data: null,
      message: error.message || '批量上传失败'
    }
  }
}

/**
 * 获取知识库概览信息
 * @param {number} knowledgeId 知识库ID
 * @returns {Promise} 概览信息
 */
export async function getKnowledgeBaseOverview(knowledgeId) {
  try {
    // 并行获取知识库信息和文档列表
    const [knowledgeBase, documents] = await Promise.all([
      getKnowledgeBaseDetail(knowledgeId),
      getDocumentsByKnowledgeId(knowledgeId)
    ])

    // 统计文档状态
    const statusStats = documents.reduce((stats, doc) => {
      stats[doc.processStatus] = (stats[doc.processStatus] || 0) + 1
      return stats
    }, {})

    // 统计向量化状态
    const vectorStats = documents.reduce((stats, doc) => {
      stats[doc.vectorStatus] = (stats[doc.vectorStatus] || 0) + 1
      return stats
    }, {})

    return {
      success: true,
      data: {
        knowledgeBase,
        documents,
        statistics: {
          totalDocuments: documents.length,
          processStatusStats: statusStats,
          vectorStatusStats: vectorStats,
          totalVectors: documents.reduce((sum, doc) => sum + (doc.vectorCount || 0), 0),
          totalChunks: documents.reduce((sum, doc) => sum + (doc.chunkCount || 0), 0)
        }
      }
    }
  } catch (error) {
    return {
      success: false,
      data: null,
      message: error.message || '获取知识库概览失败'
    }
  }
}

/**
 * 清理失败的文档
 * @param {number} knowledgeId 知识库ID
 * @returns {Promise} 清理结果
 */
export async function cleanupFailedDocuments(knowledgeId) {
  try {
    const documents = await getDocumentsByKnowledgeId(knowledgeId)
    const failedDocuments = documents.filter(doc =>
      doc.processStatus === PROCESS_STATUS.FAILED || doc.vectorStatus === VECTOR_STATUS.FAILED
    )

    if (failedDocuments.length === 0) {
      return {
        success: true,
        data: { cleanedCount: 0 },
        message: '没有失败的文档需要清理'
      }
    }

    const failedDocumentIds = failedDocuments.map(doc => doc.documentId)
    await deleteDocuments(failedDocumentIds)

    // 刷新知识库统计信息
    await refreshStatistics(knowledgeId)

    return {
      success: true,
      data: { 
        cleanedCount: failedDocuments.length,
        cleanedDocuments: failedDocuments.map(doc => ({
          id: doc.documentId,
          name: doc.documentName,
          processStatus: doc.processStatus,
          vectorStatus: doc.vectorStatus
        }))
      },
      message: `成功清理 ${failedDocuments.length} 个失败的文档`
    }
  } catch (error) {
    return {
      success: false,
      data: null,
      message: error.message || '清理失败文档时出错'
    }
  }
}


