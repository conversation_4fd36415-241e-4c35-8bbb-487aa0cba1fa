import request from '@/utils/request'

/**
 * AI模型计费策略管理API
 * 基于ModelPricingController实现
 */

// 分页查询计费策略列表
export function getModelPricingList(query) {
  return request({
    url: '/ai/pricing/page',
    method: 'get',
    params: query
  }).then(response => {
    // 适配数据结构：统一返回 {rows, total} 格式
    if (response.data && response.data.records) {
      return {
        rows: response.data.records,
        total: response.data.total
      }
    } else if (response.data && response.data.rows) {
      return {
        rows: response.data.rows,
        total: response.data.total
      }
    }
    return response
  })
}

// 获取所有有效计费策略
export function getAllValidPricing() {
  return request({
    url: '/ai/pricing/list',
    method: 'get'
  }).then(response => {
    return response.data || response
  })
}

// 获取计费策略详细信息
export function getModelPricingDetail(id) {
  return request({
    url: `/ai/pricing/${id}`,
    method: 'get'
  }).then(response => {
    // 适配数据结构：返回data字段的内容
    return response.data || response
  })
}

// 新增计费策略
export function createModelPricing(data) {
  return request({
    url: '/ai/pricing/create',
    method: 'post',
    data: data
  })
}

// 修改计费策略
export function updateModelPricing(data) {
  return request({
    url: '/ai/pricing/update',
    method: 'put',
    data: data
  })
}

// 删除计费策略
export function deleteModelPricing(id) {
  return request({
    url: `/ai/pricing/${id}`,
    method: 'delete'
  })
}

// 批量删除计费策略（前端实现，循环调用单个删除接口）
export function batchDeleteModelPricing(ids) {
  const deletePromises = ids.map(id => deleteModelPricing(id))
  return Promise.all(deletePromises)
}

// 获取支持的模型列表
export function getSupportedModels() {
  return request({
    url: '/ai/pricing/models',
    method: 'get'
  }).then(response => {
    // 适配数据结构：返回选项数组
    return response.data || response
  })
}

// 获取支持的提供商列表
export function getSupportedProviders() {
  return request({
    url: '/ai/pricing/providers',
    method: 'get'
  }).then(response => {
    // 适配数据结构：返回选项数组
    return response.data || response
  })
}

// 检查计费策略是否重复（前端简单实现，实际可根据业务需求调整）
export function checkPricingDuplicate(data) {
  // 由于后端没有提供此接口，前端暂时返回不重复
  return Promise.resolve(false)
}

/**
 * 货币单位枚举
 */
export const CURRENCY_OPTIONS = [
  { label: '人民币', value: 'CNY', symbol: '¥' },
  { label: '美元', value: 'USD', symbol: '$' }
]

/**
 * 状态枚举
 */
export const STATUS_OPTIONS = [
  { label: '正常', value: '0', type: 'success' },
  { label: '停用', value: '1', type: 'danger' }
]

/**
 * 格式化价格显示
 * @param {number} price - 价格
 * @param {string} currency - 货币单位
 * @returns {string} 格式化后的价格字符串
 */
export function formatPrice(price, currency = 'CNY') {
  if (price === null || price === undefined) return '-'
  
  const currencyInfo = CURRENCY_OPTIONS.find(item => item.value === currency)
  const symbol = currencyInfo ? currencyInfo.symbol : '¥'
  
  // 保留6位小数，去除末尾的0
  const formattedPrice = parseFloat(price).toFixed(6).replace(/\.?0+$/, '')
  
  return `${symbol}${formattedPrice}/千Token`
}

/**
 * 获取状态标签类型
 * @param {string} status - 状态值
 * @returns {string} Element UI标签类型
 */
export function getStatusType(status) {
  const statusInfo = STATUS_OPTIONS.find(item => item.value === status)
  return statusInfo ? statusInfo.type : 'info'
}

/**
 * 获取状态标签文本
 * @param {string} status - 状态值
 * @returns {string} 状态文本
 */
export function getStatusText(status) {
  const statusInfo = STATUS_OPTIONS.find(item => item.value === status)
  return statusInfo ? statusInfo.label : '未知'
}
