server {
    listen 80;
    listen [::]:80;

    # gzip config
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";
    resolver $NAMESERVERS ipv6=off;
    root /usr/share/nginx/html;
    include /etc/nginx/mime.types;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # location ^~/api/ {
    #     proxy_pass http://aier-admin-dev:8080;
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header X-Forwarded-Proto $scheme;
    #     rewrite ^/api/(.*)$ /$1 break;
    #     proxy_redirect default;
    #     client_max_body_size 1024m;
    # }

    # location ^~/ws/ {
    #     proxy_pass http://aier-admin-dev:8080;
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection upgrade;
    # }
}
